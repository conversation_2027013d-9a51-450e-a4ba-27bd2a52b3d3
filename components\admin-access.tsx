"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { Shield } from "lucide-react"

export function AdminAccess() {
  const { user, userWithRoles } = useAuth()
  const isAdmin = userWithRoles?.roles?.some(role => 
    role.role?.name === 'Owner' || role.role?.name === 'Admin'
  ) || false
  const router = useRouter()
  const [tapCount, setTapCount] = useState(0)
  const [showAdminButton, setShowAdminButton] = useState(false)
  
  // Reset tap count after 3 seconds of inactivity
  useEffect(() => {
    if (tapCount > 0) {
      const timer = setTimeout(() => {
        setTapCount(0)
      }, 3000)
      
      return () => clearTimeout(timer)
    }
  }, [tapCount])
  
  // Show admin button if user is an admin and has tapped 5 times
  useEffect(() => {
    if (tapCount >= 5) {
      setShowAdminButton(true)
      
      // Hide button after 5 seconds
      const timer = setTimeout(() => {
        setShowAdminButton(false)
        setTapCount(0)
      }, 5000)
      
      return () => clearTimeout(timer)
    }
  }, [tapCount])
  
  const handleTap = () => {
    setTapCount(prev => prev + 1)
  }
  
  const goToAdmin = () => {
    router.push("/admin/dashboard")
  }
  
  // Only render for admin users
  if (!user || !isAdmin) return null
  
  return (
    <div className="mt-8 relative">
      {/* Hidden tap area */}
      <div 
        onClick={handleTap}
        className="absolute top-0 right-0 w-16 h-16 -mt-16 -mr-4 cursor-default"
        aria-hidden="true"
      />
      
      {/* Admin button that appears after 5 taps */}
      {showAdminButton && (
        <button
          onClick={goToAdmin}
          className="w-full bg-gray-800 text-white py-3 px-4 rounded-md font-medium hover:bg-gray-700 transition-colors flex items-center justify-center gap-2 animate-fade-in"
        >
          <Shield size={18} />
          Access Admin Portal
        </button>
      )}
    </div>
  )
}

