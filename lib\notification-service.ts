// "use server"

// import { createClient } from '@/utils/supabase/client'

// export interface Notification {
//   id: string
//   user_id: string
//   type: "SMS" | "WhatsApp"
//   message: string
//   status: "Pending" | "Sent" | "Failed"
//   sent_at?: string
//   created_at?: string
// }

// export async function createNotification(userId: string, type: "SMS" | "WhatsApp", message: string) {
//   try {
//     if (!userId) {
//       // Cannot create notification without a user ID
//       return null
//     }

//     const supabase = await createClient()

//     // Create the notification record
//     const { data, error } = await supabase
//       .from("notifications")
//       .insert({
//         user_id: userId,
//         type,
//         message,
//         status: "Pending",
//       })
//       .select()
//       .single()

//     if (error) {
//       // Error creating notification
//       return null
//     }

//     if (!data || !data.id) {
//       // Notification created but no data returned
//       return null
//     }

//     // In a real app, you would integrate with SMS/WhatsApp APIs here
//     // For now, we'll simulate sending by updating the status
//     try {
//       const { error: updateError } = await supabase
//         .from("notifications")
//         .update({
//           status: "Sent",
//           sent_at: new Date().toISOString(),
//         })
//         .eq("id", data.id)

//       if (updateError) {
//         // Error updating notification status
//         // Continue anyway - the notification was created
//       }
//     } catch (updateError) {
//       // Exception updating notification status
//       // Continue anyway - the notification was created
//     }

//     return data as Notification
//   } catch (error) {
//     // Exception in createNotification
//     return null
//   }
// }

// export async function getUserNotifications(userId: string) {
//   const supabase = await createClient()
//   const { data, error } = await supabase
//     .from("notifications")
//     .select("*")
//     .eq("user_id", userId)
//     .order("created_at", { ascending: false })

//   if (error) {
//     console.error("Error fetching notifications:", error)
//     return []
//   }

//   return data as Notification[]
// }

// export async function sendOrderStatusNotification(orderId: string, status: string) {
//   console.log(`Sending ${status} notification for order: ${orderId}`);

//   try {
//     // Create Supabase client with error handling
//     let supabase;
//     try {
//       supabase = await createClient();
//     } catch (supabaseError) {
//       console.error("Failed to create Supabase client for notification:", supabaseError);
//       return null;
//     }

//     // Get order and user details
//     console.log("Fetching order details for notification");
//     let order;
//     try {
//       const { data, error: orderError } = await supabase
//         .from("orders")
//         .select(`*, user:users(*)`)
//         .eq("id", orderId)
//         .single();

//       if (orderError) {
//         console.error("Error fetching order for notification:", orderError);
//         return null;
//       }

//       if (!data) {
//         console.error("Order not found for notification:", orderId);
//         return null;
//       }

//       order = data;
//     } catch (orderFetchError) {
//       console.error("Exception fetching order for notification:", orderFetchError);
//       return null;
//     }

//     if (!order.user_id) {
//       console.error("Order has no user_id for notification:", orderId);
//       return null;
//     }

//     // Create notification message based on status
//     let message = ""
//     switch (status) {
//       case "Processing":
//         message = `Your order #${orderId.slice(0, 8)} has been received and is being processed.`
//         break
//       case "Out for Delivery":
//         message = `Your order #${orderId.slice(0, 8)} is out for delivery and will arrive soon.`
//         break
//       case "Delivered":
//         message = `Your order #${orderId.slice(0, 8)} has been delivered. Thank you for shopping with us!`
//         break
//       case "Cancelled":
//         message = `Your order #${orderId.slice(0, 8)} has been cancelled. Please contact support for assistance.`
//         break
//       default:
//         message = `Your order #${orderId.slice(0, 8)} status has been updated to: ${status}`
//     }

//     // Create and send notification
//     return await createNotification(order.user_id, "SMS", message)
//   } catch (error) {
//     // Exception in sendOrderStatusNotification
//     return null
//   }
// }
