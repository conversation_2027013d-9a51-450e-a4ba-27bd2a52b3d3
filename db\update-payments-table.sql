-- Add missing columns needed by the application
ALTER TABLE public.payments 
  ADD COLUMN IF NOT EXISTS payment_url TEXT,
  ADD COLUMN IF NOT EXISTS payment_gateway_id TEXT,
  ADD COLUMN IF NOT EXISTS reference TEXT,
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Make important columns NOT NULL
ALTER TABLE public.payments
  ALTER COLUMN order_id SET NOT NULL,
  ALTER COLUMN amount SET NOT NULL,
  ALTER COLUMN payment_method SET NOT NULL,
  ALTER COLUMN status SET NOT NULL;

-- Add foreign key constraint if not exists
ALTER TABLE public.payments
  ADD CONSTRAINT payments_order_id_fkey 
  FOREIGN KEY (order_id) 
  REFERENCES orders(id) ON DELETE CASCADE;

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS payments_order_id_idx ON payments(order_id);

-- Add RLS policies if not exists
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own payments
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'payments' AND policyname = 'payments_select_policy'
  ) THEN
    CREATE POLICY payments_select_policy ON payments
      FOR SELECT
      USING (
        order_id IN (
          SELECT id FROM orders WHERE user_id = auth.uid()
        )
      );
  END IF;
END
$$;