"use client"

import { useState } from "react"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { createClient } from '@/utils/supabase/client'
import Link from "next/link"

export default function DebugLoginPage() {
  const { user, isAuthenticated, loading, login, register } = useAuth()
  const [testMobile, setTestMobile] = useState("0721234567")
  const [testPassword, setTestPassword] = useState("password123")
  const [testShopName, setTestShopName] = useState("Test Spaza Shop")
  const [testAddress, setTestAddress] = useState("123 Test Street")
  const [testLoading, setTestLoading] = useState(false)
  const [testResult, setTestResult] = useState("")

  const handleTestLogin = async () => {
    setTestLoading(true)
    setTestResult("")

    try {
      const result = await login(testMobile, testPassword)
      setTestResult(JSON.stringify(result, null, 2))
    } catch (error) {
      setTestResult(`Login Error: ${error}`)
    } finally {
      setTestLoading(false)
    }
  }

  const handleTestRegister = async () => {
    setTestLoading(true)
    setTestResult("")

    try {
      const result = await register(testMobile, testPassword, testShopName, testAddress)
      setTestResult(JSON.stringify(result, null, 2))
    } catch (error) {
      setTestResult(`Register Error: ${error}`)
    } finally {
      setTestLoading(false)
    }
  }

  const checkSupabaseConnection = async () => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase.from('users').select('count').limit(1)
      
      if (error) {
        setTestResult(`Supabase Error: ${error.message}`)
      } else {
        setTestResult(`Supabase Connected: ${JSON.stringify(data)}`)
      }
    } catch (error) {
      setTestResult(`Connection Error: ${error}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Debug Login</h1>

        {/* Auth Status */}
        <div className="mb-6 p-4 bg-gray-100 rounded">
          <h2 className="font-bold mb-2">Authentication Status:</h2>
          <p className="text-sm">Loading: {loading ? "Yes" : "No"}</p>
          <p className="text-sm">Authenticated: {isAuthenticated ? "Yes" : "No"}</p>
          <p className="text-sm">User ID: {user?.id || "None"}</p>
          <p className="text-sm">Mobile: {user?.mobile || "None"}</p>
        </div>

        {/* Test Registration */}
        <div className="mb-6 p-4 bg-green-50 rounded">
          <h2 className="font-bold mb-2">Test Registration:</h2>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Mobile:</label>
            <input
              type="text"
              value={testMobile}
              onChange={(e) => setTestMobile(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Password:</label>
            <input
              type="password"
              value={testPassword}
              onChange={(e) => setTestPassword(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Shop Name:</label>
            <input
              type="text"
              value={testShopName}
              onChange={(e) => setTestShopName(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Address:</label>
            <input
              type="text"
              value={testAddress}
              onChange={(e) => setTestAddress(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <button
            onClick={handleTestRegister}
            disabled={testLoading}
            className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 disabled:bg-green-400 mb-2"
          >
            {testLoading ? "Registering..." : "Test Register"}
          </button>
        </div>

        {/* Test Login */}
        <div className="mb-6 p-4 bg-blue-50 rounded">
          <h2 className="font-bold mb-2">Test Login:</h2>
          <p className="text-sm text-gray-600 mb-3">Use the same mobile/password from registration above</p>
          <button
            onClick={handleTestLogin}
            disabled={testLoading}
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 disabled:bg-blue-400"
          >
            {testLoading ? "Testing..." : "Test Login"}
          </button>
        </div>

        {/* Test Supabase Connection */}
        <div className="mb-6">
          <button
            onClick={checkSupabaseConnection}
            className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700"
          >
            Test Supabase Connection
          </button>
        </div>

        {/* Test Result */}
        {testResult && (
          <div className="mb-6 p-4 bg-gray-100 rounded">
            <h2 className="font-bold mb-2">Test Result:</h2>
            <pre className="text-xs overflow-auto bg-gray-200 p-2 rounded">
              {testResult}
            </pre>
          </div>
        )}

        {/* Navigation */}
        <div className="space-y-2">
          <Link
            href="/login"
            className="block w-full bg-blue-600 text-white py-2 px-4 rounded text-center hover:bg-blue-700"
          >
            Go to Login Page
          </Link>
          <Link
            href="/auth"
            className="block w-full bg-green-600 text-white py-2 px-4 rounded text-center hover:bg-green-700"
          >
            Go to Register Page
          </Link>
          <Link
            href="/products"
            className="block w-full bg-purple-600 text-white py-2 px-4 rounded text-center hover:bg-purple-700"
          >
            Go to Products Page
          </Link>
          <Link
            href="/"
            className="block w-full bg-gray-600 text-white py-2 px-4 rounded text-center hover:bg-gray-700"
          >
            Go to Home Page
          </Link>
        </div>

        {/* Local Storage Info */}
        <div className="mt-6 p-4 bg-yellow-50 rounded">
          <h2 className="font-bold mb-2">Local Storage:</h2>
          <p className="text-sm">userLoggedIn: {localStorage.getItem('userLoggedIn') || "Not set"}</p>
          <p className="text-sm">currentUserId: {localStorage.getItem('currentUserId') || "Not set"}</p>
          <button
            onClick={() => {
              localStorage.clear()
              window.location.reload()
            }}
            className="mt-2 bg-red-600 text-white py-1 px-3 rounded text-sm hover:bg-red-700"
          >
            Clear Local Storage & Reload
          </button>
        </div>
      </div>
    </div>
  )
}
