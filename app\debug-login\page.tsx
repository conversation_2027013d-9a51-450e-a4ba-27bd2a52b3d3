"use client"

import { useState } from "react"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { createClient } from '@/utils/supabase/client'
import Link from "next/link"

export default function DebugLoginPage() {
  const { user, isAuthenticated, loading, login, register, signOut } = useAuth()
  const [testMobile, setTestMobile] = useState("0721234567")
  const [testPassword, setTestPassword] = useState("password123")
  const [testShopName, setTestShopName] = useState("Test Spaza Shop")
  const [testAddress, setTestAddress] = useState("123 Test Street")
  const [testLoading, setTestLoading] = useState(false)
  const [testResult, setTestResult] = useState("")

  const handleTestLogin = async () => {
    setTestLoading(true)
    setTestResult("")

    try {
      const result = await login(testMobile, testPassword)
      setTestResult(JSON.stringify(result, null, 2))
    } catch (error) {
      setTestResult(`Login Error: ${error}`)
    } finally {
      setTestLoading(false)
    }
  }

  const handleTestRegister = async () => {
    setTestLoading(true)
    setTestResult("")

    try {
      const result = await register(testMobile, testPassword, testShopName, testAddress)
      setTestResult(JSON.stringify(result, null, 2))
    } catch (error) {
      setTestResult(`Register Error: ${error}`)
    } finally {
      setTestLoading(false)
    }
  }

  const checkSupabaseConnection = async () => {
    setTestResult("Testing Supabase connection...")

    try {
      const supabase = createClient()

      // Test 1: Check if client was created
      if (!supabase) {
        setTestResult("❌ Failed to create Supabase client")
        return
      }

      // Test 2: Check environment variables
      const url = process.env.NEXT_PUBLIC_SUPABASE_URL
      const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

      if (!url || !key) {
        setTestResult(`❌ Missing environment variables:\nURL: ${url ? '✅' : '❌'}\nKey: ${key ? '✅' : '❌'}`)
        return
      }

      // Test 3: Try to get session (this doesn't require network)
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        setTestResult(`❌ Session Error: ${sessionError.message}`)
        return
      }

      // Test 4: Try a simple database query
      const { data, error } = await supabase.from('users').select('count').limit(1)

      if (error) {
        setTestResult(`❌ Database Error: ${error.message}\nCode: ${error.code}\nDetails: ${error.details}`)
      } else {
        setTestResult(`✅ Supabase Connected Successfully!\nURL: ${url}\nSession: ${sessionData.session ? 'Active' : 'None'}\nDatabase: Accessible`)
      }
    } catch (error) {
      setTestResult(`❌ Connection Error: ${error}\nThis might be a network issue or CORS problem.`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Debug Login</h1>

        {/* Auth Status */}
        <div className="mb-6 p-4 bg-gray-100 rounded">
          <h2 className="font-bold mb-2">Authentication Status:</h2>
          <p className="text-sm">Loading: {loading ? "Yes" : "No"}</p>
          <p className="text-sm">Authenticated: {isAuthenticated ? "Yes" : "No"}</p>
          <p className="text-sm">User ID: {user?.id || "None"}</p>
          <p className="text-sm">Mobile: {user?.mobile || "None"}</p>
          <p className="text-sm">Email: {user?.email || "None"}</p>
          <p className="text-sm">Is Admin: {user?.is_admin ? "Yes" : "No"}</p>

          <div className="mt-2 space-x-2">
            <button
              onClick={async () => {
                const supabase = createClient()
                const { data: { session } } = await supabase.auth.getSession()
                setTestResult(`Current Session: ${JSON.stringify(session, null, 2)}`)
              }}
              className="bg-blue-600 text-white py-1 px-3 rounded text-sm hover:bg-blue-700"
            >
              Check Session
            </button>

            <button
              onClick={async () => {
                try {
                  await signOut()
                  setTestResult("Signed out successfully")
                  setTimeout(() => window.location.reload(), 1000)
                } catch (error) {
                  setTestResult(`Sign out error: ${error}`)
                }
              }}
              className="bg-red-600 text-white py-1 px-3 rounded text-sm hover:bg-red-700"
            >
              Sign Out
            </button>

            <button
              onClick={() => {
                localStorage.clear()
                sessionStorage.clear()
                window.location.reload()
              }}
              className="bg-yellow-600 text-white py-1 px-3 rounded text-sm hover:bg-yellow-700"
            >
              Clear All & Reload
            </button>
          </div>
        </div>

        {/* Test Registration */}
        <div className="mb-6 p-4 bg-green-50 rounded">
          <h2 className="font-bold mb-2">Test Registration:</h2>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Mobile:</label>
            <input
              type="text"
              value={testMobile}
              onChange={(e) => setTestMobile(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Password:</label>
            <input
              type="password"
              value={testPassword}
              onChange={(e) => setTestPassword(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Shop Name:</label>
            <input
              type="text"
              value={testShopName}
              onChange={(e) => setTestShopName(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Address:</label>
            <input
              type="text"
              value={testAddress}
              onChange={(e) => setTestAddress(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          <button
            onClick={handleTestRegister}
            disabled={testLoading}
            className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 disabled:bg-green-400 mb-2"
          >
            {testLoading ? "Registering..." : "Test Register"}
          </button>
        </div>

        {/* Test Login */}
        <div className="mb-6 p-4 bg-blue-50 rounded">
          <h2 className="font-bold mb-2">Test Login:</h2>
          <p className="text-sm text-gray-600 mb-3">Use the same mobile/password from registration above</p>
          <button
            onClick={handleTestLogin}
            disabled={testLoading}
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 disabled:bg-blue-400"
          >
            {testLoading ? "Testing..." : "Test Login"}
          </button>
        </div>

        {/* Test Supabase Connection */}
        <div className="mb-6">
          <button
            onClick={checkSupabaseConnection}
            className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 mb-2"
          >
            Test Supabase Connection
          </button>

          <button
            onClick={async () => {
              setTestResult("Testing direct URL access...")
              try {
                const url = process.env.NEXT_PUBLIC_SUPABASE_URL
                const response = await fetch(`${url}/rest/v1/`, {
                  method: 'GET',
                  headers: {
                    'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
                    'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
                  }
                })

                if (response.ok) {
                  setTestResult(`✅ Direct URL access successful!\nStatus: ${response.status}\nURL: ${url}`)
                } else {
                  setTestResult(`❌ Direct URL access failed!\nStatus: ${response.status}\nURL: ${url}`)
                }
              } catch (error) {
                setTestResult(`❌ Direct URL test failed: ${error}`)
              }
            }}
            className="w-full bg-yellow-600 text-white py-2 rounded hover:bg-yellow-700"
          >
            Test Direct URL Access
          </button>
        </div>

        {/* Test Result */}
        {testResult && (
          <div className="mb-6 p-4 bg-gray-100 rounded">
            <h2 className="font-bold mb-2">Test Result:</h2>
            <pre className="text-xs overflow-auto bg-gray-200 p-2 rounded">
              {testResult}
            </pre>
          </div>
        )}

        {/* Navigation */}
        <div className="space-y-2">
          <Link
            href="/login"
            className="block w-full bg-blue-600 text-white py-2 px-4 rounded text-center hover:bg-blue-700"
          >
            Go to Login Page
          </Link>
          <Link
            href="/auth"
            className="block w-full bg-green-600 text-white py-2 px-4 rounded text-center hover:bg-green-700"
          >
            Go to Register Page
          </Link>
          <Link
            href="/products"
            className="block w-full bg-purple-600 text-white py-2 px-4 rounded text-center hover:bg-purple-700"
          >
            Go to Products Page
          </Link>
          <Link
            href="/"
            className="block w-full bg-gray-600 text-white py-2 px-4 rounded text-center hover:bg-gray-700"
          >
            Go to Home Page
          </Link>
        </div>

        {/* Environment Variables */}
        <div className="mt-6 p-4 bg-purple-50 rounded">
          <h2 className="font-bold mb-2">Environment Variables:</h2>
          <p className="text-xs break-all">URL: {process.env.NEXT_PUBLIC_SUPABASE_URL || "❌ Not set"}</p>
          <p className="text-xs break-all">Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? `${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20)}...` : "❌ Not set"}</p>
        </div>

        {/* Local Storage Info */}
        <div className="mt-6 p-4 bg-yellow-50 rounded">
          <h2 className="font-bold mb-2">Local Storage:</h2>
          <p className="text-sm">userLoggedIn: {localStorage.getItem('userLoggedIn') || "Not set"}</p>
          <p className="text-sm">currentUserId: {localStorage.getItem('currentUserId') || "Not set"}</p>
          <button
            onClick={() => {
              localStorage.clear()
              window.location.reload()
            }}
            className="mt-2 bg-red-600 text-white py-1 px-3 rounded text-sm hover:bg-red-700"
          >
            Clear Local Storage & Reload
          </button>
        </div>
      </div>
    </div>
  )
}
