"use client"

import { useState, useRef, memo } from "react"
import { useRouter } from "next/navigation"
import type { Product } from "@/lib/types/schema"
import { useAuth } from "@/contexts/auth-context"
import { useCart } from "@/contexts/cart-context"
import { useToast } from "@/components/toast-notification"
import { ProductIcon } from "@/lib/product-icons"
import { ProductQuickView } from "./product-quick-view"
import {
  ShoppingCart, Plus, Minus, Check, Eye,
  Heart, AlertCircle, Clock
} from "lucide-react"

interface ProductCardProps {
  product: Product
  showQuickView?: boolean
}

// Memoized ProductCard component to prevent unnecessary re-renders
function ProductCardComponent({ product, showQuickView = true }: ProductCardProps) {
  const [quantity, setQuantity] = useState(1)
  const [loading, setLoading] = useState(false)
  const [showQuickActions, setShowQuickActions] = useState(false)
  const [isQuickViewOpen, setIsQuickViewOpen] = useState(false)
  const [cardState, setCardState] = useState({
    success: false,
    highlight: false,
    isFavorite: false
  })
  const cardRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const { user } = useAuth()
  const { addToCart } = useCart()
  const { showToast } = useToast()

  // Calculate the total price for this product based on quantity
  const totalPrice = product.price * quantity

  const handleQuantityChange = (value: number) => {
    const newQuantity = Math.max(1, value)
    if (newQuantity !== quantity) {
      setQuantity(newQuantity)
      // Highlight the price when quantity changes
      setCardState(prev => ({ ...prev, highlight: true }))
      setTimeout(() => setCardState(prev => ({ ...prev, highlight: false })), 500)
    }
  }

  // Unified add to cart function for both quick add and regular add
  const handleAddToCart = async (qty = quantity) => {
    // Check if user is authenticated
    if (!user) {
      // Redirect to auth page if not logged in
      router.push("/auth")
      return
    }

    if (qty !== quantity) {
      setQuantity(qty) // Update the displayed quantity if using quick add
    }

    setLoading(true)

    try {
      // Use the cart context to add to cart
      const result = await addToCart(product.id, qty)

      // Check if the operation was successful
      if (result) {
        // Show success state and toast notification
        setCardState(prev => ({ ...prev, success: true }))
        setTimeout(() => setCardState(prev => ({ ...prev, success: false })), 2000) // Reset after 2 seconds
        showToast(`Added ${qty} x ${product.name} to cart`, "success")
      } else {
        // Handle the case where addToCart returned false
        if (process.env.NODE_ENV === 'development') {
          console.warn("Add to cart operation returned false")
        }

        // Check if we need to refresh the page due to data sync issues
        if (window.location.href.includes('/products')) {
          showToast("Product data may be out of sync. Refreshing page...", "info")

          // Give the user a moment to see the toast before refreshing
          setTimeout(() => {
            window.location.reload()
          }, 1500)
        } else {
          showToast("Could not add to cart. Please try again.", "error")
        }
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error("Error adding to cart:", error)
      }

      // Show error toast with more specific message if available
      let errorMessage = error instanceof Error ? error.message : "Failed to add to cart"

      // Check for specific error messages that indicate data sync issues
      if (errorMessage.includes("Product not found") ||
          errorMessage.includes("exists locally but not on server")) {

        errorMessage = "Product data may be out of sync. Refreshing page..."

        // Give the user a moment to see the toast before refreshing
        setTimeout(() => {
          window.location.reload()
        }, 1500)
      }

      showToast(errorMessage, "error")
    } finally {
      setLoading(false)
    }
  }

  const toggleFavorite = () => {
    setCardState(prev => ({ ...prev, isFavorite: !prev.isFavorite }))
    showToast(
      cardState.isFavorite ? "Removed from favorites" : "Added to favorites",
      "success"
    )
  }

  const handleQuickView = () => {
    setIsQuickViewOpen(true)
  }

  return (
    <div
      ref={cardRef}
      className="relative flex items-center border border-gray-200 rounded-md p-3 bg-white hover:shadow-md transition-all duration-200"
      onMouseEnter={() => setShowQuickActions(true)}
      onMouseLeave={() => setShowQuickActions(false)}
    >
      {/* Stock status indicator */}
      {!product.is_available && (
        <div className="absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1 rounded-bl-md rounded-tr-md z-10 flex items-center gap-1">
          <AlertCircle size={12} />
          Out of Stock
        </div>
      )}

      {/* Favorite button */}
      <button
        onClick={toggleFavorite}
        className={`absolute top-2 left-2 rounded-full p-1.5 transition-colors ${
          cardState.isFavorite ? 'bg-red-100 text-red-500' : 'bg-gray-100 text-gray-400 hover:text-red-500'
        }`}
        aria-label={cardState.isFavorite ? "Remove from favorites" : "Add to favorites"}
      >
        <Heart size={16} fill={cardState.isFavorite ? "currentColor" : "none"} />
      </button>

      <ProductIcon
        product={product}
        size={40}
        className="w-14 h-14 rounded mr-3 flex-shrink-0"
      />

      <div className="flex-grow">
        <h3 className="font-medium text-gray-900 text-sm">{product.name}</h3>
        <div className="flex items-center gap-2">
          <p className="text-gray-700 font-bold text-xs">R {product.price.toFixed(2)}</p>
          {/* Recently viewed indicator */}
          <span className="text-xs text-gray-500 flex items-center gap-0.5">
            <Clock size={10} />
            <span className="text-[10px]">Recently viewed</span>
          </span>
        </div>
        {product.description && <p className="text-gray-500 text-xs line-clamp-1">{product.description}</p>}

        {/* Quick quantity selector */}
        {showQuickActions && (
          <div className="flex flex-wrap gap-1 mt-1 animate-in fade-in duration-200">
            {[1, 2, 3, 5].map(qty => (
              <button
                key={qty}
                onClick={() => handleAddToCart(qty)}
                className="bg-blue-50 hover:bg-blue-100 text-blue-600 text-[10px] font-medium rounded-full px-2 py-0.5 min-w-[24px] min-h-[24px]"
                disabled={loading}
              >
                {qty}
              </button>
            ))}
            <button
              onClick={handleQuickView}
              className="bg-purple-50 hover:bg-purple-100 text-purple-600 text-[10px] font-medium rounded-full px-2 py-0.5 flex items-center gap-1"
              disabled={loading}
            >
              <Eye size={10} />
              View
            </button>
          </div>
        )}
      </div>

      <div className="flex flex-col items-end">
        <div className="flex flex-col items-center gap-1 mb-1">
          <div className="flex items-center border border-gray-300 rounded-full overflow-hidden">
            <button
              onClick={() => handleQuantityChange(quantity - 1)}
              className="bg-gray-100 p-1 flex items-center justify-center w-7 h-7"
              type="button"
            >
              <Minus size={12} />
            </button>
            <input
              type="number"
              min="1"
              value={quantity}
              onChange={(e) => handleQuantityChange(Number.parseInt(e.target.value) || 1)}
              className="w-8 p-0.5 text-center border-x border-gray-300 text-xs"
              style={{ fontSize: '12px' }} /* Prevents iOS zoom */
            />
            <button
              onClick={() => handleQuantityChange(quantity + 1)}
              className="bg-gray-100 p-1 flex items-center justify-center w-7 h-7"
              type="button"
            >
              <Plus size={12} />
            </button>
          </div>
          <div className={`text-xs font-medium transition-all duration-300 ${cardState.highlight ? 'text-green-600 scale-110' : 'text-blue-600'}`}>
            R {totalPrice.toFixed(2)}
          </div>
        </div>
        <button
          onClick={() => handleAddToCart()}
          disabled={loading || cardState.success || !product.is_available}
          className={`px-2 py-1.5 rounded-full text-xs transition-colors flex items-center justify-center gap-1 ${
            !product.is_available
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : cardState.success
                ? 'bg-green-500 text-white'
                : 'bg-green-600 text-white hover:bg-green-700 disabled:bg-green-400'
          }`}
        >
          {cardState.success ? (
            <>
              <Check size={12} />
              <span className="whitespace-nowrap">Added!</span>
            </>
          ) : (
            <>
              <ShoppingCart size={12} />
              <span className="whitespace-nowrap">{loading ? "Adding..." : "Add"}</span>
            </>
          )}
        </button>
      </div>

      {/* Quick view modal */}
      {isQuickViewOpen && (
        <ProductQuickView
          product={product}
          isOpen={isQuickViewOpen}
          onClose={() => setIsQuickViewOpen(false)}
        />
      )}
    </div>
  )
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const ProductCard = memo(ProductCardComponent)

