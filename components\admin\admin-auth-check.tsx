"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import Link from "next/link"
import { createClient } from "@/utils/supabase/client"

export function AdminAuthCheck({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth()
  const pathname = usePathname()
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [checkingAdmin, setCheckingAdmin] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Allow access to login and public admin routes without auth
  const isLoginPage = pathname === '/admin/login'

  useEffect(() => {
    const checkAdminStatus = async () => {
      // Skip admin check if on login page
      if (isLoginPage) {
        setCheckingAdmin(false)
        return
      }

      if (!user) {
        setCheckingAdmin(false)
        return
      }

      // First check if user has admin flag in context
      if (user.is_admin) {
        console.log("AdminAuthCheck: User is admin according to context")
        setIsAdmin(true)
        setCheckingAdmin(false)
        return
      }

      try {
        // Check admin status directly in Supabase via singleton client
        const supabase = createClient()

        // First check user metadata
        const { data: { user: authUser }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error("AdminAuthCheck: Error getting auth user", authError)
          setError("Error verifying admin status")
          setIsAdmin(false)
          setCheckingAdmin(false)
          return
        }

        if (authUser?.user_metadata?.is_admin === true) {
          console.log("AdminAuthCheck: User is admin according to metadata")
          setIsAdmin(true)
          setCheckingAdmin(false)
          return
        }

        // Check database if not in metadata
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("is_admin")
          .eq("id", user.id)
          .single()

        if (userError) {
          console.error("AdminAuthCheck: Error checking admin status in database", userError)
          setError("Error checking admin status")
          setIsAdmin(false)
        } else if (userData?.is_admin) {
          console.log("AdminAuthCheck: User is admin according to database")
          setIsAdmin(true)

          // Update user metadata for future checks
          try {
            await supabase.auth.updateUser({
              data: { is_admin: true }
            })
          } catch (updateErr) {
            console.error("AdminAuthCheck: Error updating admin metadata", updateErr)
          }
        } else {
          console.log("AdminAuthCheck: User is not admin according to database")
          setIsAdmin(false)
        }
      } catch (err) {
        console.error("AdminAuthCheck: Unexpected error", err)
        setError("An unexpected error occurred")
        setIsAdmin(false)
      } finally {
        setCheckingAdmin(false)
      }
    }

    if (!loading) {
      checkAdminStatus()
    }
  }, [user, loading, isLoginPage])

  useEffect(() => {
    if (!loading && !checkingAdmin && !isLoginPage) {
      if (!user) {
        router.push("/admin/login")
      }
    }
  }, [user, loading, checkingAdmin, router, isLoginPage])

  // If we're on the login page, just render children
  if (isLoginPage) {
    return <>{children}</>
  }

  if (loading || checkingAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md p-6 bg-white rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
          <p className="mb-6 text-gray-700">{error}</p>
          <div className="flex justify-center gap-4">
            <Link
              href="/admin/login"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Go to Admin Login
            </Link>
            <Link
              href="/"
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Go to Home
            </Link>
          </div>
        </div>
      </div>
    )
  }

  // Only ensure user is logged in; allow any authenticated user
  if (!user) {
    return null
  }

  return <>{children}</>
}
