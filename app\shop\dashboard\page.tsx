"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useShop } from "@/contexts/shop-context"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ShopSelector } from "@/components/shop-selector"
import { AuthCheck } from "@/components/auth-check"
import { 
  Store, 
  Package, 
  ShoppingCart, 
  Users, 
  BarChart, 
  AlertTriangle,
  TrendingUp,
  DollarSign
} from "lucide-react"
import Link from "next/link"

export default function ShopDashboardPage() {
  const router = useRouter()
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { currentShop, loading: shopLoading } = useShop()
  const [lowStockCount, setLowStockCount] = useState(0)
  const [orderCount, setOrderCount] = useState(0)
  const [productCount, setProductCount] = useState(0)
  const [salesTotal, setSalesTotal] = useState(0)
  
  // Redirect to shop selection if no shop is selected
  useEffect(() => {
    if (!authLoading && !shopLoading && isAuthenticated && !currentShop) {
      router.push("/shops")
    }
  }, [authLoading, shopLoading, isAuthenticated, currentShop, router])
  
  // Fetch dashboard data
  useEffect(() => {
    if (currentShop) {
      // In a real implementation, you would fetch this data from the API
      // For now, we'll use mock data
      setLowStockCount(5)
      setOrderCount(12)
      setProductCount(48)
      setSalesTotal(4250)
    }
  }, [currentShop])
  
  const loading = authLoading || shopLoading
  
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <MainHeader />
        <main className="flex-1 p-4">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/3"></div>
            <div className="grid grid-cols-2 gap-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </main>
        <MainFooter />
      </div>
    )
  }
  
  return (
    <AuthCheck>
      <div className="flex flex-col min-h-screen">
        <MainHeader />
        
        <main className="flex-1 p-4">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Shop Dashboard</h1>
            <ShopSelector />
          </div>
          
          {currentShop && (
            <>
              <div className="grid grid-cols-2 gap-4 mb-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Products</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">{productCount}</div>
                      <Package className="h-5 w-5 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Orders</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">{orderCount}</div>
                      <ShoppingCart className="h-5 w-5 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Low Stock</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">{lowStockCount}</div>
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-500">Sales</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center">
                      <div className="text-2xl font-bold">R{salesTotal}</div>
                      <TrendingUp className="h-5 w-5 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Package className="h-5 w-5 text-blue-600" />
                      Inventory Management
                    </CardTitle>
                    <CardDescription>
                      Manage your products and stock levels
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex gap-2">
                    <Link href="/shop/products" passHref>
                      <Button variant="outline" className="flex-1">View Products</Button>
                    </Link>
                    <Link href="/shop/products/low-stock" passHref>
                      <Button variant="outline" className="flex-1">Low Stock Items</Button>
                    </Link>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ShoppingCart className="h-5 w-5 text-green-600" />
                      Order Management
                    </CardTitle>
                    <CardDescription>
                      View and manage customer orders
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex gap-2">
                    <Link href="/shop/orders" passHref>
                      <Button variant="outline" className="flex-1">View Orders</Button>
                    </Link>
                    <Link href="/shop/orders/new" passHref>
                      <Button variant="outline" className="flex-1">Create Order</Button>
                    </Link>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart className="h-5 w-5 text-purple-600" />
                      Reports & Analytics
                    </CardTitle>
                    <CardDescription>
                      View sales reports and analytics
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex gap-2">
                    <Link href="/shop/reports/sales" passHref>
                      <Button variant="outline" className="flex-1">Sales Report</Button>
                    </Link>
                    <Link href="/shop/reports/inventory" passHref>
                      <Button variant="outline" className="flex-1">Inventory Report</Button>
                    </Link>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-indigo-600" />
                      Shop Management
                    </CardTitle>
                    <CardDescription>
                      Manage your shop settings and staff
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="flex gap-2">
                    <Link href="/shop/settings" passHref>
                      <Button variant="outline" className="flex-1">Shop Settings</Button>
                    </Link>
                    <Link href="/shop/staff" passHref>
                      <Button variant="outline" className="flex-1">Manage Staff</Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </>
          )}
        </main>
        
        <MainFooter />
      </div>
    </AuthCheck>
  )
}
