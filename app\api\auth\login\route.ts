import { NextRequest, NextResponse } from 'next/server'
import { signIn } from '@/lib/services/auth-service'
import { z } from 'zod'

// Define a schema for the request body
const loginSchema = z.object({
  mobile: z.string().min(10, { message: "Mobile must be at least 10 characters" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
})

export async function POST(request: NextRequest) {
  try {
    // Validate the request body against the schema
    const body = await request.json()
    const validatedBody = loginSchema.safeParse(body)

    if (!validatedBody.success) {
      return NextResponse.json({
        success: false,
        error: validatedBody.error.issues[0].message
      }, { status: 400 })
    }

    const { mobile, password } = validatedBody.data
    const { success, userId, error } = await signIn(mobile, password)

    if (!success) {
      return NextResponse.json({ success: false, error }, { status: 400 })
    }

    return NextResponse.json({ success: true, userId })
  } catch (err: any) {
    console.error('Error in login API:', err)
    return NextResponse.json(
      { success: false, error: 'Unexpected server error' },
      { status: 500 }
    )
  }
}

