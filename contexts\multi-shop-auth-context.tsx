"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import type { User, UserWithShopRoles } from "@/lib/types"
import { getUserWithShopRoles } from "@/utils/role-utils"
import { devLog, logError } from "@/utils/logger"
import { AuthChangeEvent, Session } from '@supabase/supabase-js'
import { createClient } from '@/utils/supabase/client'
import { formatMobile } from "@/utils/direct-login"

interface AuthContextType {
  user: User | null
  userWithRoles: UserWithShopRoles | null
  loading: boolean
  isAuthenticated: boolean
  signOut: () => Promise<{ success: boolean; error?: string } | void>
  login: (mobile: string, password: string) => Promise<{ success: boolean; error?: string | undefined }>
  register: (mobile: string, password: string, shopName?: string, address?: string) => Promise<{ success: boolean; error?: string | undefined }>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userWithRoles: null,
  loading: true,
  isAuthenticated: false,
  signOut: async () => ({ success: false, error: "Not implemented" }),
  login: async () => ({ success: false, error: "Not implemented" }),
  register: async () => ({ success: false, error: "Not implemented" })
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userWithRoles, setUserWithRoles] = useState<UserWithShopRoles | null>(null)
  const [loading, setLoading] = useState(true)

  // Initialize Supabase client only on the client side
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Get the singleton instance of the client
        const client = createClient()

        if (!client) {
          console.error("Failed to initialize Supabase client: Missing environment variables")
          setUser(null)
          setUserWithRoles(null)
          setLoading(false)
          return
        }

        // Check for existing session
        const { data: { session } } = await client.auth.getSession()
        if (session?.user) {
          setUser(session.user)
          await loadUserWithRoles(session.user.id)
        }
        setLoading(false)
      } catch (error) {
        logError('Failed to initialize Supabase client:', error)
        setUser(null)
        setUserWithRoles(null)
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  // Load user with roles
  const loadUserWithRoles = async (userId: string) => {
    try {
      const result = await getUserWithShopRoles(userId)
      setUserWithRoles(result)
    } catch (error) {
      logError('Failed to load user roles:', error)
    }
  }

  // Login implementation
  const handleLogin = async (mobile: string, password: string) => {
    try {
      console.log("Login attempt with mobile:", mobile);

      // Get the singleton instance of the client
      const client = createClient()

      if (!client) {
        console.error("Authentication service not initialized. Check your environment variables.")
        return { success: false, error: "Authentication service not initialized. Please check your configuration." }
      }

      console.log("Supabase client initialized successfully for login");

      const formattedMobile = formatMobile(mobile)
      console.log("Formatted mobile for login:", formattedMobile);

      // Try both formats: direct mobile and email format
      console.log("Attempting to sign in with mobile as email:", formattedMobile);
      let result;

      try {
        result = await client.auth.signInWithPassword({
          email: formattedMobile,
          password
        });

        // If direct mobile format fails, try with email format
        if (result.error) {
          console.log("Direct mobile login failed, trying email format");
          const emailFormat = `mobile${formattedMobile}@gmail.com`;
          console.log("Attempting to sign in with email format:", emailFormat);
          result = await client.auth.signInWithPassword({
            email: emailFormat,
            password
          });
        }
      } catch (networkError) {
        console.error("Network error during authentication:", networkError);
        return {
          success: false,
          error: "Network connection failed. Please check your internet connection and try again."
        };
      }

      const { data, error } = result;

      if (error) {
        console.error("Login error from Supabase:", error);
        return { success: false, error: error.message || 'Login failed' } as const;
      }

      console.log("Login response data:", data ? "Data received" : "No data");

      if (!data?.user) {
        console.error("No user returned from login");
        return { success: false, error: 'Login failed - no user returned' }
      }

      console.log("User authenticated successfully:", data.user.id);

      // Update user state immediately
      setUser(data.user)

      // Set authentication state in localStorage for persistence
      localStorage.setItem('userLoggedIn', 'true');
      localStorage.setItem('currentUserId', data.user.id);

      // Set a flag in sessionStorage to indicate this is a fresh login
      // This will be used by FreshLoginHandler to trigger redirection
      sessionStorage.setItem('freshLogin', 'true');

      // Load user roles in the background
      console.log("Loading user roles...");
      try {
        await loadUserWithRoles(data.user.id);
        console.log("User roles loaded successfully");
      } catch (roleError) {
        console.error("Failed to load user roles:", roleError);
        // Continue anyway since basic authentication succeeded
      }

      console.log("Login completed successfully");

      // Add a small delay to ensure state is updated before returning
      await new Promise(resolve => setTimeout(resolve, 100))

      return { success: true }
    } catch (error) {
      console.error('Login failed with exception:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Login failed' } as const;
    }
  }

  // Simple sign out implementation (replaced by more comprehensive version below)
  // This function is kept for reference but not used

  // Register implementation
  const handleRegister = async (mobile: string, password: string, shopName?: string, address?: string) => {
    try {
      const formattedMobile = formatMobile(mobile)

      // Use the same email format as in the login function
      const email = `mobile${formattedMobile}@gmail.com`
      console.log("Registering with email format:", email);

      let data, error;

      try {
        const result = await createClient().auth.signUp({
          email: email,
          password
        });
        data = result.data;
        error = result.error;
      } catch (networkError) {
        console.error("Network error during registration:", networkError);
        return {
          success: false,
          error: "Network connection failed. Please check your internet connection and try again."
        };
      }

      if (error) {
        return { success: false, error: `Registration failed: ${error.message}` } as const;
      }

      if (!data?.user) {
        return { success: false, error: 'Registration failed - no user returned' }
      }

      // Create user profile
      const { error: profileError } = await createClient()
        .from('users')
        .insert({
          id: data.user.id,
          mobile: formattedMobile,
          shop_name: shopName,
          address,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      if (profileError) {
        return { success: false, error: profileError.message } as const;
      }

      setUser({...data.user, is_admin: data.user.user_metadata?.is_admin || false})
      await loadUserWithRoles(data.user.id)
      return { success: true }
    } catch (error) {
      logError('Registration failed:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Registration failed' } as const;
    }
  }

  // Only run auth logic when Supabase client is available
  useEffect(() => {
    // If Supabase client isn't initialized yet, don't proceed
    const fetchUser = async () => {
      try {
        const client = createClient()
        const { data: { session } } = await client.auth.getSession()

        if (session?.user) {
          setUser({...session.user, is_admin: session.user.user_metadata?.is_admin || false})
          await loadUserWithRoles(session.user.id)
        } else {
          // No session found
          devLog('Auth context: No session found');
          setUser(null);
          setUserWithRoles(null);
          setLoading(false);
        }
      } catch (error) {
        // Error in fetchUser
        logError('Auth context: Error in fetchUser:', error);
        setUser(null);
        setUserWithRoles(null);
        setLoading(false);
      }
    }

    // Initial session check
    (async () => {
      try {
        const client = createClient();

        if (!client) {
          console.error("Auth context: Could not initialize Supabase client for session check");
          setUser(null);
          setUserWithRoles(null);
          setLoading(false);
          return;
        }

        const { data: { session } } = await client.auth.getSession();
        if (session) {
          try {
            if (session?.user) {
              setUser({...session.user, is_admin: session.user.user_metadata?.is_admin || false});
              await loadUserWithRoles(session.user.id);
            }
          } catch (error) {
            logError('Auth context: Error in fetchUser:', error);
            setUser(null);
            setUserWithRoles(null);
            setLoading(false);
          }
        } else {
          setUser(null);
          setUserWithRoles(null);
          setLoading(false);
        }
      } catch (error) {
        logError('Auth context: Error getting session:', error);
        setUser(null);
        setUserWithRoles(null);
        setLoading(false);
      }
    })();

    // Setting up auth state change listener
    const client = createClient();
    let authListener: any = null;

    if (client) {
      authListener = client.auth.onAuthStateChange(async (event: AuthChangeEvent, session: Session | null) => {
        devLog(`Auth context: Auth state changed: ${event}`);

        // Get the current URL path
        const currentPath = window.location.pathname;

        if (event === "SIGNED_IN" && session?.user) {
          devLog('Auth context: User signed in');

          // Check if this is a broadcast event from another tab
          const isBroadcast = sessionStorage.getItem('authEventHandled') === 'true';

          if (!isBroadcast) {
            // Mark that we've handled this auth event to prevent duplicate handling
            sessionStorage.setItem('authEventHandled', 'true');

            // Update user state
            setUser({...session.user, is_admin: session.user.user_metadata?.is_admin || false});
            await loadUserWithRoles(session.user.id);
            localStorage.setItem('userLoggedIn', 'true');

            // Clear this flag after a short delay to allow for future auth events
            setTimeout(() => {
              sessionStorage.removeItem('authEventHandled');
            }, 2000);
          } else {
            devLog('Auth context: Ignoring duplicate SIGNED_IN event from broadcast');
          }

          // Don't redirect here - let the login page handle redirects
          // This prevents redirect loops
        } else if (event === "SIGNED_OUT") {
          devLog('Auth context: User signed out');

          // Check if this is a broadcast event from another tab
          const isBroadcast = sessionStorage.getItem('signOutEventHandled') === 'true';

          if (!isBroadcast) {
            // Mark that we've handled this sign out event
            sessionStorage.setItem('signOutEventHandled', 'true');

            // Update state
            setUser(null);
            setUserWithRoles(null);
            setLoading(false);
            localStorage.removeItem('userLoggedIn');

            // Clear all redirect tracking to prevent loops
            sessionStorage.removeItem('redirectingTo');
            sessionStorage.removeItem('redirectAttemptCount');
            sessionStorage.removeItem('currentRedirectKey');
            sessionStorage.removeItem('loginPageInstance');

            // Only redirect if we're not already on the home page
            if (currentPath !== '/' && currentPath !== '/login' && currentPath !== '/auth') {
              devLog('Auth context: Redirecting to home after sign out');
              window.location.href = '/';
            }

            // Clear this flag after a short delay
            setTimeout(() => {
              sessionStorage.removeItem('signOutEventHandled');
            }, 2000);
          } else {
            devLog('Auth context: Ignoring duplicate SIGNED_OUT event from broadcast');
          }
        } else if (event === "TOKEN_REFRESHED" && session?.user) {
          devLog('Auth context: Token refreshed, fetching user data again');

          // Silently update user data without redirects
          setUser({...session.user, is_admin: session.user.user_metadata?.is_admin || false});
          await loadUserWithRoles(session.user.id);

          // Don't redirect on token refresh - this can cause loops
        }
      });
    } else {
      console.error("Auth context: Could not set up auth state change listener - client not initialized");
      setLoading(false);
    }

    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe()
      }
    }
  }, [])

  const handleSignOut = async () => {
    try {
      devLog('Auth context: Signing out...');

      // Mark that we're handling a sign out to prevent duplicate handling
      sessionStorage.setItem('signOutEventHandled', 'true');

      // Get the client
      const client = createClient();

      if (!client) {
        console.error("Authentication service not initialized. Check your environment variables.");
        // Still clear local state even if client is not available
        setUser(null);
        setUserWithRoles(null);
        localStorage.removeItem('userLoggedIn');
        localStorage.removeItem('currentShopId');

        // Clear all session storage related to auth and redirects
        sessionStorage.removeItem('redirectingTo');
        sessionStorage.removeItem('redirectAttemptCount');
        sessionStorage.removeItem('freshLogin');
        sessionStorage.removeItem('currentRedirectKey');
        sessionStorage.removeItem('loginPageInstance');

        // Get current path
        const currentPath = window.location.pathname;

        // Only redirect if we're not already on a public page
        if (currentPath !== '/' && currentPath !== '/login' && currentPath !== '/auth') {
          window.location.href = '/';
        }

        return;
      }

      // Sign out from Supabase
      const { error } = await client.auth.signOut();

      if (error) {
        logError('Auth context: Sign out error:', error);
        return { success: false, error: 'Sign out failed' } as const;
      }

      // Clear user state
      setUser(null);
      setUserWithRoles(null);

      // Clear local storage
      localStorage.removeItem('userLoggedIn');
      localStorage.removeItem('currentShopId');

      // Clear all session storage related to auth and redirects
      sessionStorage.removeItem('redirectingTo');
      sessionStorage.removeItem('redirectAttemptCount');
      sessionStorage.removeItem('freshLogin');
      sessionStorage.removeItem('currentRedirectKey');
      sessionStorage.removeItem('loginPageInstance');

      // Get current path
      const currentPath = window.location.pathname;

      // Only redirect if we're not already on a public page
      if (currentPath !== '/' && currentPath !== '/login' && currentPath !== '/auth') {
        // Force a page reload to clear any cached state
        window.location.href = '/';
      }

      // Clear the sign out event handled flag after a delay
      setTimeout(() => {
        sessionStorage.removeItem('signOutEventHandled');
      }, 2000);

      return { success: true };
    } catch (err) {
      logError('Auth context: Exception during sign out:', err);

      // Even if there's an exception, still try to clean up the state
      setUser(null);
      setUserWithRoles(null);

      // Clear all session storage related to auth and redirects
      sessionStorage.removeItem('redirectingTo');
      sessionStorage.removeItem('redirectAttemptCount');
      sessionStorage.removeItem('freshLogin');
      sessionStorage.removeItem('currentRedirectKey');
      sessionStorage.removeItem('loginPageInstance');

      // Get current path
      const currentPath = window.location.pathname;

      // Only redirect if we're not already on a public page
      if (currentPath !== '/' && currentPath !== '/login' && currentPath !== '/auth') {
        // Force a page reload to clear any cached state
        window.location.href = '/';
      }

      return { success: false, error: 'Sign out failed with exception' };
    }
  }

  // Login and register functions are implemented above
  // No unused functions here

  return (
    <AuthContext.Provider value={{
      user,
      userWithRoles,
      loading,
      isAuthenticated: !!user,
      signOut: handleSignOut,
      login: handleLogin,
      register: handleRegister
    }}>
      {children}
    </AuthContext.Provider>
  )
}





