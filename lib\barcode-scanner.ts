"use client"

import { Browser<PERSON>ultiFormatReader, BarcodeFormat, DecodeHintType, Result } from '@zxing/library'
import { BrowserQRCodeReader } from '@zxing/browser'

export type BarcodeResult = {
  text: string
  format: string
}

export type ScannerOptions = {
  formats?: BarcodeFormat[]
  tryHarder?: boolean
  onResult?: (result: BarcodeResult) => void
  onError?: (error: Error) => void
}

export class BarcodeScanner {
  private reader: BrowserMultiFormatReader
  private videoElement: HTMLVideoElement | null = null
  private active = false
  private timerId: NodeJS.Timeout | null = null
  private onResultCallback: ((result: BarcodeResult) => void) | null = null
  private onErrorCallback: ((error: Error) => void) | null = null

  constructor(options: ScannerOptions = {}) {
    // Configure hints for the reader
    const hints = new Map()

    // Set formats to scan for
    const formats = options.formats || [
      BarcodeFormat.EAN_13,
      BarcodeFormat.EAN_8,
      BarcodeFormat.UPC_A,
      BarcodeFormat.UPC_E,
      BarcodeFormat.CODE_39,
      BarcodeFormat.CODE_128,
      BarcodeFormat.QR_CODE
    ]
    hints.set(DecodeHintType.POSSIBLE_FORMATS, formats)

    // Set try harder flag for better detection but slower performance
    if (options.tryHarder) {
      hints.set(DecodeHintType.TRY_HARDER, true)
    }

    // Create the reader with hints
    this.reader = new BrowserMultiFormatReader(hints)

    // Set callbacks
    this.onResultCallback = options.onResult || null
    this.onErrorCallback = options.onError || null
  }

  /**
   * Start scanning using the provided video element
   */
  public async startScanning(videoElement: HTMLVideoElement): Promise<void> {
    this.videoElement = videoElement
    this.active = true

    try {
      // Request camera access
      const constraints = {
        video: { facingMode: 'environment' }
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      this.videoElement.srcObject = stream

      // Start the scanning loop
      this.scanLoop()
    } catch (error) {
      this.active = false
      if (this.onErrorCallback) {
        this.onErrorCallback(error instanceof Error ? error : new Error(String(error)))
      }
    }
  }

  /**
   * Stop scanning and release resources
   */
  public stopScanning(): void {
    this.active = false

    if (this.timerId) {
      clearTimeout(this.timerId)
      this.timerId = null
    }

    if (this.videoElement && this.videoElement.srcObject) {
      const stream = this.videoElement.srcObject as MediaStream
      stream.getTracks().forEach(track => track.stop())
      this.videoElement.srcObject = null
    }
  }

  /**
   * Main scanning loop
   */
  private scanLoop(): void {
    if (!this.active || !this.videoElement) {
      return
    }

    // Check if video is ready
    if (this.videoElement.readyState === this.videoElement.HAVE_ENOUGH_DATA) {
      try {
        // Create a canvas to capture the current video frame
        const canvas = document.createElement('canvas')
        const context = canvas.getContext('2d')

        if (!context) {
          throw new Error('Could not get canvas context')
        }

        // Set canvas dimensions to match video
        canvas.width = this.videoElement.videoWidth
        canvas.height = this.videoElement.videoHeight

        // Draw current video frame to canvas
        context.drawImage(this.videoElement, 0, 0, canvas.width, canvas.height)

        // Get image data from canvas
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height)

        // Decode the image data
        const result = this.reader.decodeFromCanvas(canvas)

        if (result && this.onResultCallback) {
          const barcodeResult: BarcodeResult = {
            text: result.getText(),
            format: result.getBarcodeFormat().toString()
          }

          this.onResultCallback(barcodeResult)
        }
      } catch (error) {
        // Ignore errors from not finding a barcode - these are expected
        // Only report other types of errors to the callback
        if (!(error instanceof Error) || !error.message.includes('No MultiFormat Readers were able to detect')) {
          if (this.onErrorCallback) {
            this.onErrorCallback(error instanceof Error ? error : new Error(String(error)))
          }
        }
      }
    }

    // Continue scanning - use requestAnimationFrame for better performance
    this.timerId = setTimeout(() => {
      if (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {
        window.requestAnimationFrame(() => this.scanLoop())
      } else {
        this.scanLoop()
      }
    }, 150) // Increased from 100ms to 150ms to reduce CPU usage
  }
}

/**
 * Create a barcode scanner with the specified options
 */
export function createBarcodeScanner(options: ScannerOptions = {}): BarcodeScanner {
  return new BarcodeScanner(options)
}

/**
 * Scan a single barcode from an image element
 */
export async function scanBarcodeFromImage(
  imageElement: HTMLImageElement
): Promise<BarcodeResult | null> {
  try {
    const hints = new Map()
    hints.set(DecodeHintType.POSSIBLE_FORMATS, [
      BarcodeFormat.EAN_13,
      BarcodeFormat.EAN_8,
      BarcodeFormat.UPC_A,
      BarcodeFormat.UPC_E,
      BarcodeFormat.CODE_39,
      BarcodeFormat.CODE_128,
      BarcodeFormat.QR_CODE
    ])
    hints.set(DecodeHintType.TRY_HARDER, true)

    const reader = new BrowserMultiFormatReader(hints)
    const result = await reader.decodeFromImageElement(imageElement)

    if (result) {
      return {
        text: result.getText(),
        format: result.getBarcodeFormat().toString()
      }
    }

    return null
  } catch (error) {
    // Error scanning barcode from image - return null
    return null
  }
}
