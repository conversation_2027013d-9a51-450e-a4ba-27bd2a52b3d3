import type { AppProps } from 'next/app'
import { useEffect } from 'react'
import { useRouter } from 'next/router'
import '../app/globals.css'

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter()

  // Handle errors at the app level
  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Global error caught by _app.tsx:', error)
      // You could report to an error tracking service here
    }

    window.addEventListener('error', handleError)

    return () => {
      window.removeEventListener('error', handleError)
    }
  }, [])

  // Redirect to App Router paths when possible
  useEffect(() => {
    // If we're on a Pages Router path that has an App Router equivalent,
    // redirect to the App Router path
    if (router.pathname === '/') {
      router.replace('/')
    }
  }, [router])

  return <Component {...pageProps} />
}
