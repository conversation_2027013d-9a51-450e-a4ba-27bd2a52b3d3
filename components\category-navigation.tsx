"use client"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { ChevronRight, ChevronLeft, Grid } from "lucide-react"
import { categoryIcons } from "@/lib/product-icons"

interface Category {
  id: string
  name: string
}

interface CategoryNavigationProps {
  categories: Category[]
  activeCategory?: string
}

export function CategoryNavigation({ categories, activeCategory }: CategoryNavigationProps) {
  const [scrollPosition, setScrollPosition] = useState(0)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  const canScrollLeft = scrollPosition > 0
  const canScrollRight = scrollContainerRef.current
    ? scrollPosition < scrollContainerRef.current.scrollWidth - scrollContainerRef.current.clientWidth
    : false

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      setScrollPosition(scrollContainerRef.current.scrollLeft)
    }
  }

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' })
    }
  }

  const handleCategoryClick = (categoryId: string) => {
    router.push(`/products/filter?category=${categoryId}`)
  }

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll)
      return () => scrollContainer.removeEventListener('scroll', handleScroll)
    }
  }, [])

  // Scroll active category into view
  useEffect(() => {
    if (activeCategory && scrollContainerRef.current) {
      const activeElement = scrollContainerRef.current.querySelector(`[data-category-id="${activeCategory}"]`)
      if (activeElement) {
        activeElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' })
      }
    }
  }, [activeCategory])

  return (
    <div className="relative bg-white border-b border-gray-200">
      {/* Left scroll button */}
      {canScrollLeft && (
        <button
          onClick={scrollLeft}
          className="absolute left-0 top-0 bottom-0 bg-gradient-to-r from-white to-transparent z-10 px-2 flex items-center justify-center min-w-[44px] touch-target"
          aria-label="Scroll left"
        >
          <div className="bg-white rounded-full p-2 shadow-md flex items-center justify-center min-w-[44px] min-h-[44px]">
            <ChevronLeft size={24} className="text-gray-600" />
          </div>
        </button>
      )}

      {/* Categories */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto py-2 px-4 scrollbar-hide scroll-smooth"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {/* All categories button */}
        <button
          onClick={() => router.push('/products')}
          className={`flex flex-col items-center justify-center min-w-[80px] p-3 rounded-lg mr-2 transition-colors touch-target ${
            !activeCategory ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          <div className="bg-white p-2 rounded-full mb-1">
            <Grid size={24} className={!activeCategory ? 'text-blue-600' : 'text-gray-600'} />
          </div>
          <span className="text-xs font-medium whitespace-nowrap">All Items</span>
        </button>

        {categories.map((category) => (
          <button
            key={category.id}
            data-category-id={category.id}
            onClick={() => handleCategoryClick(category.id)}
            className={`flex flex-col items-center justify-center min-w-[80px] p-3 rounded-lg mr-2 transition-colors touch-target ${
              activeCategory === category.id ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <div className="bg-white p-2 rounded-full mb-1">
              {categoryIcons[category.name] || categoryIcons["Beverages"]}
            </div>
            <span className="text-xs font-medium whitespace-nowrap">{category.name}</span>
          </button>
        ))}
      </div>

      {/* Right scroll button */}
      {canScrollRight && (
        <button
          onClick={scrollRight}
          className="absolute right-0 top-0 bottom-0 bg-gradient-to-l from-white to-transparent z-10 px-2 flex items-center justify-center min-w-[44px] touch-target"
          aria-label="Scroll right"
        >
          <div className="bg-white rounded-full p-2 shadow-md flex items-center justify-center min-w-[44px] min-h-[44px]">
            <ChevronRight size={24} className="text-gray-600" />
          </div>
        </button>
      )}
    </div>
  )
}
