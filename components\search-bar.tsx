"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Search, Mic, X, History, TrendingUp } from "lucide-react"

// Mock suggestions for demo purposes
const POPULAR_SEARCHES = [
  "Maize Meal",
  "Rice",
  "Cooking Oil",
  "Sugar",
  "Milk",
  "Bread",
  "Canned Food",
  "Soft Drinks"
]

export function SearchBar() {
  const [searchQuery, setSearchQuery] = useState("")
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [isListening, setIsListening] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('searchHistory')
    if (history) {
      try {
        setSearchHistory(JSON.parse(history).slice(0, 5))
      } catch (e) {
        console.error('Error parsing search history:', e)
      }
    }
  }, [])

  // Update suggestions when search query changes
  useEffect(() => {
    if (searchQuery.trim().length > 0) {
      // Filter suggestions based on input
      const filtered = POPULAR_SEARCHES.filter(item =>
        item.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setSuggestions(filtered.slice(0, 5))
      setShowSuggestions(true)
    } else {
      setSuggestions([])
      setShowSuggestions(false)
    }
  }, [searchQuery])

  // Handle clicks outside the suggestions dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Save to search history
      const newHistory = [
        searchQuery.trim(),
        ...searchHistory.filter(item => item !== searchQuery.trim())
      ].slice(0, 5)

      setSearchHistory(newHistory)
      localStorage.setItem('searchHistory', JSON.stringify(newHistory))

      // Navigate to search results
      router.push(`/products/filter?query=${encodeURIComponent(searchQuery.trim())}`)
      setShowSuggestions(false)
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion)

    // Save to search history
    const newHistory = [
      suggestion,
      ...searchHistory.filter(item => item !== suggestion)
    ].slice(0, 5)

    setSearchHistory(newHistory)
    localStorage.setItem('searchHistory', JSON.stringify(newHistory))

    // Navigate to search results
    router.push(`/products/filter?query=${encodeURIComponent(suggestion)}`)
    setShowSuggestions(false)
  }

  const clearSearch = () => {
    setSearchQuery("")
    inputRef.current?.focus()
  }

  const startVoiceSearch = () => {
    if (!('webkitSpeechRecognition' in window)) {
      alert('Voice search is not supported in your browser')
      return
    }

    try {
      // @ts-ignore - SpeechRecognition is not in the TypeScript types
      const recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)()
      recognition.lang = 'en-US'

      setIsListening(true)

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        setSearchQuery(transcript)
        setIsListening(false)

        // Auto-submit after voice input
        setTimeout(() => {
          router.push(`/products/filter?query=${encodeURIComponent(transcript)}`)
        }, 500)
      }

      recognition.onerror = () => {
        setIsListening(false)
      }

      recognition.onend = () => {
        setIsListening(false)
      }

      recognition.start()
    } catch (error) {
      console.error('Error starting voice recognition:', error)
      setIsListening(false)
    }
  }

  return (
    <div className="relative z-20 bg-white shadow-sm">
      <div className="p-3 bg-blue-50 border-b border-blue-100">
        <form onSubmit={handleSearch} className="relative">
          <input
            ref={inputRef}
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => {
              setIsFocused(true)
              if (searchQuery.trim().length > 0) {
                setShowSuggestions(true)
              }
            }}
            onBlur={() => setIsFocused(false)}
            placeholder="Search products..."
            className={`w-full p-3 pl-10 pr-16 border ${
              isFocused ? 'border-blue-400 ring-2 ring-blue-100' : 'border-gray-300'
            } rounded-full text-base transition-all duration-200 shadow-sm`}
            style={{ fontSize: '16px' }} /* Prevents iOS zoom */
          />

          {/* Search icon */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-500">
            <Search size={20} />
          </div>

          {/* Clear button */}
          {searchQuery && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 p-2 rounded-full min-w-[44px] min-h-[44px] touch-target flex items-center justify-center"
              aria-label="Clear search"
            >
              <X size={20} />
            </button>
          )}

          {/* Voice search button */}
          <button
            type="button"
            onClick={startVoiceSearch}
            className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-full min-w-[44px] min-h-[44px] touch-target flex items-center justify-center ${
              isListening
                ? 'bg-red-100 text-red-500 animate-pulse'
                : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
            }`}
            aria-label="Voice search"
          >
            <Mic size={20} />
          </button>
        </form>

        {/* Suggestions dropdown */}
        {showSuggestions && (suggestions.length > 0 || searchHistory.length > 0) && (
          <div
            ref={suggestionsRef}
            className="absolute left-0 right-0 bg-white mt-1 mx-3 rounded-lg shadow-lg border border-gray-200 z-30 overflow-hidden animate-in slide-in-from-top-2 duration-200"
          >
            {/* Search history */}
            {searchHistory.length > 0 && (
              <div className="p-2">
                <div className="flex items-center gap-1 text-xs text-gray-500 px-2 py-1">
                  <History size={14} />
                  <span>Recent searches</span>
                </div>
                {searchHistory.map((item, index) => (
                  <button
                    key={`history-${index}`}
                    onClick={() => handleSuggestionClick(item)}
                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-gray-700"
                  >
                    <History size={16} className="text-gray-400" />
                    {item}
                  </button>
                ))}
              </div>
            )}

            {/* Divider */}
            {searchHistory.length > 0 && suggestions.length > 0 && (
              <div className="border-t border-gray-100 mx-2"></div>
            )}

            {/* Suggestions */}
            {suggestions.length > 0 && (
              <div className="p-2">
                <div className="flex items-center gap-1 text-xs text-gray-500 px-2 py-1">
                  <TrendingUp size={14} />
                  <span>Suggestions</span>
                </div>
                {suggestions.map((suggestion, index) => (
                  <button
                    key={`suggestion-${index}`}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-gray-700"
                  >
                    <Search size={16} className="text-gray-400" />
                    {suggestion}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
