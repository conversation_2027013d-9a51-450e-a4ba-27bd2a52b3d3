"use client"

import { useState, useEffect, create<PERSON>ontext, useContext, ReactNode } from "react"
import { X, CheckCircle, AlertCircle, Info } from "lucide-react"

type ToastType = "success" | "error" | "info"

interface Toast {
  id: string
  message: string
  type: ToastType
  duration?: number
}

interface ToastContextType {
  toasts: Toast[]
  showToast: (message: string, type: ToastType, duration?: number) => void
  hideToast: (id: string) => void
}

const ToastContext = createContext<ToastContextType>({
  toasts: [],
  showToast: () => {},
  hideToast: () => {},
})

export const useToast = () => useContext(ToastContext)

export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const showToast = (message: string, type: ToastType = "info", duration = 3000) => {
    const id = Math.random().toString(36).substring(2, 9)
    setToasts((prev) => [...prev, { id, message, type, duration }])
  }

  const hideToast = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id))
  }

  return (
    <ToastContext.Provider value={{ toasts, showToast, hideToast }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

function ToastContainer() {
  const { toasts, hideToast } = useToast()

  return (
    <div className="fixed bottom-4 left-0 right-0 z-50 flex flex-col items-center gap-2 pointer-events-none">
      {toasts.map((toast) => (
        <ToastNotification key={toast.id} toast={toast} onClose={() => hideToast(toast.id)} />
      ))}
    </div>
  )
}

function ToastNotification({ toast, onClose }: { toast: Toast; onClose: () => void }) {
  useEffect(() => {
    if (toast.duration) {
      const timer = setTimeout(() => {
        onClose()
      }, toast.duration)
      return () => clearTimeout(timer)
    }
  }, [toast, onClose])

  const getIcon = () => {
    switch (toast.type) {
      case "success":
        return <CheckCircle className="text-white" size={18} />
      case "error":
        return <AlertCircle className="text-white" size={18} />
      case "info":
        return <Info className="text-white" size={18} />
    }
  }

  const getBackgroundColor = () => {
    switch (toast.type) {
      case "success":
        return "bg-green-600"
      case "error":
        return "bg-red-600"
      case "info":
        return "bg-blue-600"
    }
  }

  return (
    <div
      className={`${getBackgroundColor()} text-white px-4 py-3 rounded-md shadow-lg flex items-center gap-2 max-w-xs sm:max-w-sm md:max-w-md pointer-events-auto animate-in slide-in-from-bottom-5 duration-300`}
      role="alert"
    >
      {getIcon()}
      <span className="flex-grow">{toast.message}</span>
      <button onClick={onClose} className="text-white hover:text-gray-200">
        <X size={18} />
      </button>
    </div>
  )
}
