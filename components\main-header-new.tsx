"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useShop } from "@/contexts/shop-context"
import { useCart } from "@/contexts/cart-context"
import Link from "next/link"
import { LogOut, ShoppingCart, Store } from "lucide-react"
import { ShopSelector } from "./shop-selector"

export function MainHeader() {
  const router = useRouter()
  const { user, isAuthenticated, signOut } = useAuth()
  const { currentShop } = useShop()
  const { cartCount } = useCart()
  const [loggingOut, setLoggingOut] = useState(false)

  const handleLogout = async () => {
    if (loggingOut) return
    
    setLoggingOut(true)
    try {
      await signOut()
      // Redirect happens in signOut function
    } catch (error) {
      console.error("Logout error:", error)
      setLoggingOut(false)
    }
  }

  return (
    <header className="bg-blue-600 text-white p-3 relative z-10 flex justify-between items-center">
      <div className="flex items-center gap-2">
        <h1 className="text-xl font-bold">Spaza Smart Order</h1>
        {isAuthenticated && currentShop && (
          <div className="hidden sm:flex items-center bg-blue-700 rounded px-2 py-1">
            <Store className="h-3 w-3 mr-1" />
            <span className="text-xs font-medium truncate max-w-[100px]">
              {currentShop.name}
            </span>
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        {isAuthenticated && (
          <>
            <Link href="/cart" className="relative p-1">
              <ShoppingCart size={20} />
              {cartCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {cartCount > 99 ? '99+' : cartCount}
                </span>
              )}
            </Link>
            
            <div className="flex items-center gap-2">
              {/* User indicator */}
              <div className="flex items-center bg-blue-700 rounded px-2 py-1">
                <span className="text-xs font-medium truncate max-w-[80px]">
                  {user?.full_name || user?.mobile || 'User'}
                </span>
              </div>

              {/* Logout button */}
              <button
                onClick={handleLogout}
                disabled={loggingOut}
                className="text-sm flex items-center gap-1 bg-blue-700 px-2 py-1 rounded hover:bg-blue-800 disabled:opacity-50 transition-colors"
              >
                <LogOut size={14} />
                <span className="hidden sm:inline">{loggingOut ? 'Logging out...' : 'Logout'}</span>
              </button>
            </div>
          </>
        )}
        
        {!isAuthenticated && (
          <Link href="/auth" className="text-sm bg-blue-700 px-2 py-1 rounded hover:bg-blue-800">
            Login
          </Link>
        )}
      </div>
    </header>
  )
}
