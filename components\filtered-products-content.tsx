"use client"
import Link from "next/link"
import { ProductList } from "@/components/product-list"
import { ProductFilters } from "@/components/product-filters"
import type { Product, Category } from "@/lib/types"

interface FilteredProductsContentProps {
  products: Product[]
  categories: Category[]
  minPrice: number
  maxPrice: number
  selectedCategory?: Category
}

export function FilteredProductsContent({
  products,
  categories,
  minPrice,
  maxPrice,
  selectedCategory,
}: FilteredProductsContentProps) {
  return (
    <div className="p-4 flex-grow overflow-y-auto">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-xl font-bold">Products</h1>
        <Link href="/products" className="text-blue-600 text-sm">
          View All Products
        </Link>
      </div>

      <ProductFilters categories={categories} minPrice={minPrice} maxPrice={maxPrice} />

      <div className="mb-4">
        <h2 className="font-bold text-lg">
          {selectedCategory ? selectedCategory.name : "All Products"}
          <span className="font-normal text-gray-500 text-sm ml-2">
            ({products.length} item{products.length !== 1 ? "s" : ""})
          </span>
        </h2>
      </div>

      {products.length > 0 ? (
        <ProductList products={products} />
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">No products match your filters</p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      )}
    </div>
  )
}
