import { NextResponse, NextRequest } from "next/server"
import { processPaymentCallback } from "@/lib/payment-service"

/**
 * Payment callback handler for Shop2Shop
 * This endpoint is called when a user is redirected back from the payment gateway
 */
export async function GET(request: NextRequest) {
  // Get query parameters
  const url = new URL(request.url)
  const paymentId = url.searchParams.get("paymentId")
  const status = url.searchParams.get("status") || "success"
  const transactionId = url.searchParams.get("transactionId")

  if (!paymentId) {
    return NextResponse.redirect(new URL("/orders", request.url))
  }

  try {
    // Process the payment callback - paymentId will be handled as string and converted as needed
    const result = await processPaymentCallback(
      paymentId,
      status,
      transactionId || undefined
    )

    if (result.success && result.data) {
      // Redirect to order confirmation page
      if (result.data.orderId) {
        return NextResponse.redirect(
          new URL(`/orders/${result.data.orderId}/confirmation`, request.url)
        )
      } else {
        return NextResponse.redirect(new URL("/orders", request.url))
      }
    } else {
      // Redirect to payment page with error
      return NextResponse.redirect(
        new URL(`/payment/${paymentId}?error=payment_failed`, request.url)
      )
    }
  } catch (error) {
    return NextResponse.redirect(new URL("/orders", request.url))
  }
}

/**
 * Payment notification handler for Shop2Shop
 * This endpoint is called by Shop2Shop to notify of payment status changes
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Extract payment details from the webhook payload
    const paymentId = body.merchantReference
    const status = body.status
    const transactionId = body.transactionId

    if (!paymentId) {
      return NextResponse.json(
        { error: "Missing payment ID" },
        { status: 400 }
      )
    }

    // Process the payment callback
    const result = await processPaymentCallback(
      paymentId,
      status || "success",
      transactionId
    )

    if (result.success && result.data) {
      return NextResponse.json({
        success: true,
        status: result.data.status,
        orderId: result.data.orderId
      })
    } else {
      return NextResponse.json(
        { error: result.error || "Payment processing failed" },
        { status: 500 }
      )
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}


