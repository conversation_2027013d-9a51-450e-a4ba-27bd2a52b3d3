import { getReceiptById } from "@/lib/receipt-service"

// Format date helper function
function formatDate(dateString?: string) {
  if (!dateString) return "N/A"
  const date = new Date(dateString)
  return date.toLocaleDateString() + " " + date.toLocaleTimeString()
}

import Link from "next/link"
import { ArrowLeft, Download, Printer } from "lucide-react"

export default async function ReceiptDetailPage({ params }: { params: { id: string } }) {
  const receipt = await getReceiptById(params.id)

  if (!receipt) {
    return (
      <div className="p-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Receipt Not Found</h1>
        <p className="mb-4">The requested receipt could not be found.</p>
        <Link href="/admin/receipts" className="text-blue-600 hover:underline">
          Back to Receipts
        </Link>
      </div>
    )
  }

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <Link href="/admin/receipts" className="flex items-center text-blue-600 hover:underline">
          <ArrowLeft size={16} className="mr-2" />
          Back to Receipts
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-md p-8 mb-6">
        <div className="flex justify-between items-start mb-8">
          <div>
            <h1 className="text-2xl font-bold">Receipt #{receipt.receipt_number}</h1>
            <p className="text-gray-600">Issued: {formatDate(receipt.issued_at)}</p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => window.print()}
              className="flex items-center px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 print:hidden"
            >
              <Printer size={16} className="mr-2" />
              Print
            </button>
            {receipt.receipt_url && (
              <a
                href={receipt.receipt_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 print:hidden"
              >
                <Download size={16} className="mr-2" />
                Download
              </a>
            )}
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8 mb-8">
          <div>
            <h2 className="text-lg font-semibold mb-2">Customer</h2>
            <p className="font-medium">{receipt.user?.shop_name || "No Shop Name"}</p>
            <p>{receipt.user?.mobile}</p>
          </div>
          <div>
            <h2 className="text-lg font-semibold mb-2">Payment Details</h2>
            <p><span className="font-medium">Method:</span> {receipt.payment?.payment_method}</p>
            <p><span className="font-medium">Status:</span> {receipt.payment?.status}</p>
            <p><span className="font-medium">Order ID:</span> {receipt.order?.id}</p>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Amount</h2>
            <p className="text-2xl font-bold">
              {receipt.payment?.amount !== undefined && receipt.payment?.amount !== null && receipt.payment?.amount !== 0 ?
                new Intl.NumberFormat('en-ZA', {
                  style: 'currency',
                  currency: 'ZAR'
                }).format(receipt.payment?.amount)
                :
                new Intl.NumberFormat('en-ZA', {
                  style: 'currency',
                  currency: 'ZAR'
                }).format(receipt.order?.total_amount)
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}




