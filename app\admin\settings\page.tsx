"use client"

import { useState } from "react"
import Link from "next/link"
import { 
  Settings, User, Store, Bell, Database, Shield, 
  CreditCard, Smartphone, Globe, Save, Loader2
} from "lucide-react"

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("general")
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Settings</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="bg-white rounded-lg shadow p-4">
          <nav className="space-y-1">
            <button
              onClick={() => setActiveTab("general")}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === "general"
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              <Settings className="mr-3 h-5 w-5 text-gray-400" />
              <span>General</span>
            </button>
            
            <button
              onClick={() => setActiveTab("profile")}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === "profile"
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              <User className="mr-3 h-5 w-5 text-gray-400" />
              <span>Shop Profile</span>
            </button>
            
            <button
              onClick={() => setActiveTab("notifications")}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === "notifications"
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              <Bell className="mr-3 h-5 w-5 text-gray-400" />
              <span>Notifications</span>
            </button>
            
            <button
              onClick={() => setActiveTab("database")}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === "database"
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              <Database className="mr-3 h-5 w-5 text-gray-400" />
              <span>Database</span>
            </button>
            
            <button
              onClick={() => setActiveTab("security")}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === "security"
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              <Shield className="mr-3 h-5 w-5 text-gray-400" />
              <span>Security</span>
            </button>
            
            <button
              onClick={() => setActiveTab("payments")}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                activeTab === "payments"
                  ? "bg-blue-50 text-blue-700"
                  : "text-gray-700 hover:bg-gray-100"
              }`}
            >
              <CreditCard className="mr-3 h-5 w-5 text-gray-400" />
              <span>Payments</span>
            </button>
          </nav>
        </div>

        {/* Settings Content */}
        <div className="md:col-span-3 bg-white rounded-lg shadow p-6">
          {activeTab === "general" && <GeneralSettings />}
          {activeTab === "profile" && <ProfileSettings />}
          {activeTab === "notifications" && <NotificationSettings />}
          {activeTab === "database" && <DatabaseSettings />}
          {activeTab === "security" && <SecuritySettings />}
          {activeTab === "payments" && <PaymentSettings />}
        </div>
      </div>
    </div>
  )
}

function GeneralSettings() {
  const [loading, setLoading] = useState(false)
  
  const handleSave = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }
  
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">General Settings</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Store Name
          </label>
          <input
            type="text"
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="Your store name"
            defaultValue="Spaza Smart Order"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Currency
          </label>
          <select className="w-full p-2 border border-gray-300 rounded-md">
            <option value="ZAR">South African Rand (R)</option>
            <option value="USD">US Dollar ($)</option>
            <option value="EUR">Euro (€)</option>
            <option value="GBP">British Pound (£)</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Time Zone
          </label>
          <select className="w-full p-2 border border-gray-300 rounded-md">
            <option value="Africa/Johannesburg">Africa/Johannesburg (GMT+2)</option>
            <option value="UTC">UTC</option>
            <option value="Europe/London">Europe/London (GMT+0/+1)</option>
            <option value="America/New_York">America/New_York (GMT-5/-4)</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Date Format
          </label>
          <select className="w-full p-2 border border-gray-300 rounded-md">
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
          </select>
        </div>
        
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleSave}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center gap-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            Save Settings
          </button>
        </div>
      </div>
    </div>
  )
}

function ProfileSettings() {
  const [loading, setLoading] = useState(false)
  
  const handleSave = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }
  
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Shop Profile</h2>
      
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Shop Name
          </label>
          <input
            type="text"
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="Your shop name"
            defaultValue="Spaza Smart Shop"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Contact Email
          </label>
          <input
            type="email"
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="<EMAIL>"
            defaultValue="<EMAIL>"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Contact Phone
          </label>
          <input
            type="tel"
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="Your phone number"
            defaultValue="+27 12 345 6789"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Shop Address
          </label>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={3}
            placeholder="Your shop address"
            defaultValue="123 Main Street, Johannesburg, South Africa"
          ></textarea>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Shop Description
          </label>
          <textarea
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={4}
            placeholder="Description of your shop"
            defaultValue="We provide quality products at affordable prices. Visit us for all your daily needs."
          ></textarea>
        </div>
        
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleSave}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center gap-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            Save Profile
          </button>
        </div>
      </div>
    </div>
  )
}

function NotificationSettings() {
  const [loading, setLoading] = useState(false)
  
  const handleSave = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }
  
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Notification Settings</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Email Notifications</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                id="new-order"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="new-order" className="ml-2 block text-sm text-gray-700">
                New order notifications
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="low-stock"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="low-stock" className="ml-2 block text-sm text-gray-700">
                Low stock alerts
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="payment-received"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="payment-received" className="ml-2 block text-sm text-gray-700">
                Payment received notifications
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="system-updates"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="system-updates" className="ml-2 block text-sm text-gray-700">
                System updates and announcements
              </label>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">SMS Notifications</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                id="sms-new-order"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="sms-new-order" className="ml-2 block text-sm text-gray-700">
                New order notifications
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="sms-low-stock"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="sms-low-stock" className="ml-2 block text-sm text-gray-700">
                Low stock alerts
              </label>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">In-App Notifications</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                id="app-all"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="app-all" className="ml-2 block text-sm text-gray-700">
                Enable all in-app notifications
              </label>
            </div>
          </div>
        </div>
        
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleSave}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center gap-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            Save Notification Settings
          </button>
        </div>
      </div>
    </div>
  )
}

function DatabaseSettings() {
  const [loading, setLoading] = useState(false)
  
  const handleBackup = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 2000)
  }
  
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Database Settings</h2>
      
      <div className="space-y-6">
        <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <Database className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                Your database is hosted on Supabase. Some advanced settings can only be changed from the Supabase dashboard.
              </p>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Database Information</h3>
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-xs text-gray-500">Database Provider</p>
                <p className="text-sm font-medium">Supabase</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Region</p>
                <p className="text-sm font-medium">eu-central-1</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Status</p>
                <p className="text-sm font-medium text-green-600">Online</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">Last Backup</p>
                <p className="text-sm font-medium">Today, 03:00 AM</p>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Backup & Restore</h3>
          <div className="space-y-3">
            <button
              onClick={handleBackup}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center gap-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? <Loader2 size={16} className="animate-spin" /> : <Database size={16} />}
              Create Manual Backup
            </button>
            <p className="text-xs text-gray-500">
              Automatic backups are created daily. You can also create a manual backup at any time.
            </p>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Data Management</h3>
          <div className="space-y-3">
            <Link
              href="https://app.supabase.com/project/hmfiuuexlpdwgppvvvuw/editor"
              target="_blank"
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md flex items-center gap-2 hover:bg-gray-50 w-fit"
            >
              <Globe size={16} />
              Open Supabase Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

function SecuritySettings() {
  const [loading, setLoading] = useState(false)
  
  const handleSave = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }
  
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Password</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Current Password
              </label>
              <input
                type="password"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter current password"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                New Password
              </label>
              <input
                type="password"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter new password"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Confirm New Password
              </label>
              <input
                type="password"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Confirm new password"
              />
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Two-Factor Authentication</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-700">Enable two-factor authentication</p>
                <p className="text-xs text-gray-500">Add an extra layer of security to your account</p>
              </div>
              <div className="ml-4">
                <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                  Enable
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Session Management</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                id="auto-logout"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="auto-logout" className="ml-2 block text-sm text-gray-700">
                Automatically log out after inactivity
              </label>
            </div>
            <div className="ml-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Inactivity timeout (minutes)
              </label>
              <input
                type="number"
                className="w-32 p-2 border border-gray-300 rounded-md"
                defaultValue="30"
                min="5"
                max="120"
              />
            </div>
          </div>
        </div>
        
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleSave}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center gap-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            Save Security Settings
          </button>
        </div>
      </div>
    </div>
  )
}

function PaymentSettings() {
  const [loading, setLoading] = useState(false)
  
  const handleSave = () => {
    setLoading(true)
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }
  
  return (
    <div>
      <h2 className="text-lg font-medium text-gray-900 mb-4">Payment Settings</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Payment Methods</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                id="cash"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="cash" className="ml-2 block text-sm text-gray-700">
                Cash on Delivery
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="shop2shop"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="shop2shop" className="ml-2 block text-sm text-gray-700">
                Shop2Shop
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="credit-card"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="credit-card" className="ml-2 block text-sm text-gray-700">
                Credit Card
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="bank-transfer"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="bank-transfer" className="ml-2 block text-sm text-gray-700">
                Bank Transfer
              </label>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Shop2Shop Configuration</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Shop2Shop API Key
              </label>
              <input
                type="password"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter Shop2Shop API key"
                defaultValue="••••••••••••••••"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Shop2Shop Merchant ID
              </label>
              <input
                type="text"
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Enter Shop2Shop merchant ID"
                defaultValue="SPAZA123456"
              />
            </div>
            <div className="flex items-center">
              <input
                id="shop2shop-test"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="shop2shop-test" className="ml-2 block text-sm text-gray-700">
                Use Shop2Shop test environment
              </label>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-sm font-medium text-gray-700 mb-3">Currency & Tax</h3>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Default Tax Rate (%)
              </label>
              <input
                type="number"
                className="w-32 p-2 border border-gray-300 rounded-md"
                defaultValue="15"
                min="0"
                max="100"
                step="0.01"
              />
            </div>
            <div className="flex items-center">
              <input
                id="include-tax"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                defaultChecked
              />
              <label htmlFor="include-tax" className="ml-2 block text-sm text-gray-700">
                Prices include tax
              </label>
            </div>
          </div>
        </div>
        
        <div className="pt-4 border-t border-gray-200">
          <button
            onClick={handleSave}
            disabled={loading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center gap-2 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            Save Payment Settings
          </button>
        </div>
      </div>
    </div>
  )
}
