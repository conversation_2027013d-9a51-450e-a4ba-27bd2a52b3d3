"use server"

import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/utils/supabase/client'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json({
        success: false,
        error: 'Email and password are required'
      }, { status: 400 })
    }

    // Get Supabase client
    const supabase = await createServerClient()

    // Sign in with email/password
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error || !data.user) {
      console.error("Admin login error:", error)
      return NextResponse.json({
        success: false,
        error: error?.message || 'Login failed'
      }, { status: 400 })
    }

    // Check if user has admin or owner role in any shop
    const { data: userRoles, error: userError } = await supabase
      .from('user_roles')
      .select(`
        role_id,
        roles:roles(name)
      `)
      .eq('user_id', data.user.id)

    if (userError) {
      console.error("Error checking admin status:", userError)
      return NextResponse.json({
        success: false,
        error: 'Error verifying admin status'
      }, { status: 500 })
    }

    // Check if user has admin or owner role
    const isAdmin = userRoles?.some(role => {
      const roleName = role.roles?.name;
      return roleName === 'Owner' || roleName === 'Admin';
    }) || false;

    if (!isAdmin) {
      // Not an admin, sign out and return error
      await supabase.auth.signOut()
      return NextResponse.json({
        success: false,
        error: 'Not authorized as admin'
      }, { status: 403 })
    }

    // Set auth cookies if session is available
    if (data.session) {
      // Create a response with cookies
      const response = NextResponse.json({
        success: true,
        userId: data.user.id
      })

      // Set access token cookie
      response.cookies.set('sb-access-token', data.session.access_token, {
        path: '/',
        maxAge: data.session.expires_in,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      })

      return response
    }

    return NextResponse.json({
      success: true,
      userId: data.user.id
    })
  } catch (err: any) {
    console.error('Error in admin login API:', err)
    return NextResponse.json({
      success: false,
      error: 'Unexpected server error'
    }, { status: 500 })
  }
}
