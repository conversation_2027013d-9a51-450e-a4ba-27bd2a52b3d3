-- Create receipts table
CREATE TABLE IF NOT EXISTS receipts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_id BIGINT NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  receipt_number TEXT NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  issued_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  receipt_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS receipts_payment_id_idx ON receipts(payment_id);
CREATE INDEX IF NOT EXISTS receipts_receipt_number_idx ON receipts(receipt_number);

-- Add RLS policies
ALTER TABLE receipts ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own receipts
CREATE POLICY receipts_select_policy ON receipts
  FOR SELECT
  USING (
    payment_id IN (
      SELECT id FROM payments WHERE order_id IN (
        SELECT id FROM orders WHERE user_id = auth.uid()
      )
    )
  );

-- Policy for service role to manage all receipts
CREATE POLICY receipts_service_policy ON receipts
  USING (auth.role() = 'service_role');
