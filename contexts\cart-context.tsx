// "use client"
"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { useAuth } from "./auth-context"
import { useOffline } from "./offline-context"
import { useRealTimeData } from "./real-time-context"
import { getCartItems } from "@/lib/data-service"
import type { CartItem, Product } from "@/lib/types"
import {
  saveOfflineCart,
  getOfflineCart,
  type OfflineCartItem
} from "@/lib/offline-sync"
import { CartErrorFallback } from "@/components/cart-error-fallback"
import { devLog, logError } from "@/utils/logger"

interface CartContextType {
  cartItems: CartItem[]
  cartCount: number
  cartTotal: number
  loading: boolean
  error: boolean
  errorComponent: React.ReactNode | null
  addToCart: (productId: string, quantity: number) => Promise<boolean>
  updateCartItemQuantity: (itemId: string, quantity: number) => Promise<boolean>
  removeCartItem: (itemId: string) => Promise<boolean>
  refreshCart: () => Promise<void>
  clearCart: () => Promise<boolean>
}

const CartContext = createContext<CartContextType>({
  cartItems: [],
  cartCount: 0,
  cartTotal: 0,
  loading: true,
  error: false,
  errorComponent: null,
  addToCart: async () => false,
  updateCartItemQuantity: async () => false,
  removeCartItem: async () => false,
  refreshCart: async () => {},
  clearCart: async () => false,
})

export const useCart = () => useContext(CartContext)

export function CartProvider({ children }: { children: ReactNode }) {
  const { user } = useAuth()
  const { offline } = useOffline()
  const realTimeContext = useRealTimeData()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [offlineItems, setOfflineItems] = useState<OfflineCartItem[]>([])
  const [loading, setLoading] = useState(true)
  const [offlineMode, setOfflineMode] = useState(false)

  // Initialize products when real-time context becomes available
  useEffect(() => {
    if (realTimeContext?.products) {
      setProducts(realTimeContext.products as unknown as Product[])
      setLoading(false)
    }
  }, [realTimeContext?.products])
  const [error, setError] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Create a product cache for quick lookups
  const [productCache, setProductCache] = useState<Record<string, Product>>({})

  // Update product cache when products change
  useEffect(() => {
    if (products && products.length > 0) {
      const newCache: Record<string, Product> = {}
      products.forEach(product => {
        newCache[product.id] = product
      })
      setProductCache(newCache)
    }
  }, [products])

  // Calculate cart count (total number of items)
  const cartCount = offlineMode
    ? offlineItems.reduce((count, item) => count + item.quantity, 0)
    : cartItems.reduce((count, item) => count + item.quantity, 0)

  // Calculate cart total
  const cartTotal = offlineMode
    ? offlineItems.reduce((total, item) => total + item.price * item.quantity, 0)
    : cartItems.reduce((total, item) => {
        const price = item.product?.price || 0
        return total + price * item.quantity
      }, 0)

  // Monitor offline status
  useEffect(() => {
    setOfflineMode(offline)

    // If we're offline, load offline cart
    if (offline) {
      const offlineCartItems = getOfflineCart()
      setOfflineItems(offlineCartItems)
      setLoading(false)
    } else if (user?.id) {
      // If we're back online and have a user, refresh cart from server
      refreshCart()
    }
  }, [offline, user?.id])

  // Load cart items when user changes
  useEffect(() => {
    // Only refresh cart if we have a user and we're online
    if (user?.id && !offline) {
      // Add a small delay to prevent blocking the UI
      const timer = setTimeout(() => {
        refreshCart()
      }, 100);
      return () => clearTimeout(timer);
    } else if (!user?.id) {
      // Clear cart if no user
      setCartItems([]);
      setOfflineItems([]);
      setLoading(false);
    }
  }, [user?.id, offline])

  // Function to refresh cart data with optimized performance
  const refreshCart = async () => {
    if (!user?.id) {
      setCartItems([])
      setLoading(false)
      setError(false)
      return
    }

    // Reset error state when refreshing
    setError(false)

    // Use a local variable to track if this is the first load
    const isFirstLoad = cartItems.length === 0

    // Only show loading indicator on first load, not on refreshes
    // This prevents UI flicker when refreshing
    if (isFirstLoad) {
      setLoading(true)
    }

    // Set a shorter timeout to ensure loading state doesn't get stuck
    // This improves user experience by showing fallback data faster
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        setLoading(false)

        // If we still don't have items after timeout, show error
        // but only if this was the first load
        if (cartItems.length === 0 && isFirstLoad) {
          setError(true)
        }
      }
    }, 5000) // Reduced to 5 seconds for better UX

    try {
      // Check if we're offline first
      if (offline || !navigator.onLine) {
        // If offline, use offline cart
        setOfflineMode(true)
        const offlineCartItems = getOfflineCart()
        setOfflineItems(offlineCartItems)
        setLoading(false)
        clearTimeout(loadingTimeout)
        return
      }

      // Use a cache key to prevent duplicate requests
      const cacheKey = `cart_${user.id}_${Date.now()}_${retryCount}`

      // First try to get items from cache
      const cachedItems = cartItems.length > 0 ? cartItems : null;

      // Use a shorter timeout for better UX and optimized error handling
      const items = await Promise.race([
        getCartItems(user.id, cacheKey),
        new Promise<CartItem[]>((resolve) => {
          // Always resolve with cached data or empty array after timeout
          // This prevents hanging UI and provides a better user experience
          setTimeout(() => {
            if (cachedItems && cachedItems.length > 0) {
              // Silently use cached items without excessive logging
              resolve(cachedItems);
            } else {
              // Resolve with empty array if no cached items
              resolve([]);
            }
          }, 5000) // Reduced to 5 seconds for faster response
        })
      ]);

      // Only update if we got items back
      if (items && Array.isArray(items)) {
        setCartItems(items)
        setError(false) // Clear any previous errors
      } else {
        // If we got an empty response, show error only if we have no items
        logError("Invalid cart data received")
        if (cartItems.length === 0) {
          setError(true)
        }
      }
    } catch (error) {
      logError("Error loading cart items:", error)

      // If we have existing cart items, don't show error to prevent UI disruption
      if (cartItems.length > 0) {
        devLog("Using existing cart items despite error");
        setError(false); // Don't show error if we have items
      } else {
        // Only set error if we have no items
        setError(true);
      }

      // If we have a network error or timeout, switch to offline mode
      if (
        error instanceof TypeError && error.message.includes('fetch') ||
        error instanceof Error && error.message.includes('timeout')
      ) {
        devLog("Network error or timeout detected, switching to offline mode");
        setOfflineMode(true);
        const offlineCartItems = getOfflineCart();
        setOfflineItems(offlineCartItems);

        // If we have offline items, don't show error
        if (offlineCartItems.length > 0) {
          setError(false);
        }
      }
    } finally {
      setLoading(false);
      clearTimeout(loadingTimeout);

      // Final state logging removed for production
    }
  }

  // Handle retry when cart loading fails
  const handleRetry = () => {
    setRetryCount(prev => prev + 1) // Increment retry count to force a new request
    refreshCart()
  }

  // Add item to cart
  const addToCart = async (productId: string, quantity: number): Promise<boolean> => {
    if (!user && !offline) return false

    // Check if the product exists in our cache
    const productExists = productCache[productId] !== undefined;

    if (!productExists && !offline) {
      devLog("Product not found in cache:", productId);
      // If we're using mock data, we should still allow adding to cart
      // but log a warning
      if (Object.keys(productCache).length === 0) {
        devLog("No products loaded in cache yet, proceeding with caution");
      } else {
        // Only throw an error if we have products loaded but this one isn't found
        console.error("Product not found in available products");
        return false;
      }
    }

    // Handle offline mode
    if (offline) {
      try {
        // Fetch product details from cache or use minimal info
        let productName = "Product"
        let productPrice = 0

        // First check if the product exists in our real-time cache
        if (productCache[productId]) {
          const product = productCache[productId];
          productName = product.name;
          productPrice = typeof product.price === 'string'
            ? parseFloat(product.price)
            : product.price;

          // Cache products in localStorage for offline use
          try {
            const cachedProducts = localStorage.getItem('cachedProducts') || '[]';
            const products = JSON.parse(cachedProducts);

            // Check if product already exists in cache
            const existingIndex = products.findIndex((p: any) => p.id === productId);

            if (existingIndex >= 0) {
              // Update existing product
              products[existingIndex] = product;
            } else {
              // Add new product
              products.push(product);
            }

            localStorage.setItem('cachedProducts', JSON.stringify(products));
          } catch (cacheError) {
            console.error("Error caching product:", cacheError);
          }
        } else {
          // Try to find product in existing cart items
          const existingProduct = offlineItems.find(item => item.productId === productId)
          if (existingProduct) {
            productName = existingProduct.name
            productPrice = existingProduct.price
          } else {
            // Try to get product from offline cache
            const cachedProducts = localStorage.getItem('cachedProducts')
            if (cachedProducts) {
              try {
                const products = JSON.parse(cachedProducts)
                const product = products.find((p: any) => p.id === productId)
                if (product) {
                  productName = product.name
                  productPrice = typeof product.price === 'string'
                    ? parseFloat(product.price)
                    : product.price
                }
              } catch (parseError) {
                console.error("Error parsing cached products:", parseError)
                // Continue with default values
              }
            }
          }
        }

        // Check if item already exists in cart
        const updatedItems = [...offlineItems]
        const existingItemIndex = updatedItems.findIndex(item => item.productId === productId)

        if (existingItemIndex >= 0) {
          // Update existing item
          updatedItems[existingItemIndex].quantity += quantity
        } else {
          // Add new item
          updatedItems.push({
            productId,
            quantity,
            price: productPrice,
            name: productName
          })
        }

        // Save to offline storage
        setOfflineItems(updatedItems)
        await saveOfflineCart(updatedItems)
        return true
      } catch (error) {
        console.error("Error adding to offline cart:", error)
        // Error adding to offline cart
        return false
      }
    }

    // Online mode
    try {
      // Use a timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      // Cache the product in localStorage before sending to server
      // This helps with offline fallback if the server request fails
      if (productCache[productId]) {
        try {
          const cachedProducts = localStorage.getItem('cachedProducts') || '[]';
          const products = JSON.parse(cachedProducts);

          // Check if product already exists in cache
          const existingIndex = products.findIndex((p: any) => p.id === productId);

          if (existingIndex >= 0) {
            // Update existing product
            products[existingIndex] = productCache[productId];
          } else {
            // Add new product
            products.push(productCache[productId]);
          }

          localStorage.setItem('cachedProducts', JSON.stringify(products));
        } catch (cacheError) {
          console.error("Error caching product:", cacheError);
        }
      }

      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user?.id,
          productId,
          quantity
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // Handle non-OK responses
      if (!response.ok) {
        let errorMessage = 'Failed to add to cart';
        try {
          const result = await response.json();
          errorMessage = result.error || errorMessage;

          // If it's a 404 (product not found), but we have it in our cache,
          // there might be a sync issue between the client and server
          if (response.status === 404 && productCache[productId]) {
            console.error("Product exists in cache but not on server:", productId);
            errorMessage = "Product exists locally but not on server. Try refreshing the page.";

            // We can't call useRealTimeData() here, so we'll just log the issue
            // and let the user know they need to refresh the page
            console.error("Product sync issue detected. User should refresh the page.");
          }
        } catch (jsonError) {
          // If we can't parse the JSON, use the default error message
          console.error("Error parsing error response:", jsonError);
        }

        // Check if we're offline and switch to offline mode
        if (!navigator.onLine || response.status === 0) {
          console.log("Network error detected, switching to offline mode");
          setOfflineMode(true);
          return addToCart(productId, quantity); // Retry in offline mode
        }

        throw new Error(errorMessage);
      }

      // Always refresh the cart to ensure we have the latest data
      try {
        await refreshCart();
      } catch (refreshError) {
        console.error("Error refreshing cart after add:", refreshError);
        // Continue anyway - the item was added successfully
      }

      return true;
    } catch (error) {
      console.error("Error adding to cart:", error);

      // If online request fails due to network issues, try to save offline
      if (error instanceof DOMException && error.name === 'AbortError') {
        console.log("Request timed out, switching to offline mode");
        setOfflineMode(true);
        return addToCart(productId, quantity);
      }

      if (!navigator.onLine || (error instanceof TypeError && error.message.includes('fetch'))) {
        console.log("Network error detected, switching to offline mode");
        setOfflineMode(true);
        return addToCart(productId, quantity);
      }

      return false;
    }
  }

  // Update cart item quantity
  const updateCartItemQuantity = async (itemId: string, quantity: number): Promise<boolean> => {
    if (quantity < 1) return false

    // Handle offline mode
    if (offline || offlineMode) {
      try {
        // For offline items, we need to find by productId instead of itemId
        // This is a simplification - in a real app, you'd need a more robust approach
        const productId = itemId

        // Update the item in the offline cart
        const updatedItems = offlineItems.map(item =>
          item.productId === productId ? { ...item, quantity } : item
        )

        // Save to state and storage
        setOfflineItems(updatedItems)
        await saveOfflineCart(updatedItems)
        return true
      } catch (error) {
        // Error updating offline cart
        return false
      }
    }

    // Online mode
    if (!user) return false

    try {
      // Optimistically update the UI
      setCartItems(items =>
        items.map(item => item.id === itemId ? { ...item, quantity } : item)
      )

      const response = await fetch('/api/cart/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemId,
          quantity
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update cart')
      }

      return true
    } catch (error) {
      // If online request fails, try to update offline
      if (navigator.onLine === false) {
        setOfflineMode(true)
        // We need the product ID for offline mode
        const item = cartItems.find(item => item.id === itemId)
        if (item && item.product_id) {
          return updateCartItemQuantity(item.product_id, quantity)
        }
      }

      // Reload cart items to reset to server state if there was an error
      await refreshCart()
      return false
    }
  }

  // Remove item from cart
  const removeCartItem = async (itemId: string): Promise<boolean> => {
    // Handle offline mode
    if (offline || offlineMode) {
      try {
        // For offline items, we need to find by productId instead of itemId
        const productId = itemId

        // Remove the item from the offline cart
        const updatedItems = offlineItems.filter(item => item.productId !== productId)

        // Save to state and storage
        setOfflineItems(updatedItems)
        await saveOfflineCart(updatedItems)
        return true
      } catch (error) {
        // Error removing item from offline cart
        return false
      }
    }

    // Online mode
    if (!user) return false

    try {
      // Optimistically update the UI
      setCartItems(items => items.filter(item => item.id !== itemId))

      const response = await fetch('/api/cart/remove', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          itemId
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to remove item from cart')
      }

      return true
    } catch (error) {
      // If online request fails, try to remove offline
      if (navigator.onLine === false) {
        setOfflineMode(true)
        // We need the product ID for offline mode
        const item = cartItems.find(item => item.id === itemId)
        if (item && item.product_id) {
          return removeCartItem(item.product_id)
        }
      }

      // Reload cart items to reset to server state if there was an error
      await refreshCart()
      return false
    }
  }

  // Sync offline cart with server when coming back online
  const syncOfflineCart = async (): Promise<boolean> => {
    if (!user || !offlineItems.length) return false

    try {
      let success = true

      // Process each offline item
      for (const item of offlineItems) {
        try {
          // Add to server cart
          const response = await fetch('/api/cart/add', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: user.id,
              productId: item.productId,
              quantity: item.quantity
            }),
          })

          if (!response.ok) {
            success = false
          }
        } catch (error) {
          // Error syncing item
          success = false
        }
      }

      // Clear offline cart if successful
      if (success) {
        setOfflineItems([])
        await saveOfflineCart([])
      }

      // Refresh cart from server
      await refreshCart()
      return success
    } catch (error) {
      // Error syncing offline cart
      return false
    }
  }

  // Try to sync offline cart when coming back online
  useEffect(() => {
    if (!offline && offlineMode && user && offlineItems.length > 0) {
      syncOfflineCart().then(success => {
        if (success) {
          setOfflineMode(false)
        }
      })
    }
  }, [offline, offlineMode, user, offlineItems.length])

  // Clear the entire cart
  const clearCart = async (): Promise<boolean> => {
    // Handle offline mode
    if (offline || offlineMode) {
      try {
        // Clear offline cart
        setOfflineItems([])
        await saveOfflineCart([])
        return true
      } catch (error) {
        logError("Error clearing offline cart:", error)
        return false
      }
    }

    // Online mode
    if (!user?.id) return false

    try {
      // Optimistically update UI immediately
      setCartItems([])

      // Use a timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      // Clear cart in database
      const response = await fetch('/api/cart/clear', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId);

      if (!response.ok) {
        // If the API call fails, refresh the cart to get the actual state
        await refreshCart()
        return false
      }

      // Make sure the cart is really cleared in the UI
      setCartItems([])
      return true
    } catch (error) {
      logError("Error clearing cart:", error)

      // If we have a network error, switch to offline mode
      if (!navigator.onLine ||
          (error instanceof TypeError && error.message.includes('fetch')) ||
          (error instanceof DOMException && error.name === 'AbortError')) {
        devLog("Network error detected during cart clear, switching to offline mode");
        setOfflineMode(true);
        return clearCart(); // Retry in offline mode
      }

      // Refresh to get actual state
      await refreshCart()
      return false
    }
  }

  // Create error component when needed
  const errorComponent = error ? (
    <CartErrorFallback onRetry={handleRetry} />
  ) : null;

  return (
    <CartContext.Provider
      value={{
        cartItems: offlineMode ? [] : cartItems,
        cartCount,
        cartTotal,
        loading,
        error,
        errorComponent,
        addToCart,
        updateCartItemQuantity,
        removeCartItem,
        refreshCart,
        clearCart
      }}
    >
      {children}
    </CartContext.Provider>
  )
}


