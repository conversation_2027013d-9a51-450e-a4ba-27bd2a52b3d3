"use client"

import { useState, useEffect } from "react"
import { createClient } from '@/utils/supabase/client'
import Link from "next/link"

export default function SimpleLoginTest() {
  const [session, setSession] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [loginData, setLoginData] = useState({ mobile: "0721234567", password: "password123" })

  useEffect(() => {
    checkSession()
  }, [])

  const checkSession = async () => {
    try {
      const supabase = createClient()
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        setError(`Session error: ${error.message}`)
      } else {
        setSession(session)
        console.log("Current session:", session)
      }
    } catch (err) {
      setError(`Error checking session: ${err}`)
    } finally {
      setLoading(false)
    }
  }

  const handleLogin = async () => {
    setLoading(true)
    setError("")
    
    try {
      const supabase = createClient()
      
      // Format mobile number
      let formattedMobile = loginData.mobile.replace(/\D/g, '')
      if (formattedMobile.startsWith('0')) {
        formattedMobile = '27' + formattedMobile.substring(1)
      }
      
      console.log("Attempting login with:", formattedMobile)
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formattedMobile,
        password: loginData.password
      })
      
      if (error) {
        // Try with email format
        const emailFormat = `mobile${formattedMobile}@gmail.com`
        console.log("Trying email format:", emailFormat)
        
        const { data: data2, error: error2 } = await supabase.auth.signInWithPassword({
          email: emailFormat,
          password: loginData.password
        })
        
        if (error2) {
          setError(`Login failed: ${error2.message}`)
        } else {
          setSession(data2.session)
          console.log("Login successful with email format")
        }
      } else {
        setSession(data.session)
        console.log("Login successful with mobile format")
      }
    } catch (err) {
      setError(`Login error: ${err}`)
    } finally {
      setLoading(false)
    }
  }

  const handleSignOut = async () => {
    try {
      const supabase = createClient()
      await supabase.auth.signOut()
      setSession(null)
      console.log("Signed out successfully")
    } catch (err) {
      setError(`Sign out error: ${err}`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Simple Login Test</h1>

        {loading && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2">Loading...</p>
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded text-sm">
            {error}
          </div>
        )}

        {session ? (
          <div className="space-y-4">
            <div className="p-4 bg-green-100 rounded">
              <h2 className="font-bold text-green-800 mb-2">✅ Logged In</h2>
              <p className="text-sm">User ID: {session.user?.id}</p>
              <p className="text-sm">Email: {session.user?.email}</p>
              <p className="text-sm">Mobile: {session.user?.user_metadata?.mobile}</p>
            </div>
            
            <button
              onClick={handleSignOut}
              className="w-full bg-red-600 text-white py-2 rounded hover:bg-red-700"
            >
              Sign Out
            </button>
            
            <Link
              href="/products"
              className="block w-full bg-blue-600 text-white py-2 rounded text-center hover:bg-blue-700"
            >
              Go to Products Page
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Mobile Number:</label>
              <input
                type="text"
                value={loginData.mobile}
                onChange={(e) => setLoginData({...loginData, mobile: e.target.value})}
                className="w-full p-3 border rounded focus:ring-2 focus:ring-blue-500"
                placeholder="0721234567"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Password:</label>
              <input
                type="password"
                value={loginData.password}
                onChange={(e) => setLoginData({...loginData, password: e.target.value})}
                className="w-full p-3 border rounded focus:ring-2 focus:ring-blue-500"
                placeholder="password123"
              />
            </div>
            
            <button
              onClick={handleLogin}
              disabled={loading}
              className="w-full bg-blue-600 text-white py-3 rounded hover:bg-blue-700 disabled:bg-blue-400"
            >
              {loading ? "Logging in..." : "Login"}
            </button>
          </div>
        )}

        <div className="mt-6 space-y-2">
          <button
            onClick={checkSession}
            className="w-full bg-gray-600 text-white py-2 rounded hover:bg-gray-700"
          >
            Refresh Session
          </button>
          
          <Link
            href="/debug-login"
            className="block w-full bg-purple-600 text-white py-2 rounded text-center hover:bg-purple-700"
          >
            Go to Debug Page
          </Link>
          
          <Link
            href="/"
            className="block w-full bg-gray-600 text-white py-2 rounded text-center hover:bg-gray-700"
          >
            Go to Home
          </Link>
        </div>
      </div>
    </div>
  )
}
