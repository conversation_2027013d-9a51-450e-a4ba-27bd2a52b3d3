"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { useShop } from "@/contexts/shop-context"
import { getShopOrders } from "@/lib/services/order-service"
import { getProducts } from "@/lib/services/product-service"
import { getUserShops } from "@/lib/services/user-service"
import {
  Shield, Users, Package, ShoppingBag, TrendingUp, Clock,
  DollarSign, Calendar, BarChart, Activity, AlertTriangle, CheckCircle,
  Database, Store
} from "lucide-react"
import Link from "next/link"

export default function AdminDashboardPage() {
  const { user } = useAuth()
  const { currentShop, loading: shopLoading } = useShop()
  const [stats, setStats] = useState({
    totalOrders: 0,
    processingOrders: 0,
    totalProducts: 0,
    totalUsers: 0,
    revenue: 0,
    lowStockItems: 0,
    ordersToday: 0,
    revenueGrowth: 0
  })
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState('week')

  useEffect(() => {
    async function fetchStats() {
      if (!currentShop) return

      try {
        setLoading(true)

        // Fetch orders for the shop
        const orders = await getShopOrders(currentShop.id)

        // Fetch products for the shop
        const products = await getProducts(currentShop.id)

        // Fetch shop users
        const shopUsers = await getUserShops(currentShop.id)

        // Calculate stats
        const totalOrders = orders.length
        const processingOrders = orders.filter((order: { status: string }) => order.status === 'processing').length
        const totalProducts = products.length
        const totalUsers = shopUsers.length

        // Calculate revenue
        const revenue = orders.reduce((total: any, order: { total: any }) => total + (order.total || 0), 0)

        // Calculate low stock items
        const lowStockItems = products.filter((product: { in_stock: boolean; stock_quantity: number }) =>
          (product.in_stock === false) ||
          (typeof product.stock_quantity === 'number' && product.stock_quantity < 5)
        ).length

        // Calculate orders today
        const today = new Date().toISOString().split('T')[0]
        const ordersToday = orders.filter((order: { created_at: string }) =>
          order.created_at && order.created_at.startsWith(today)
        ).length

        // Calculate revenue growth (mock for now)
        const revenueGrowth = 12.5

        // Set the stats
        setStats({
          totalOrders,
          processingOrders,
          totalProducts,
          totalUsers,
          revenue,
          lowStockItems,
          ordersToday,
          revenueGrowth
        })
      } catch (error) {
        console.error("Error fetching admin stats:", error)
        // Use mock data on error
        setStats({
          totalOrders: 156,
          processingOrders: 23,
          totalProducts: 87,
          totalUsers: 42,
          revenue: 24650,
          lowStockItems: 5,
          ordersToday: 8,
          revenueGrowth: 12.5
        })
      } finally {
        setLoading(false)
      }
    }

    if (!shopLoading && currentShop) {
      fetchStats()
    }
  }, [currentShop, shopLoading])

  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Good morning"
    if (hour < 18) return "Good afternoon"
    return "Good evening"
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Dashboard</h1>
        <p className="text-gray-600">
          {getGreeting()}, {currentShop?.name || user?.full_name || 'Admin'}. Here's what's happening today.
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : (
        <>
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatCard
              title="Total Orders"
              value={stats.totalOrders}
              icon={<ShoppingBag className="h-8 w-8 text-blue-600" />}
              link="/admin/orders"
              trend={"+5% from last month"}
              trendUp={true}
            />
            <StatCard
              title="Processing Orders"
              value={stats.processingOrders}
              icon={<Clock className="h-8 w-8 text-yellow-600" />}
              link="/admin/orders?status=processing"
              badge={stats.processingOrders > 20 ? "High" : "Normal"}
              badgeColor={stats.processingOrders > 20 ? "yellow" : "green"}
            />
            <StatCard
              title="Today's Orders"
              value={stats.ordersToday}
              icon={<Calendar className="h-8 w-8 text-indigo-600" />}
              link="/admin/orders?period=today"
            />
            <StatCard
              title="Low Stock Items"
              value={stats.lowStockItems}
              icon={<AlertTriangle className="h-8 w-8 text-red-600" />}
              link="/admin/products?filter=low-stock"
              badge="Action Needed"
              badgeColor="red"
            />
          </div>

          {/* Revenue Section */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold">Revenue Overview</h2>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setTimeframe('week')}
                    className={`px-3 py-1 text-sm rounded-md ${
                      timeframe === 'week'
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Week
                  </button>
                  <button
                    onClick={() => setTimeframe('month')}
                    className={`px-3 py-1 text-sm rounded-md ${
                      timeframe === 'month'
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Month
                  </button>
                  <button
                    onClick={() => setTimeframe('year')}
                    className={`px-3 py-1 text-sm rounded-md ${
                      timeframe === 'year'
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    Year
                  </button>
                </div>
              </div>

              <div className="flex items-baseline mb-4">
                <span className="text-3xl font-bold mr-2">R {stats.revenue.toLocaleString()}</span>
                <span className="text-sm font-medium text-green-600 flex items-center">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  {stats.revenueGrowth}%
                </span>
              </div>

              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg mb-4">
                <BarChart className="h-8 w-8 text-gray-400" />
                <span className="ml-2 text-gray-500">Revenue chart visualization would go here</span>
              </div>

              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-gray-500 text-sm">Avg. Order Value</p>
                  <p className="font-semibold">R 158</p>
                </div>
                <div>
                  <p className="text-gray-500 text-sm">Conversion Rate</p>
                  <p className="font-semibold">3.2%</p>
                </div>
                <div>
                  <p className="text-gray-500 text-sm">Refund Rate</p>
                  <p className="font-semibold">0.8%</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">Quick Stats</h2>

              <div className="space-y-6">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600 text-sm">Total Products</span>
                    <span className="font-medium">{stats.totalProducts}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{ width: '70%' }}></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600 text-sm">Total Users</span>
                    <span className="font-medium">{stats.totalUsers}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600 text-sm">Order Completion</span>
                    <span className="font-medium">85%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-purple-600 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600 text-sm">Stock Levels</span>
                    <span className="font-medium">78%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '78%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
              <div className="grid grid-cols-2 gap-4">
                <ActionButton
                  title="Add Product"
                  icon={<Package className="h-5 w-5" />}
                  link="/admin/products/new"
                />
                <ActionButton
                  title="View Orders"
                  icon={<ShoppingBag className="h-5 w-5" />}
                  link="/admin/orders"
                />
                <ActionButton
                  title="Manage Users"
                  icon={<Users className="h-5 w-5" />}
                  link="/admin/users"
                />
                <ActionButton
                  title="View Reports"
                  icon={<BarChart className="h-5 w-5" />}
                  link="/admin/reports"
                />
              </div>
            </div>

            {/* System Status */}
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">System Status</h2>
              <div className="space-y-4">
                <Link href="/admin/database" className="block">
                  <StatusItem label="Database" status="Check Health" icon={<Database size={16} />} color="blue" />
                </Link>
                <StatusItem label="Authentication" status="Operational" icon={<CheckCircle size={16} />} color="green" />
                <StatusItem label="Storage" status="Operational" icon={<CheckCircle size={16} />} color="green" />
                <StatusItem label="Payment Processing" status="Operational" icon={<CheckCircle size={16} />} color="green" />
              </div>
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Last updated</span>
                  <span className="text-sm text-gray-700">Today at {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Recent Activity</h2>
              <Link href="/admin/activity" className="text-sm text-blue-600 hover:text-blue-800">
                View all
              </Link>
            </div>
            <div className="space-y-4">
              <ActivityItem
                title="New order received"
                description="Order #12345 for R 450.00"
                time="10 minutes ago"
                icon={<ShoppingBag className="h-5 w-5 text-blue-600" />}
              />
              <ActivityItem
                title="Low stock alert"
                description="Product 'Milk 1L' is running low"
                time="1 hour ago"
                icon={<AlertTriangle className="h-5 w-5 text-yellow-600" />}
              />
              <ActivityItem
                title="Payment received"
                description="Payment for order #12340 confirmed"
                time="2 hours ago"
                icon={<DollarSign className="h-5 w-5 text-green-600" />}
              />
              <ActivityItem
                title="New user registered"
                description="User 'John's Shop' created an account"
                time="3 hours ago"
                icon={<Users className="h-5 w-5 text-purple-600" />}
              />
            </div>
          </div>
        </>
      )}
    </div>
  )
}

function StatCard({
  title,
  value,
  icon,
  link,
  trend,
  trendUp,
  badge,
  badgeColor
}: {
  title: string;
  value: number;
  icon: React.ReactNode;
  link: string;
  trend?: string;
  trendUp?: boolean;
  badge?: string;
  badgeColor?: string;
}) {
  return (
    <Link href={link} className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start">
        <div>
          <div className="flex items-center">
            <p className="text-gray-500 text-sm mb-1">{title}</p>
            {badge && (
              <span className={`ml-2 px-2 py-0.5 text-xs rounded-full bg-${badgeColor}-100 text-${badgeColor}-800`}>
                {badge}
              </span>
            )}
          </div>
          <p className="text-2xl font-bold">{value}</p>
          {trend && (
            <p className={`text-xs mt-1 ${trendUp ? 'text-green-600' : 'text-red-600'}`}>
              {trend}
            </p>
          )}
        </div>
        {icon}
      </div>
    </Link>
  )
}

function ActionButton({ title, icon, link }: { title: string; icon: React.ReactNode; link: string }) {
  return (
    <Link
      href={link}
      className="flex items-center justify-center gap-2 bg-blue-50 hover:bg-blue-100 text-blue-700 p-3 rounded-md transition-colors"
    >
      {icon}
      <span>{title}</span>
    </Link>
  )
}

function StatusItem({ label, status, icon, color }: { label: string; status: string; icon: React.ReactNode; color: string }) {
  return (
    <div className="flex justify-between items-center">
      <span className="text-gray-600">{label}</span>
      <span className={`text-${color}-600 font-medium flex items-center`}>
        {icon}
        <span className="ml-1">{status}</span>
      </span>
    </div>
  )
}

function ActivityItem({ title, description, time, icon }: { title: string; description: string; time: string; icon: React.ReactNode }) {
  return (
    <div className="flex items-start">
      <div className="mr-4 mt-1">
        {icon}
      </div>
      <div className="flex-1">
        <h3 className="text-sm font-medium">{title}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
      <div className="text-xs text-gray-400">{time}</div>
    </div>
  )
}
