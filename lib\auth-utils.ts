// "use server"

// import { createServerClient } from "@/utils/supabase/client"

// /**
//  * Get the current user ID from the session
//  * Returns null if not authenticated
//  */
// export async function getUserId(): Promise<string | null> {
//   try {
//     const supabase = await createServerClient()
//     const { data, error } = await supabase.auth.getUser()

//     if (error || !data.user) {
//       return null
//     }

//     return data.user.id
//   } catch (error) {
//     return null
//   }
// }

// /**
//  * Check if the current user is an admin
//  * Returns false if not authenticated or not an admin
//  */
// export async function isUserAdmin(): Promise<boolean> {
//   try {
//     const userId = await getUserId()

//     if (!userId) {
//       return false
//     }

//     const supabase = await createServerClient()
//     const { data, error } = await supabase
//       .from("users")
//       .select("is_admin")
//       .eq("id", userId)
//       .single()

//     if (error || !data) {
//       return false
//     }

//     return !!data.is_admin
//   } catch (error) {
//     return false
//   }
// }
