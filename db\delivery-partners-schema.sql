-- Create delivery partners table
CREATE TABLE IF NOT EXISTS delivery_partners (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  mobile TEXT NOT NULL UNIQUE,
  vehicle_type TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create delivery assignments table
CREATE TABLE IF NOT EXISTS delivery_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  partner_id UUID NOT NULL REFERENCES delivery_partners(id),
  status TEXT NOT NULL DEFAULT 'Assigned', -- Assigned, Picked Up, Out for Delivery, Delivered, Failed
  pickup_time TIMESTAMP WITH TIME ZONE,
  delivery_time TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(order_id)
);

-- Enable RLS on new tables
ALTER TABLE delivery_partners ENABLE ROW LEVEL SECURITY;
ALTER TABLE delivery_assignments ENABLE ROW LEVEL SECURITY;

-- RLS policies for delivery partners
CREATE POLICY delivery_partners_admin_read ON delivery_partners
  FOR SELECT USING (is_admin());

CREATE POLICY delivery_partners_admin_insert ON delivery_partners
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY delivery_partners_admin_update ON delivery_partners
  FOR UPDATE USING (is_admin());

CREATE POLICY delivery_partners_admin_delete ON delivery_partners
  FOR DELETE USING (is_admin());

-- RLS policies for delivery assignments
CREATE POLICY delivery_assignments_admin_read ON delivery_assignments
  FOR SELECT USING (is_admin());

CREATE POLICY delivery_assignments_admin_insert ON delivery_assignments
  FOR INSERT WITH CHECK (is_admin());

CREATE POLICY delivery_assignments_admin_update ON delivery_assignments
  FOR UPDATE USING (is_admin());
