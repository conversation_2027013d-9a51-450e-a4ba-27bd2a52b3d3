-- Create receipts table
CREATE TABLE IF NOT EXISTS receipts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  payment_id UUID REFERENCES payments(id),
  receipt_number TEXT NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  issued_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  confirmed_by_customer BOOLEAN DEFAULT FALSE,
  confirmed_at TIMESTAMP WITH TIME ZONE,
  receipt_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS receipts_order_id_idx ON receipts(order_id);
CREATE INDEX IF NOT EXISTS receipts_payment_id_idx ON receipts(payment_id);
CREATE INDEX IF NOT EXISTS receipts_receipt_number_idx ON receipts(receipt_number);

-- Enable RLS on receipts table
ALTER TABLE receipts ENABLE ROW LEVEL SECURITY;

-- RLS policies for receipts
CREATE POLICY receipts_select_policy ON receipts
  FOR SELECT
  USING (
    order_id IN (
      SELECT id FROM orders WHERE user_id = auth.uid()
    )
  );

-- Policy for admins to manage all receipts
CREATE POLICY receipts_admin_policy ON receipts
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid() AND users.is_admin = TRUE
    )
  );

-- Policy for service role to manage all receipts
CREATE POLICY receipts_service_policy ON receipts
  USING (auth.role() = 'service_role');