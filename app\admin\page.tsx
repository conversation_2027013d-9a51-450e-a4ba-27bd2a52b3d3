import { getDashboardStats } from "@/lib/admin-service"
import { Users, Package, ShoppingBag, DollarSign, BarChart3, Scan, AlertTriangle, FileText } from "lucide-react"
import Link from "next/link"
import { createClient } from '@/utils/supabase/server'

export default async function AdminDashboard() {
  const supabase = createClient()
  const stats = await getDashboardStats(supabase, undefined, { includeRevenue: true }) // The third argument is options, not a shopId

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <Users size={24} />
            </div>
            <div className="ml-4">
              <h2 className="text-gray-500 text-sm">Total Users</h2>
              <p className="text-2xl font-bold">{stats.userCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <Package size={24} />
            </div>
            <div className="ml-4">
              <h2 className="text-gray-500 text-sm">Total Products</h2>
              <p className="text-2xl font-bold">{stats.productCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-orange-100 text-orange-600">
              <ShoppingBag size={24} />
            </div>
            <div className="ml-4">
              <h2 className="text-gray-500 text-sm">Total Orders</h2>
              <p className="text-2xl font-bold">{stats.orderCount}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <DollarSign size={24} />
            </div>
            <div className="ml-4">
              <h2 className="text-gray-500 text-sm">Total Revenue</h2>
              <p className="text-2xl font-bold">R {stats.totalRevenue.toFixed(2)}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-xl font-bold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/admin/products/new"
            className="bg-blue-600 text-white p-4 rounded-md hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <Package size={20} />
            Add New Product
          </Link>
          <Link
            href="/admin/categories/new"
            className="bg-green-600 text-white p-4 rounded-md hover:bg-green-700 transition-colors flex items-center gap-2"
          >
            <FileText size={20} />
            Add New Category
          </Link>
          <Link
            href="/admin/barcode-scan"
            className="bg-purple-600 text-white p-4 rounded-md hover:bg-purple-700 transition-colors flex items-center gap-2"
          >
            <Scan size={20} />
            Scan Barcode
          </Link>
          <Link
            href="/admin/stock-sheet"
            className="bg-orange-600 text-white p-4 rounded-md hover:bg-orange-700 transition-colors flex items-center gap-2"
          >
            <FileText size={20} />
            Stock Sheet
          </Link>
        </div>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-xl font-bold mb-4">Advanced Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Link
            href="/admin/analytics"
            className="bg-indigo-600 text-white p-4 rounded-md hover:bg-indigo-700 transition-colors flex items-center gap-2"
          >
            <BarChart3 size={20} />
            Analytics Dashboard
          </Link>
          <Link
            href="/admin/inventory-alerts"
            className="bg-red-600 text-white p-4 rounded-md hover:bg-red-700 transition-colors flex items-center gap-2"
          >
            <AlertTriangle size={20} />
            Inventory Alerts
          </Link>
          <Link
            href="/admin/barcode-scan/batch"
            className="bg-teal-600 text-white p-4 rounded-md hover:bg-teal-700 transition-colors flex items-center gap-2"
          >
            <Package size={20} />
            Batch Stock Update
          </Link>
        </div>
      </div>
    </div>
  )
}
