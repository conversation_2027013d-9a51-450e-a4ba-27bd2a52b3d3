"use client"

import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import Link from "next/link"
import { WifiOff, RefreshCw, Home, ShoppingBag } from "lucide-react"
import { useEffect, useState } from "react"

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    // Check online status on mount
    setIsOnline(navigator.onLine)

    // Add event listeners for online/offline events
    const handleOnline = () => {
      setIsOnline(true)
      // Attempt to reload cached resources when coming back online
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'REFRESH_CACHE'
        });
      }
    }
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <>
      <MainHeader />
      <div className="flex-grow p-4 flex flex-col items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="bg-blue-100 text-blue-700 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
            <WifiOff size={32} />
          </div>

          <h1 className="text-2xl font-bold mb-2">You're Offline</h1>

          <p className="text-gray-600 mb-6">
            {isOnline
              ? "You're back online! Click the button below to refresh the page."
              : "It looks like you're currently offline. Some features may not be available until you reconnect."}
          </p>

          {isOnline ? (
            <button
              onClick={handleRefresh}
              className="bg-green-600 text-white py-3 px-6 rounded-md font-medium hover:bg-green-700 transition-colors w-full flex items-center justify-center gap-2"
            >
              <RefreshCw size={18} />
              Refresh Page
            </button>
          ) : (
            <div className="space-y-3">
              <p className="text-sm text-gray-500 mb-4">
                You can still access cached pages while offline:
              </p>

              <div className="grid grid-cols-2 gap-3">
                <Link
                  href="/"
                  className="bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <Home size={18} />
                  Home
                </Link>

                <Link
                  href="/products"
                  className="bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <ShoppingBag size={18} />
                  Products
                </Link>
              </div>

              <button
                onClick={handleRefresh}
                className="bg-gray-200 text-gray-800 py-3 px-6 rounded-md font-medium hover:bg-gray-300 transition-colors w-full flex items-center justify-center gap-2 mt-4"
              >
                <RefreshCw size={18} />
                Try Again
              </button>
            </div>
          )}
        </div>

        <div className="mt-8 text-sm text-gray-500 max-w-md text-center">
          <p>
            Spaza Smart Order stores some content for offline use.
            When you're back online, any pending orders will be automatically synchronized.
          </p>
        </div>
      </div>
      <MainFooter />
    </>
  )
}
