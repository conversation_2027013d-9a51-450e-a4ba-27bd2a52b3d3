/* Dark mode styles */
.dark {
  --background: #121212;
  --foreground: #ffffff;
  --card: #1e1e1e;
  --card-foreground: #ffffff;
  --popover: #1e1e1e;
  --popover-foreground: #ffffff;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #2d2d2d;
  --secondary-foreground: #ffffff;
  --muted: #2d2d2d;
  --muted-foreground: #a1a1aa;
  --accent: #2d2d2d;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #2d2d2d;
  --input: #2d2d2d;
  --ring: #3b82f6;
}

/* Dark mode specific styles */
.dark body {
  background-color: var(--background);
  color: var(--foreground);
}

.dark .bg-white {
  background-color: var(--card);
}

.dark .text-gray-900 {
  color: var(--foreground);
}

.dark .text-gray-700,
.dark .text-gray-600,
.dark .text-gray-500 {
  color: var(--muted-foreground);
}

.dark .border-gray-200,
.dark .border-gray-300 {
  border-color: var(--border);
}

.dark .bg-gray-100,
.dark .bg-gray-200 {
  background-color: var(--secondary);
}

.dark .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1);
}

.dark .bg-red-50 {
  background-color: rgba(239, 68, 68, 0.1);
}

.dark .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1);
}

/* Improved contrast for interactive elements in dark mode */
.dark button:not([disabled]),
.dark [role="button"]:not([disabled]),
.dark a[href] {
  transition: all 0.2s ease;
}

.dark button:not([disabled]):hover,
.dark [role="button"]:not([disabled]):hover,
.dark a[href]:hover {
  filter: brightness(1.2);
}

/* Improved focus styles for dark mode */
.dark *:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}
