"use client"

import { create<PERSON>ontext, useContext, useEffect, useState, useCallback, ReactNode, useRef, useMemo } from "react"
import { devLog, logError } from "../utils/logger" 
import { useShop } from "../contexts/shop-context"   
import { getSupabaseClient } from "../lib/supabase"  
import { Database } from '../lib/database.types' // Ensure this path is correct and Database is exported
import { AuthChangeEvent, Session, RealtimePostgresChangesPayload, SupabaseClient, RealtimeChannel } from '@supabase/supabase-js'

// Type-safe logger wrapper
const safeLog = (...args: any[]) => {
  if (typeof devLog === 'function') {
    devLog(...args)
  } else {
    console.log(...args) 
  }
}

// Define aliases for your Row types
type CategoryRow = Database['public']['Tables']['categories']['Row'];
type ProductRow = Database['public']['Tables']['products']['Row'];

// Mock data (adjust to match your exact table structures if needed)
// Using the aliases here too for consistency, though not strictly necessary for the fix itself
const mockCategories: CategoryRow[] = [
  { id: 'mock-cat-1', name: 'Mock Beverages', created_at: new Date().toISOString(), updated_at: new Date().toISOString() /* other fields if any */ },
  { id: 'mock-cat-2', name: 'Mock Snacks', created_at: new Date().toISOString(), updated_at: new Date().toISOString() /* other fields if any */ },
];

// Make sure mockProducts correctly implements Partial<ProductRow> or ProductRow
const mockProducts: Array<Partial<ProductRow> & { id: string, name: string, category_id: string }> = [
  { id: 'mock-prod-1', name: 'Mock Cola', price: 10.0, category_id: 'mock-cat-1', in_stock: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString(), shop_id: 'mock-shop' /* if shop_id is required */ },
  { id: 'mock-prod-2', name: 'Mock Chips', price: 15.0, category_id: 'mock-cat-2', in_stock: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString(), shop_id: 'mock-shop' /* if shop_id is required */ },
];


// Types for Context
// Use the alias in the extends clause
interface CategoryWithProducts extends CategoryRow {
  products: ProductRow[];
}

interface RealTimeContextType {
  products: ProductRow[];
  categories: CategoryRow[];
  categoriesWithProducts: CategoryWithProducts[];
  loading: boolean;
  error: string | null;
  isMockData: boolean;
  subscriptionsActive: boolean;
  fetchData: () => Promise<void>; 
}

const RealTimeContext = createContext<RealTimeContextType | undefined>(undefined)

export const useRealTimeData = () => {
  const context = useContext(RealTimeContext)
  if (context === undefined) {
    throw new Error('useRealTimeData must be used within a RealTimeProvider')
  }
  return context
}

export function RealTimeProvider({ children }: { children: ReactNode }) {
  const { currentShop } = useShop()
  const [supabase, setSupabase] = useState<SupabaseClient<Database> | null>(null)
  
  const [products, setProducts] = useState<ProductRow[]>([])
  const [categories, setCategories] = useState<CategoryRow[]>([])
  
  const [loading, setLoading] = useState(true) 
  const [error, setError] = useState<string | null>(null)
  const [isMockData, setIsMockData] = useState(false)
  const [subscriptionsActive, setSubscriptionsActive] = useState(false)
  
  const [activeChannels, setActiveChannels] = useState<RealtimeChannel[]>([])
  const fetchDataRef = useRef<() => Promise<void>>(async () => { /* no-op initial, will be updated */ })

  const initializeMockData = useCallback((reason?: string) => {
    safeLog(`RealTimeContext: Initializing with mock data. Reason: ${reason || 'N/A'}`)
    setCategories(mockCategories as CategoryRow[]); // Cast if mock doesn't fully match
    setProducts(mockProducts as ProductRow[]);   // Cast if mock doesn't fully match
    setIsMockData(true);
    setLoading(false); 
  }, [])

  const fetchData = useCallback(async () => {
    if (!supabase) {
      safeLog("RealTimeContext: fetchData called but Supabase client not available.");
      setError("Supabase client not available."); 
      setLoading(false);
      return;
    }
    if (!currentShop || !currentShop.id) {
      safeLog("RealTimeContext: fetchData - No current shop, clearing data.");
      setProducts([]);
      setCategories([]);
      setIsMockData(false);
      setLoading(false);
      setError(currentShop === null ? "No shop selected. Please select a shop to view data." : null);
      return;
    }

    safeLog(`RealTimeContext: Fetching data for shop ${currentShop.id}`);
    setLoading(true);
    setError(null); 
    setIsMockData(false); 

    try {
      const { data: categoriesData, error: categoriesError } = await supabase
        .from('categories')
        .select('*')
        .order('name');

      if (categoriesError) throw categoriesError;

      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select('*')
        .eq('shop_id', currentShop.id)
        .order('name');

      if (productsError) throw productsError;

      setCategories((categoriesData || []) as CategoryRow[]);
      setProducts((productsData || []) as ProductRow[]);
      setLoading(false);
      safeLog("RealTimeContext: Data fetched successfully.");
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      logError('RealTimeContext: Failed to fetch data:', errorMessage, err);
      setError(`Failed to load data: ${errorMessage}. Displaying sample data.`);
      initializeMockData('Data fetch failed'); 
    }
  }, [supabase, currentShop, initializeMockData]); 

  useEffect(() => {
    fetchDataRef.current = fetchData;
  }, [fetchData]);

  useEffect(() => {
    const client = getSupabaseClient() as SupabaseClient<Database>;
    setSupabase(client);
    safeLog("RealTimeContext: Supabase client initialized.");

    const { data: { subscription: authListenerSubscription } } = client.auth.onAuthStateChange((event: AuthChangeEvent, session: Session | null) => {
      safeLog(`RealTimeContext: Auth state changed - Event: ${event}`);
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === "USER_UPDATED") {
        fetchDataRef.current();
      }
    });

    return () => {
      authListenerSubscription?.unsubscribe();
      safeLog("RealTimeContext: Auth listener unsubscribed.");
    };
  }, []); 

  const setupSubscriptions = useCallback(async () => {
    if (!supabase || !currentShop || !currentShop.id) {
      safeLog('RealTimeContext: Pre-conditions not met for setting up subscriptions (no supabase/shop).');
      return { newChannels: [], success: false };
    }
    const productsChannelName = `products-changes-shop-${currentShop.id}`;
    if (activeChannels.some(ch => ch.topic === productsChannelName)) {
        safeLog(`RealTimeContext: Subscriptions for shop ${currentShop.id} seem to be already active.`);
        setSubscriptionsActive(true); 
        return { newChannels: [...activeChannels], success: true };
    }

    safeLog(`RealTimeContext: Setting up new subscriptions for shop ${currentShop.id}`);
    const tempNewChannels: RealtimeChannel[] = [];

    try {
      const categoriesChannel = supabase.channel('categories-changes-realtime')
        .on('postgres_changes', {
          event: '*', schema: 'public', table: 'categories',
        } as any, 
        (payload: RealtimePostgresChangesPayload<any>) => {
          safeLog('RealTimeContext: Categories changed, refetching data.', payload);
          fetchDataRef.current();
        });
      tempNewChannels.push(categoriesChannel);

      const productsShopChannel = supabase.channel(productsChannelName)
        .on('postgres_changes', {
          event: '*', schema: 'public', table: 'products', filter: `shop_id=eq.${currentShop.id}`,
        } as any, 
        (payload: RealtimePostgresChangesPayload<any>) => {
          safeLog(`RealTimeContext: Products for shop ${currentShop.id} changed, refetching data.`, payload);
          fetchDataRef.current();
        });
      tempNewChannels.push(productsShopChannel);
      
      await Promise.all(tempNewChannels.map(ch => new Promise<void>((resolve, reject) => {
        ch.subscribe(status => {
          if (status === 'SUBSCRIBED') {
            safeLog(`RealTimeContext: Successfully subscribed to ${ch.topic}`);
            resolve();
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            logError(`RealTimeContext: Failed to subscribe to ${ch.topic}`, status);
            reject(new Error(`Subscription failed for ${ch.topic} with status ${status}`));
          }
        });
      })));

      setSubscriptionsActive(true);
      setError(null); 
      safeLog('RealTimeContext: Real-time subscriptions active.');
      return { newChannels: tempNewChannels, success: true };

    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      logError('RealTimeContext: Failed to set up some or all subscriptions:', errorMessage, err);
      setError('Failed to set up real-time updates. Data may be stale.');
      setSubscriptionsActive(false);
      tempNewChannels.forEach(ch => supabase.removeChannel(ch).catch(e => logError("Error removing partially created channel", e)));
      return { newChannels: [], success: false };
    }
  }, [supabase, currentShop, activeChannels]); 

  useEffect(() => {
    if (!supabase) {
      safeLog("RealTimeContext MainEffect: Supabase client not yet available.");
      setLoading(true); 
      return; 
    }

    let isEffectMounted = true; 

    const manageDataAndSubscriptions = async () => {
      if (activeChannels.length > 0) {
        safeLog(`RealTimeContext MainEffect: Removing ${activeChannels.length} previous subscriptions.`);
        await Promise.all(activeChannels.map(ch => supabase.removeChannel(ch)
          .catch(e => logError(`Error removing channel ${ch.topic} on cleanup`, e)))
        );
        if (isEffectMounted) {
            setActiveChannels([]);
            setSubscriptionsActive(false);
        }
      }

      if (!currentShop || !currentShop.id) {
        safeLog("RealTimeContext MainEffect: No current shop. Data will be cleared by fetchData.");
        fetchDataRef.current(); 
        return;
      }

      await fetchDataRef.current(); 
      
      if (isEffectMounted && supabase && currentShop && currentShop.id) {
        const subResult = await setupSubscriptions();
        if (isEffectMounted && subResult.success) {
            setActiveChannels(subResult.newChannels);
        } else if (isEffectMounted && !subResult.success) {
            setActiveChannels([]);
            setSubscriptionsActive(false);
        }
      }
    };

    manageDataAndSubscriptions();

    return () => {
      isEffectMounted = false;
      safeLog("RealTimeContext MainEffect: Cleaning up on unmount or dependency change.");
      activeChannels.forEach(channel => {
         if (supabase && supabase.removeChannel) { 
            supabase.removeChannel(channel).catch(e => logError(`Error removing channel ${channel.topic} on final unmount`, e));
         }
      });
    };
  }, [supabase, currentShop, setupSubscriptions]); 

  const categoriesWithProducts = useMemo<CategoryWithProducts[]>(() => {
    if (isMockData) { 
        return (mockCategories as CategoryRow[]).map(cat => ({
            ...cat,
            products: (mockProducts as ProductRow[]).filter(p => p.category_id === cat.id)
        }));
    }
    return categories.map((category) => ({
      ...category,
      products: products.filter((product) => product.category_id === category.id),
    }));
  }, [categories, products, isMockData]);

  return (
    <RealTimeContext.Provider
      value={{
        products,
        categories,
        categoriesWithProducts,
        loading,
        error,
        isMockData,
        subscriptionsActive,
        fetchData,
      }}
    >
      {children}
    </RealTimeContext.Provider>
  )
}