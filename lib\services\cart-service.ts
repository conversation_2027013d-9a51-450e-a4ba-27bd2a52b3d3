// import { createClient } from '@/utils/supabase/client';
// import { devLog, logError } from '@/utils/logger';
// import { Database } from '@/lib/database.types';

// export type CartItem = Database['public']['Tables']['cart_items']['Row'] & {
//   products?: Database['public']['Tables']['products']['Row']
// };

// /**
//  * Get cart items for a user
//  * @param userId User ID
//  * @returns Array of cart items with product details
//  */
// export async function getCartItems(userId: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('cart_items')
//       .select(`
//         *,
//         products (*)
//       `)
//       .eq('user_id', userId);
    
//     if (error) {
//       logError(`Error fetching cart items for user ${userId}:`, error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError(`Unexpected error fetching cart items for user ${userId}:`, error);
//     return [];
//   }
// }

// /**
//  * Add an item to the cart
//  * @param userId User ID
//  * @param productId Product ID
//  * @param quantity Quantity
//  * @returns Result object with success status and cart item ID or error
//  */
// export async function addToCart(userId: string, productId: string, quantity: number) {
//   const supabase = createClient();
  
//   try {
//     // Check if item already exists in cart
//     const { data: existingItems } = await supabase
//       .from('cart_items')
//       .select('id, quantity')
//       .eq('user_id', userId)
//       .eq('product_id', productId)
//       .limit(1);
    
//     if (existingItems && existingItems.length > 0) {
//       // Update quantity if item already exists
//       const newQuantity = existingItems[0].quantity + quantity;
      
//       const { error } = await supabase
//         .from('cart_items')
//         .update({ quantity: newQuantity, updated_at: new Date().toISOString() })
//         .eq('id', existingItems[0].id);
      
//       if (error) {
//         logError(`Error updating cart item ${existingItems[0].id}:`, error);
//         return { success: false, error: "Failed to update cart item" };
//       }
      
//       return { success: true, cartItemId: existingItems[0].id };
//     } else {
//       // Add new item to cart
//       const { data, error } = await supabase
//         .from('cart_items')
//         .insert({
//           user_id: userId,
//           product_id: productId,
//           quantity,
//           created_at: new Date().toISOString(),
//           updated_at: new Date().toISOString()
//         })
//         .select()
//         .single();
      
//       if (error) {
//         logError("Error adding item to cart:", error);
//         return { success: false, error: "Failed to add item to cart" };
//       }
      
//       return { success: true, cartItemId: data.id };
//     }
//   } catch (error) {
//     logError("Unexpected error adding item to cart:", error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Update cart item quantity
//  * @param cartItemId Cart item ID
//  * @param quantity New quantity
//  * @returns Result object with success status or error
//  */
// export async function updateCartItemQuantity(cartItemId: string, quantity: number) {
//   const supabase = createClient();
  
//   try {
//     if (quantity <= 0) {
//       // Remove item if quantity is 0 or negative
//       return removeFromCart(cartItemId);
//     }
    
//     const { error } = await supabase
//       .from('cart_items')
//       .update({ 
//         quantity, 
//         updated_at: new Date().toISOString() 
//       })
//       .eq('id', cartItemId);
    
//     if (error) {
//       logError(`Error updating cart item ${cartItemId}:`, error);
//       return { success: false, error: "Failed to update cart item" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error updating cart item ${cartItemId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Remove an item from the cart
//  * @param cartItemId Cart item ID
//  * @returns Result object with success status or error
//  */
// export async function removeFromCart(cartItemId: string) {
//   const supabase = createClient();
  
//   try {
//     const { error } = await supabase
//       .from('cart_items')
//       .delete()
//       .eq('id', cartItemId);
    
//     if (error) {
//       logError(`Error removing cart item ${cartItemId}:`, error);
//       return { success: false, error: "Failed to remove item from cart" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error removing cart item ${cartItemId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Clear the cart for a user
//  * @param userId User ID
//  * @returns Result object with success status or error
//  */
// export async function clearCart(userId: string) {
//   const supabase = createClient();
  
//   try {
//     const { error } = await supabase
//       .from('cart_items')
//       .delete()
//       .eq('user_id', userId);
    
//     if (error) {
//       logError(`Error clearing cart for user ${userId}:`, error);
//       return { success: false, error: "Failed to clear cart" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error clearing cart for user ${userId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Get cart total for a user
//  * @param userId User ID
//  * @returns Cart total amount
//  */
// export async function getCartTotal(userId: string) {
//   const cartItems = await getCartItems(userId);
  
//   return cartItems.reduce((total, item) => {
//     const price = item.products?.price || 0;
//     return total + (price * item.quantity);
//   }, 0);
// }



