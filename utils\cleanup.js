/**
 * This script removes deprecated files that are no longer needed
 * after consolidating the Supabase client implementations.
 */

const fs = require('fs');
const path = require('path');

// List of files to be removed
const filesToRemove = [
  'lib/supabase-client.ts',
  'utils/supabase/client.ts',
  'utils/supabase/server.ts',
  'lib/supabase.ts',
  'contexts/auth-context-simplified.tsx'
];

// Function to remove a file if it exists
function removeFileIfExists(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`✅ Removed: ${filePath}`);
    } catch (error) {
      console.error(`❌ Error removing ${filePath}:`, error.message);
    }
  } else {
    console.log(`⚠️ File not found: ${filePath}`);
  }
}

// Remove each file in the list
console.log('Cleaning up deprecated files...');
filesToRemove.forEach(removeFileIfExists);
console.log('Cleanup complete!');
