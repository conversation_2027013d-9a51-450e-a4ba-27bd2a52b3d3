"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import type { User, UserWithShopRoles } from "@/lib/types"
import { getUserWithShopRoles } from "@/utils/role-utils"
import { devLog, logError } from "@/utils/logger"
import { createClient } from '@/utils/supabase/client'
import { Session, AuthError } from '@supabase/supabase-js'
import { formatMobile, isValidMobile } from '@/utils/direct-login'

interface AuthContextType {
  user: User | null
  userWithRoles: UserWithShopRoles | null
  loading: boolean
  isAuthenticated: boolean
  signOut: () => Promise<void>
  login: (mobile: string, password: string) => Promise<{ success: boolean; error?: string }>
  register: (mobile: string, password: string, shopName?: string, address?: string) => Promise<{ success: boolean; error?: string }>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  userWithRoles: null,
  loading: true,
  isAuthenticated: false,
  signOut: async () => {},
  login: async (mobile: string, password: string) => ({ success: false, error: "Authentication service not initialized" }),
  register: async () => ({ success: false, error: "Authentication service not initialized" }),
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userWithRoles, setUserWithRoles] = useState<UserWithShopRoles | null>(null)
  const [loading, setLoading] = useState(true)
  const [supabase, setSupabase] = useState<any>(null)

  // Initialize Supabase client
  useEffect(() => {
    const initializeSupabase = async () => {
      try {
        const client = createClient()
        setSupabase(client)

        // Check for existing session
        const { data: { session } } = await client.auth.getSession()
        if (session?.user) {
          setUser({...session.user, is_admin: session.user.user_metadata?.is_admin || false})
          loadUserWithRoles(session.user.id)
        }
      } catch (error) {
        logError('Failed to initialize Supabase client:', error)
      }
    }

    initializeSupabase()
  }, [])

  // Load user with roles
  const loadUserWithRoles = async (userId: string) => {
    try {
      const result = await getUserWithShopRoles(userId)
      setUserWithRoles(result)
    } catch (error) {
      logError('Failed to load user roles:', error)
    }
  }

  // Handle login
  const login = async (mobile: string, password: string) => {
    try {
      if (!supabase) {
        throw new Error('Supabase client not initialized')
      }

      // Format and validate mobile number
      const formattedMobile = formatMobile(mobile)
      if (!isValidMobile(formattedMobile)) {
        throw new Error('Please enter a valid South African mobile number')
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email: formattedMobile,
        password
      })

      if (error) {
        throw new Error(error.message)
      }

      if (data?.user) {
        setUser(data.user)
        await loadUserWithRoles(data.user.id)
        return { success: true }
      }

      throw new Error('Login failed - no user returned')
    } catch (error) {
      logError('Login failed:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Login failed' }
    }
  }

  // Handle sign out
  const signOut = async () => {
    if (!supabase) {
      throw new Error('Supabase client not initialized')
    }

    try {
      await supabase.auth.signOut()
      setUser(null)
      setUserWithRoles(null)
    } catch (error) {
      logError('Sign out failed:', error)
    }
  }

  // Handle registration
  const register = async (mobile: string, password: string, shopName?: string, address?: string) => {
    try {
      if (!supabase) {
        throw new Error('Supabase client not initialized')
      }

      const { data, error } = await supabase.auth.signUp({
        email: mobile,
        password
      })

      if (error) {
        throw new Error(error.message)
      }

      if (data?.user) {
        // Create user profile
        const { error: profileError } = await supabase
          .from('users')
          .insert([
            {
              id: data.user.id,
              mobile,
              shop_name: shopName,
              address,
              created_at: new Date().toISOString()
            }
          ])

        if (profileError) {
          throw profileError
        }

        setUser({...data.user, is_admin: data.user.user_metadata?.is_admin || false})
        await loadUserWithRoles(data.user.id)
        return { success: true }
      }

      throw new Error('Registration failed - no user returned')
    } catch (error) {
      logError('Registration failed:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Registration failed' }
    }
  }

  // Listen for auth changes
  useEffect(() => {
    if (!supabase) return

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event: string, session: Session | null) => {
      try {
        if (session?.user) {
          setUser({...session.user, is_admin: session.user.user_metadata?.is_admin || false})
          loadUserWithRoles(session.user.id)
        } else {
          setUser(null)
          setUserWithRoles(null)
        }
      } catch (error) {
        logError('Auth state change error:', error)
      }
    })

    return () => subscription.unsubscribe()
  }, [supabase])

  // Set loading to false when initialization is complete
  useEffect(() => {
    if (supabase) {
      setLoading(false)
    }
  }, [supabase])

  return (
    <AuthContext.Provider
      value={{
        user,
        userWithRoles,
        loading,
        isAuthenticated: !!user,
        signOut,
        login,
        register
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}


