"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import { useAuth } from "./multi-shop-auth-context"
import { Shop, UserRoleType } from "@/lib/types"
import { getShopsWithUserRole, getDefaultShopForUser } from "@/utils/role-utils"
import { devLog, logError } from "@/utils/logger"

interface ShopContextType {
  currentShop: Shop | null
  userShops: { shop_id: string; shop_name: string; role: UserRoleType }[]
  loading: boolean
  error: boolean
  switchShop: (shopId: string) => Promise<void>
  userRole: UserRoleType | null
  hasRole: (roles: UserRoleType[]) => boolean
  refreshShops: () => Promise<void>
}

const ShopContext = createContext<ShopContextType>({
  currentShop: null,
  userShops: [],
  loading: true,
  error: false,
  switchShop: async () => {},
  userRole: null,
  hasRole: () => false,
  refreshShops: async () => {},
})

export const useShop = () => useContext(ShopContext)

export function ShopProvider({ children }: { children: ReactNode }) {
  const { user, loading: authLoading } = useAuth()
  const [currentShop, setCurrentShop] = useState<Shop | null>(null)
  const [userShops, setUserShops] = useState<{ shop_id: string; shop_name: string; role: UserRoleType }[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(false)
  const [userRole, setUserRole] = useState<UserRoleType | null>(null)
  const [supabase, setSupabase] = useState<any>(null)

  // Initialize Supabase client only on the client side
  useEffect(() => {
    const initializeSupabase = async () => {
      try {
        // Dynamically import the createBrowserClient to ensure it only runs on the client
        const { createBrowserClient } = await import('@/utils/supabase/client')
        setSupabase(createBrowserClient())
      } catch (error) {
        logError('Failed to initialize Supabase client:', error)
      }
    }

    initializeSupabase()
  }, [])

  // Load user's shops when user is authenticated
  useEffect(() => {
    if (authLoading || !user || !supabase) {
      return
    }

    loadUserShops()
  }, [user, authLoading, supabase])

  // Load user shops
  const loadUserShops = async () => {
    try {
      setLoading(true)
      setError(false)

      // Get all shops where user has a role
      const shops = await getShopsWithUserRole(user!.id)
      setUserShops(shops)

      // Get current shop from localStorage or use default
      const storedShopId = localStorage.getItem('currentShopId')
      let targetShopId = storedShopId

      // If no stored shop or stored shop not in user's shops, get default
      if (!targetShopId || !shops.some(s => s.shop_id === targetShopId)) {
        targetShopId = await getDefaultShopForUser(user!.id)
      }

      // If we have a target shop, load it
      if (targetShopId) {
        await loadShopDetails(targetShopId)
      } else {
        setCurrentShop(null)
        setUserRole(null)
      }

      setLoading(false)
    } catch (error) {
      logError('Error loading user shops:', error)
      setError(true)
      setLoading(false)
    }
  }

  // Load shop details
  const loadShopDetails = async (shopId: string) => {
    try {
      if (!supabase) return null

      // Get shop details
      const { data: shop, error } = await supabase
        .from('shops')
        .select('*')
        .eq('id', shopId)
        .single()

      if (error) {
        throw error
      }

      // Get user's role in this shop
      const shopRole = userShops.find(s => s.shop_id === shopId)?.role || null

      setCurrentShop(shop)
      setUserRole(shopRole)

      // Store current shop in localStorage
      localStorage.setItem('currentShopId', shopId)

      return shop
    } catch (error) {
      logError('Error loading shop details:', error)
      return null
    }
  }

  // Switch to a different shop
  const switchShop = async (shopId: string) => {
    try {
      // Check if user has access to this shop
      if (!userShops.some(s => s.shop_id === shopId)) {
        throw new Error('User does not have access to this shop')
      }

      const shop = await loadShopDetails(shopId)

      if (!shop) {
        throw new Error('Failed to load shop details')
      }

      // Reload the page to refresh data for the new shop
      window.location.reload()

      return shop
    } catch (error) {
      logError('Error switching shop:', error)
      throw error
    }
  }

  // Check if user has any of the specified roles
  const hasRole = (roles: UserRoleType[]): boolean => {
    if (!userRole) return false
    return roles.includes(userRole)
  }

  // Refresh shops
  const refreshShops = async () => {
    await loadUserShops()
  }

  return (
    <ShopContext.Provider
      value={{
        currentShop,
        userShops,
        loading: loading || authLoading,
        error,
        switchShop,
        userRole,
        hasRole,
        refreshShops
      }}
    >
      {children}
    </ShopContext.Provider>
  )
}
