-- Make sure the users table has the mobile field
ALTER TABLE users ADD COLUMN IF NOT EXISTS mobile TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS shop_name TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS address TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_suspended BOOLEAN DEFAULT FALSE;

-- Create an index on the mobile field for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_mobile ON users(mobile);

-- Add a unique constraint to ensure mobile numbers are unique
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_mobile_key;
ALTER TABLE users ADD CONSTRAINT users_mobile_key UNIQUE (mobile);
