-- Insert categories if they don't exist
INSERT INTO categories (id, name, created_at)
VALUES 
  (uuid_generate_v4(), 'Groceries', NOW()),
  (uuid_generate_v4(), 'Beverages', NOW()),
  (uuid_generate_v4(), 'Household', NOW()),
  (uuid_generate_v4(), 'Snacks', NOW()),
  (uuid_generate_v4(), 'Personal Care', NOW())
ON CONFLICT (name) DO NOTHING;

-- Get category IDs for reference
WITH cat_ids AS (
  SELECT id, name FROM categories
  WHERE name IN ('Groceries', 'Beverages', 'Household', 'Snacks', 'Personal Care')
)

-- Insert sample products
INSERT INTO products (id, name, description, price, image_url, category_id, in_stock, created_at)
SELECT 
  uuid_generate_v4(),
  product_data.name,
  product_data.description,
  product_data.price,
  product_data.image_url,
  cat_ids.id,
  product_data.in_stock,
  NOW()
FROM (
  VALUES
    -- Groceries
    ('White Star Maize Meal 5kg', 'Premium quality maize meal', 65.00, '/placeholder.svg?height=60&width=60', 'Groceries', true),
    ('Tastic Rice 2kg', 'Premium quality rice', 45.00, '/placeholder.svg?height=60&width=60', 'Groceries', true),
    ('Ace Super Maize Meal 10kg', 'Super maize meal for the whole family', 95.00, '/placeholder.svg?height=60&width=60', 'Groceries', true),
    ('Sasko Cake Flour 2.5kg', 'All-purpose cake flour', 38.00, '/placeholder.svg?height=60&width=60', 'Groceries', true),
    
    -- Beverages
    ('Coca-Cola Can 300ml', 'Refreshing soft drink', 8.50, '/placeholder.svg?height=60&width=60', 'Beverages', true),
    ('Fanta Orange 2L', 'Orange flavored soft drink', 22.00, '/placeholder.svg?height=60&width=60', 'Beverages', true),
    ('Sprite 1.5L', 'Lemon-lime flavored soft drink', 18.00, '/placeholder.svg?height=60&width=60', 'Beverages', true),
    ('Stoney Ginger Beer 2L', 'Spicy ginger beer', 20.00, '/placeholder.svg?height=60&width=60', 'Beverages', false),
    
    -- Household
    ('Sunlight Dishwashing Liquid 750ml', 'Effective dishwashing liquid', 35.00, '/placeholder.svg?height=60&width=60', 'Household', true),
    ('Handy Andy Surface Cleaner 750ml', 'All-purpose surface cleaner', 28.00, '/placeholder.svg?height=60&width=60', 'Household', false),
    ('Jik Bleach 750ml', 'Powerful bleach for cleaning and disinfecting', 25.00, '/placeholder.svg?height=60&width=60', 'Household', true),
    ('Sta-Soft Fabric Softener 2L', 'Leaves clothes soft and fresh', 42.00, '/placeholder.svg?height=60&width=60', 'Household', true),
    
    -- Snacks
    ('Simba Chips Salt & Vinegar 125g', 'Crispy potato chips', 15.00, '/placeholder.svg?height=60&width=60', 'Snacks', true),
    ('Nik Naks Cheese 100g', 'Cheesy corn snack', 12.00, '/placeholder.svg?height=60&width=60', 'Snacks', true),
    ('Doritos Cheese Supreme 150g', 'Cheesy tortilla chips', 22.00, '/placeholder.svg?height=60&width=60', 'Snacks', true),
    ('Cadbury Dairy Milk Chocolate 80g', 'Creamy milk chocolate', 18.00, '/placeholder.svg?height=60&width=60', 'Snacks', false),
    
    -- Personal Care
    ('Colgate Toothpaste 100ml', 'Cavity protection toothpaste', 25.00, '/placeholder.svg?height=60&width=60', 'Personal Care', true),
    ('Dove Soap 100g', 'Moisturizing beauty bar', 15.00, '/placeholder.svg?height=60&width=60', 'Personal Care', true),
    ('Vaseline Lotion 400ml', 'Intensive care body lotion', 45.00, '/placeholder.svg?height=60&width=60', 'Personal Care', true),
    ('Shield Deodorant Roll-On 50ml', '48-hour protection deodorant', 28.00, '/placeholder.svg?height=60&width=60', 'Personal Care', true)
  ) AS product_data(name, description, price, image_url, category_name, in_stock)
JOIN cat_ids ON cat_ids.name = product_data.category_name
ON CONFLICT DO NOTHING;
