"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useShop } from "@/contexts/shop-context"
import { Loader2 } from "lucide-react"

interface AuthShopRedirectProps {
  children: React.ReactNode
  redirectUnauthenticatedTo?: string
  redirectNoShopTo?: string
}

export function AuthShopRedirect({
  children,
  redirectUnauthenticatedTo = "/auth/new",
  redirectNoShopTo = "/shops"
}: AuthShopRedirectProps) {
  const router = useRouter()
  const { user, loading: authLoading, isAuthenticated } = useAuth()
  const { currentShop, loading: shopLoading } = useShop()
  const [redirecting, setRedirecting] = useState(false)

  useEffect(() => {
    // Only redirect after both auth and shop state are determined
    if (!authLoading && !shopLoading) {
      if (!isAuthenticated) {
        // User is not authenticated, redirect to login
        setRedirecting(true)
        router.push(redirectUnauthenticatedTo)
      } else if (!currentShop && redirectNoShopTo) {
        // User is authenticated but has no shop selected, redirect to shop selection
        setRedirecting(true)
        router.push(redirectNoShopTo)
      }
    }
  }, [
    authLoading,
    shopLoading,
    isAuthenticated,
    currentShop,
    redirectUnauthenticatedTo,
    redirectNoShopTo,
    router
  ])

  if (authLoading || shopLoading || redirecting) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Loader2 className="h-8 w-8 text-blue-600 animate-spin mb-4" />
        <p className="text-gray-600 text-center">
          {redirecting
            ? "Redirecting..."
            : authLoading
            ? "Checking authentication..."
            : "Loading shop information..."}
        </p>
      </div>
    )
  }

  // If we're not redirecting, render the children
  return <>{children}</>
}
