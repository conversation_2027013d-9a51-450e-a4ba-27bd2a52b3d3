import { createClient } from '@/utils/supabase/client'

/**
 * Debug function to check data in Supabase tables
 */
export async function checkSupabaseData() {
  const supabase = await createClient()

  // Check orders
  const { data: orders, error: ordersError } = await supabase
    .from('orders')
    .select('id, created_at, payment_method, payment_status')
    .order('created_at', { ascending: false })
    .limit(10)

  if (ordersError) {
    console.error('Error fetching orders:', ordersError)
  }

  // Check payments
  const { data: payments, error: paymentsError } = await supabase
    .from('payments')
    .select('id, order_id, amount, payment_method, status, created_at')
    .order('created_at', { ascending: false })
    .limit(10)

  if (paymentsError) {
    console.error('Error fetching payments:', paymentsError)
  }

  // Check receipts
  const { data: receipts, error: receiptsError } = await supabase
    .from('receipts')
    .select('id, payment_id, receipt_number, amount, issued_date, created_at')
    .order('created_at', { ascending: false })
    .limit(10)

  if (receiptsError) {
    console.error('Error fetching receipts:', receiptsError)
  }

  return {
    orders: {
      count: orders?.length || 0,
      data: orders || []
    },
    payments: {
      count: payments?.length || 0,
      data: payments || []
    },
    receipts: {
      count: receipts?.length || 0,
      data: receipts || []
    }
  }
}