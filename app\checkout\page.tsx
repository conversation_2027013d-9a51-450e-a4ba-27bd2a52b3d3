"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { useShop } from "@/contexts/shop-context"
import { useCart } from "@/contexts/cart-context"
import { useRealTimeData } from "@/contexts/real-time-context"
import { createOrder } from "@/lib/order-service"
import { getCartItems } from "@/lib/data-service"
import { getDeliveryOptions, getDeliveryOptionById } from "@/lib/services/delivery-options-service"
import type { DeliveryOption } from "@/lib/types"
import { MapPin, CreditCard, Truck, ShoppingBag, AlertCircle, Calculator, Smartphone, Info, Package } from "lucide-react"

export default function CheckoutPage() {
  const { user } = useAuth()
  const { currentShop } = useShop()
  const router = useRouter()
  const { refreshCart, clearCart } = useCart()
  const { isMockData } = useRealTimeData()
  const [deliveryAddress, setDeliveryAddress] = useState("")
  const [paymentMethod, setPaymentMethod] = useState<"COD" | "Shop2Shop">("COD")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [cartItems, setCartItems] = useState<any[]>([])
  const [loadingCart, setLoadingCart] = useState(true)

  const [deliveryOptions, setDeliveryOptions] = useState<DeliveryOption[]>([])
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState<string>("")
  const [loadingDeliveryOptions, setLoadingDeliveryOptions] = useState(true)
  const [deliveryFee, setDeliveryFee] = useState(50.00) // Default delivery fee

  // Set delivery address from user profile when component mounts
  useEffect(() => {
    if (user?.address) {
      setDeliveryAddress(user.address)
    }
  }, [user])

  // Load delivery options and get selected option from localStorage
  useEffect(() => {
    async function loadDeliveryOptions() {
      try {
        setLoadingDeliveryOptions(true)
        const options = await getDeliveryOptions()
        setDeliveryOptions(options)

        // Get selected delivery option from localStorage
        const savedOptionId = localStorage.getItem('selectedDeliveryOption')

        if (savedOptionId) {
          // Find the option in our loaded options to make sure it exists
          const option = options.find(opt => opt.id === savedOptionId)
          if (option) {
            setSelectedDeliveryOption(savedOptionId)
            setDeliveryFee(option.price)
          } else if (options.length > 0) {
            // If saved option doesn't exist, use the first available option
            setSelectedDeliveryOption(options[0].id)
            setDeliveryFee(options[0].price)
          }
        } else if (options.length > 0) {
          // If no saved option, use the first available option
          setSelectedDeliveryOption(options[0].id)
          setDeliveryFee(options[0].price)
        }
      } catch (error) {
        console.error("Error loading delivery options:", error)
      } finally {
        setLoadingDeliveryOptions(false)
      }
    }

    loadDeliveryOptions()
  }, [])

  // Load cart items
  useEffect(() => {
    async function loadCartItems() {
      if (!user) {
        setLoadingCart(false)
        return
      }

      try {
        const items = await getCartItems(user.id)
        setCartItems(items)
      } catch (error) {
        console.error("Error loading cart items:", error)
      } finally {
        setLoadingCart(false)
      }
    }

    loadCartItems()
  }, [user])

  const calculateSubtotal = () => {
    return cartItems.reduce((total, item) => {
      const price = item.product?.price || 0
      return total + price * item.quantity
    }, 0)
  }

  const calculateTotal = () => {
    return calculateSubtotal() + deliveryFee
  }

  // Handle delivery option change
  const handleDeliveryOptionChange = (optionId: string) => {
    const option = deliveryOptions.find(opt => opt.id === optionId)
    if (option) {
      setSelectedDeliveryOption(optionId)
      setDeliveryFee(option.price)
      localStorage.setItem('selectedDeliveryOption', optionId)
    }
  }

  const handleCheckout = async () => {
    // Validate user is logged in
    if (!user) {
      setError("Please log in to continue with checkout");
      setTimeout(() => router.push("/auth"), 1500);
      return;
    }

    if (!user.id) {
      setError("User account is incomplete. Please log out and log in again.");
      return;
    }

    // Validate shop is selected
    if (!currentShop) {
      setError("Please select a shop to continue with checkout");
      return;
    }

    // Validate delivery address
    if (!deliveryAddress) {
      setError("Please provide a delivery address");
      return;
    }

    setLoading(true);
    setError("");

    try {
      console.log('Checkout: Starting checkout process for user:', user.id);

      // Get cart items with error handling
      let cartItems;
      try {
        console.log('Checkout: Fetching cart items');
        cartItems = await getCartItems(user.id);
        console.log(`Checkout: Found ${cartItems.length} items in cart`);
      } catch (cartError: any) {
        console.error("Error fetching cart items:", cartError);
        setError("Failed to load your cart items. Please try again.");
        setLoading(false);
        return;
      }

      // Validate cart has items
      if (!cartItems || cartItems.length === 0) {
        console.error("Cart is empty");
        setError("Your cart is empty. Please add items before checkout.");
        setLoading(false);
        return;
      }

      // Create order with proper error handling
      console.log('Checkout: Creating order...');
      try {
        // Create the order
        const order = await createOrder(
          user.id,
          currentShop?.id || '', // Use current shop ID
          cartItems,
          deliveryAddress,
          paymentMethod,
          undefined,
          false, // No longer using demo mode
          selectedDeliveryOption,
          deliveryFee
        );



        // This should never happen now that we're throwing errors, but just in case
        if (!order) {
          console.error("Order creation returned null");
          setError("Failed to create your order. Please try again.");
          setLoading(false);
          return;
        }

        console.log('Checkout: Order created successfully:', order.id);

        // Clear the cart immediately to ensure UI is updated
        try {
          console.log('Checkout: Clearing cart in UI');
          // First clear the cart in the UI immediately
          await clearCart();
          console.log('Checkout: Cart cleared successfully');

          // Then refresh to ensure everything is in sync
          await refreshCart();
          console.log('Checkout: Cart refreshed successfully');
        } catch (clearError) {
          console.error('Checkout: Error clearing cart:', clearError);
          // Continue with checkout even if cart clearing fails
        }

        // Redirect based on payment method
        if (paymentMethod === "Shop2Shop") {
          console.log('Checkout: Redirecting to payment page...');
          // Redirect to payment page
          router.push(`/payment/${order.id}`);
        } else {
          console.log('Checkout: Redirecting to confirmation page...');
          // COD - go to confirmation
          router.push(`/orders/${order.id}/confirmation`);
        }
      } catch (orderError: any) {
        console.error("Exception creating order:", orderError);
        // Show a user-friendly error message
        setError(orderError.message || "An unexpected error occurred creating your order. Please try again.");
        setLoading(false);
      }
    } catch (err: any) {
      console.error("Checkout error:", err);
      setError(err.message || "An error occurred during checkout. Please try again.");
      setLoading(false);
    } finally {
      // This will only run if we haven't redirected
      setLoading(false);
    }
  }

  return (
    <>
      <MainHeader />
      <div className="p-4 flex-grow overflow-y-auto">
        <h1 className="text-2xl font-bold mb-4">Checkout</h1>

        <div className="bg-white rounded-md p-4 mb-4 border border-gray-200">
          <h2 className="font-bold text-lg mb-2 flex items-center gap-2">
            <MapPin size={20} className="text-blue-600" />
            Delivery Address
          </h2>
          <textarea
            value={deliveryAddress}
            onChange={(e) => setDeliveryAddress(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
            rows={3}
            placeholder="Enter your delivery address"
            required
          />
        </div>

        <div className="bg-white rounded-md p-4 mb-4 border border-gray-200">
          <h2 className="font-bold text-lg mb-2 flex items-center gap-2">
            <CreditCard size={20} className="text-blue-600" />
            Payment Method
          </h2>
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="paymentMethod"
                value="COD"
                checked={paymentMethod === "COD"}
                onChange={() => setPaymentMethod("COD")}
                className="h-4 w-4 text-blue-600"
              />
              <span>Cash on Delivery (COD)</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name="paymentMethod"
                value="Shop2Shop"
                checked={paymentMethod === "Shop2Shop"}
                onChange={() => setPaymentMethod("Shop2Shop")}
                className="h-4 w-4 text-blue-600"
              />
              <div className="flex items-center gap-2">
                <span>Shop2Shop Payment (Credit/Debit Card, EFT)</span>
                <div className="flex gap-1">
                  <CreditCard size={16} className="text-blue-600" />
                  <Smartphone size={16} className="text-blue-600" />
                </div>
              </div>
            </label>
          </div>
        </div>

        <div className="bg-white rounded-md p-4 mb-4 border border-gray-200">
          <h2 className="font-bold text-lg mb-2 flex items-center gap-2">
            <Truck size={20} className="text-blue-600" />
            Delivery Options
          </h2>

          {loadingDeliveryOptions ? (
            <div className="flex justify-center py-2">
              <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : deliveryOptions.length === 0 ? (
            <p className="text-gray-600">No delivery options available</p>
          ) : (
            <div className="space-y-2">
              {deliveryOptions.map(option => (
                <label key={option.id} className="flex items-start p-2 border rounded-md cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="deliveryOption"
                    value={option.id}
                    checked={selectedDeliveryOption === option.id}
                    onChange={() => handleDeliveryOptionChange(option.id)}
                    className="mt-1 h-4 w-4 text-blue-600"
                  />
                  <div className="ml-2 flex-1">
                    <div className="flex items-center">
                      {option.name.toLowerCase().includes('express') ? (
                        <Package size={16} className="text-blue-600 mr-1" />
                      ) : (
                        <Truck size={16} className="text-blue-600 mr-1" />
                      )}
                      <span className="font-medium">{option.name}</span>
                      <span className="ml-auto font-medium">R {option.price.toFixed(2)}</span>
                    </div>
                    <p className="text-sm text-gray-600">{option.estimated_days}</p>
                    {option.description && (
                      <p className="text-xs text-gray-500 mt-1">{option.description}</p>
                    )}
                  </div>
                </label>
              ))}
            </div>
          )}
        </div>

        <div className="bg-white rounded-md p-4 mb-4 border border-gray-200">
          <h2 className="font-bold text-lg mb-2 flex items-center gap-2">
            <Calculator size={20} className="text-blue-600" />
            Order Summary
          </h2>

          {loadingCart ? (
            <div className="text-center py-4">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
              <p className="mt-2 text-gray-500 text-sm">Loading cart...</p>
            </div>
          ) : cartItems.length === 0 ? (
            <p className="text-gray-600">Your cart is empty</p>
          ) : (
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal ({cartItems.length} items):</span>
                <span>R {calculateSubtotal().toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Delivery Fee ({deliveryOptions.find(o => o.id === selectedDeliveryOption)?.name || 'Standard'}):</span>
                <span>R {deliveryFee.toFixed(2)}</span>
              </div>
              <div className="border-t border-gray-200 pt-2 mt-2 flex justify-between font-bold">
                <span>Total:</span>
                <span>R {calculateTotal().toFixed(2)}</span>
              </div>
            </div>
          )}
        </div>

        {error && (
          <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4 flex items-center gap-2">
            <AlertCircle size={20} />
            {error}
          </div>
        )}



        <button
          onClick={handleCheckout}
          disabled={loading || !deliveryAddress}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md text-lg font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center justify-center gap-2"
        >
          <ShoppingBag size={20} />
          {loading ? "Processing..." : `Place Order (${paymentMethod === "COD" ? "Pay on Delivery" : "Pay with Shop2Shop"})`}
        </button>
      </div>
      <MainFooter />
    </>
  )
}
