import { createClient } from '@/utils/supabase/client'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    // Try both client methods to ensure we get a valid client
    let supabase;
    let user;
    let userError;

    try {
      // Get the server client
      supabase = await createClient()
      const authResult = await supabase.auth.getUser()
      user = authResult.data.user
      userError = authResult.error
    } catch (clientError) {
      console.error("Error with server client:", clientError)
      return NextResponse.json(
        { error: 'Error authenticating user' },
        { status: 500 }
      )
    }

    console.log("API: User authentication result:", user ? "User found" : "No user", userError ? `Error: ${userError.message}` : "No error")

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get the request body
    const requestData = await request.json()
    const { mobile, email, shop_name, address, is_admin } = requestData

    // Check if user profile already exists
    const { data: existingProfile, error: profileError } = await supabase.from('users')
      .select('id')
      .eq('id', user.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      // An error occurred other than "not found"
      return NextResponse.json(
        { error: profileError.message },
        { status: 500 }
      )
    }

    if (existingProfile) {
      // Profile already exists, update it
      const { error: updateError } = await supabase.from('users')
        .update({
          mobile: mobile || user.user_metadata?.mobile,
          email: email || user.email,
          shop_name: shop_name || user.user_metadata?.shop_name,
          address: address || user.user_metadata?.address,
          is_admin: is_admin === true || user.user_metadata?.is_admin === true || false,
        })
        .eq('id', user.id)

      if (updateError) {
        return NextResponse.json(
          { error: updateError.message },
          { status: 500 }
        )
      }

      return NextResponse.json({ success: true, message: 'Profile updated successfully' })
    } else {
      // Profile doesn't exist, create it
      const { error: insertError } = await supabase.from('users')
        .insert({
          id: user.id,
          mobile: mobile || user.user_metadata?.mobile || '',
          email: email || user.email || '',
          shop_name: shop_name || user.user_metadata?.shop_name || '',
          address: address || user.user_metadata?.address || '',
          is_admin: is_admin === true || user.user_metadata?.is_admin === true || false,
        })

      if (insertError) {
        return NextResponse.json(
          { error: insertError.message },
          { status: 500 }
        )
      }

      return NextResponse.json({ success: true, message: 'Profile created successfully' })
    }
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
