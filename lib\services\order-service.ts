// import { createClient } from '@/utils/supabase/client';
// import { devLog, logError } from '@/utils/logger';
// import { Database } from '@/lib/database.types';
// import { clearCart, getCartItems } from './cart-service';

// export type Order = Database['public']['Tables']['orders']['Row'];
// export type OrderItem = Database['public']['Tables']['order_items']['Row'];

// /**
//  * Get orders for a user
//  * @param userId User ID
//  * @returns Array of orders
//  */
// export async function getUserOrders(userId: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('orders')
//       .select('*')
//       .eq('user_id', userId)
//       .order('created_at', { ascending: false });
    
//     if (error) {
//       logError(`Error fetching orders for user ${userId}:`, error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError(`Unexpected error fetching orders for user ${userId}:`, error);
//     return [];
//   }
// }

// /**
//  * Get orders for a shop
//  * @param shopId Shop ID
//  * @returns Array of orders with user details
//  */
// export async function getShopOrders(shopId: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('orders')
//       .select(`
//         *,
//         users (id, mobile, full_name)
//       `)
//       .eq('shop_id', shopId)
//       .order('created_at', { ascending: false });
    
//     if (error) {
//       logError(`Error fetching orders for shop ${shopId}:`, error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError(`Unexpected error fetching orders for shop ${shopId}:`, error);
//     return [];
//   }
// }

// /**
//  * Get an order by ID with items
//  * @param orderId Order ID
//  * @returns Order with items or null
//  */
// export async function getOrderById(orderId: string) {
//   const supabase = createClient();
  
//   try {
//     // Get order details
//     const { data: order, error: orderError } = await supabase
//       .from('orders')
//       .select(`
//         *,
//         users (id, mobile, full_name)
//       `)
//       .eq('id', orderId)
//       .single();
    
//     if (orderError) {
//       logError(`Error fetching order ${orderId}:`, orderError);
//       return null;
//     }
    
//     // Get order items
//     const { data: items, error: itemsError } = await supabase
//       .from('order_items')
//       .select(`
//         *,
//         products (*)
//       `)
//       .eq('order_id', orderId);
    
//     if (itemsError) {
//       logError(`Error fetching items for order ${orderId}:`, itemsError);
//       return { ...order, items: [] };
//     }
    
//     return { ...order, items: items || [] };
//   } catch (error) {
//     logError(`Unexpected error fetching order ${orderId}:`, error);
//     return null;
//   }
// }

// /**
//  * Create a new order from cart
//  * @param userId User ID
//  * @param shopId Shop ID
//  * @param deliveryOptionId Delivery option ID
//  * @param paymentMethod Payment method
//  * @param notes Order notes
//  * @returns Result object with success status and order ID or error
//  */
// export async function createOrderFromCart(
//   userId: string,
//   shopId: string,
//   deliveryOptionId: string | null,
//   paymentMethod: string,
//   notes?: string
// ) {
//   const supabase = createClient();
  
//   try {
//     // Get cart items
//     const cartItems = await getCartItems(userId);
    
//     if (cartItems.length === 0) {
//       return { success: false, error: "Cart is empty" };
//     }
    
//     // Calculate totals
//     const subtotal = cartItems.reduce((total: number, item: { products: { price: number; }; quantity: number; }) => {
//       const price = item.products?.price || 0;
//       return total + (price * item.quantity);
//     }, 0);
    
//     // Get delivery fee if delivery option is selected
//     let deliveryFee = 0;
//     if (deliveryOptionId) {
//       const { data: deliveryOption } = await supabase
//         .from('delivery_options')
//         .select('price')
//         .eq('id', deliveryOptionId)
//         .single();
      
//       if (deliveryOption) {
//         deliveryFee = deliveryOption.price;
//       }
//     }
    
//     const total = subtotal + deliveryFee;
    
//     // Start a transaction
//     // Create order
//     const { data: order, error: orderError } = await supabase
//       .from('orders')
//       .insert({
//         user_id: userId,
//         shop_id: shopId,
//         subtotal,
//         delivery_fee: deliveryFee,
//         total,
//         status: 'pending',
//         payment_method: paymentMethod,
//         delivery_option_id: deliveryOptionId,
//         notes,
//         created_at: new Date().toISOString(),
//         updated_at: new Date().toISOString()
//       })
//       .select()
//       .single();
    
//     if (orderError) {
//       logError("Error creating order:", orderError);
//       return { success: false, error: "Failed to create order" };
//     }
    
//     // Create order items
//     const orderItems = cartItems.map((item: { product_id: any; quantity: any; products: { price: any; }; }) => ({
//       order_id: order.id,
//       product_id: item.product_id,
//       quantity: item.quantity,
//       price: item.products?.price || 0,
//       created_at: new Date().toISOString(),
//       updated_at: new Date().toISOString()
//     }));
    
//     const { error: itemsError } = await supabase
//       .from('order_items')
//       .insert(orderItems);
    
//     if (itemsError) {
//       logError("Error creating order items:", itemsError);
//       // Try to delete the order since items failed
//       await supabase.from('orders').delete().eq('id', order.id);
//       return { success: false, error: "Failed to create order items" };
//     }
    
//     // Clear the cart
//     await clearCart(userId);
    
//     return { success: true, orderId: order.id };
//   } catch (error) {
//     logError("Unexpected error creating order:", error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Update order status
//  * @param orderId Order ID
//  * @param status New status
//  * @returns Result object with success status or error
//  */
// export async function updateOrderStatus(orderId: string, status: string) {
//   const supabase = createClient();
  
//   try {
//     const { error } = await supabase
//       .from('orders')
//       .update({ 
//         status, 
//         updated_at: new Date().toISOString() 
//       })
//       .eq('id', orderId);
    
//     if (error) {
//       logError(`Error updating order ${orderId} status:`, error);
//       return { success: false, error: "Failed to update order status" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error updating order ${orderId} status:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Get delivery options
//  * @returns Array of delivery options
//  */
// export async function getDeliveryOptions() {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('delivery_options')
//       .select('*')
//       .eq('is_active', true)
//       .order('price');
    
//     if (error) {
//       logError("Error fetching delivery options:", error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError("Unexpected error fetching delivery options:", error);
//     return [];
//   }
// }
