"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/utils/supabase/client'

export function AuthRedirectFix() {
  const router = useRouter()
  
  useEffect(() => {
    let isMounted = true;

    const handleRedirect = async () => {
      try {
        const supabase = createClient()
        
        // Get current session
        const { data: { user }, error: authError } = await supabase.auth.getUser()
        
        if (authError || !user) {
          console.log('Auth error or no user found:', authError?.message || 'No user')
          if (isMounted) {
            window.location.replace('/auth')
          }
          return
        }

        console.log('Auth successful, checking shop status for user:', user.id)

        // Check if user has a shop
        const { data: shopData, error: shopError } = await supabase
          .from('user_shops')
          .select('shop_id')
          .eq('user_id', user.id)
          .single()

        if (shopError) {
          console.error('Error checking shop status:', shopError)
          return
        }

        // Only proceed if component is still mounted
        if (!isMounted) return;

        // Clear ALL session storage items related to auth/redirect
        const itemsToClear = [
          'redirectingTo',
          'redirectAttemptCount',
          'loginPageInstance',
          'currentRedirectKey',
          'freshLogin',
          'signOutEventHandled'
        ];
        
        itemsToClear.forEach(item => sessionStorage.removeItem(item));
        
        if (shopData?.shop_id) {
          console.log('User has shop, redirecting to products')
          localStorage.setItem('currentShopId', shopData.shop_id)
          window.location.replace('/products')
        } else {
          console.log('User has no shop, redirecting to shop selection')
          window.location.replace('/shops')
        }
      } catch (error) {
        console.error('Error in auth redirect:', error)
      }
    }

    // Execute redirect logic
    handleRedirect()

    // Cleanup function
    return () => {
      isMounted = false
    }
  }, []) // Remove router from dependencies since we're using window.location

  return null
}
