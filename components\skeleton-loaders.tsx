"use client"

import { Skeleton } from "@/components/ui/skeleton"

export function ProductCardSkeleton() {
  return (
    <div className="flex items-center border border-gray-200 rounded-md p-3 bg-white animate-pulse">
      <div className="w-16 h-16 rounded mr-4 flex-shrink-0 bg-gray-200"></div>
      <div className="flex-grow">
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-3 bg-gray-200 rounded w-5/6"></div>
      </div>
      <div className="flex flex-col items-end">
        <div className="h-8 w-24 bg-gray-200 rounded mb-2"></div>
        <div className="h-8 w-16 bg-gray-200 rounded"></div>
      </div>
    </div>
  )
}

export function CategorySkeleton() {
  return (
    <div className="mb-6 animate-pulse">
      <div className="flex items-center gap-2 font-bold text-gray-600 mt-4 mb-2 pb-1 border-b border-gray-200">
        <div className="bg-gray-200 p-1 rounded-md w-8 h-8"></div>
        <div className="h-5 bg-gray-200 rounded w-32"></div>
      </div>
      <div className="space-y-3">
        {[1, 2, 3].map((i) => (
          <ProductCardSkeleton key={i} />
        ))}
      </div>
    </div>
  )
}

export function SearchBarSkeleton() {
  return (
    <div className="p-3 bg-gray-100 border-b border-gray-200 animate-pulse">
      <div className="relative">
        <div className="w-full h-10 bg-gray-200 rounded-md"></div>
      </div>
    </div>
  )
}

export function ProductListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, i) => (
        <ProductCardSkeleton key={i} />
      ))}
    </div>
  )
}

export function CartItemSkeleton() {
  return (
    <div className="flex items-center border border-gray-200 rounded-md p-3 bg-white animate-pulse">
      <div className="w-16 h-16 rounded mr-4 flex-shrink-0 bg-gray-200"></div>
      <div className="flex-grow">
        <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
      </div>
      <div className="flex flex-col items-end">
        <div className="h-8 w-24 bg-gray-200 rounded mb-2"></div>
        <div className="h-8 w-16 bg-gray-200 rounded"></div>
      </div>
    </div>
  )
}

export function CartSummarySkeleton() {
  return (
    <div className="border border-gray-200 rounded-md p-4 bg-white animate-pulse">
      <div className="h-5 bg-gray-200 rounded w-1/3 mb-4"></div>
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/6"></div>
        </div>
        <div className="flex justify-between">
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/6"></div>
        </div>
      </div>
      <div className="h-10 bg-gray-200 rounded w-full"></div>
    </div>
  )
}
