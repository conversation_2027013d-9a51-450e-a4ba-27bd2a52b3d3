# Environment Variables Setup Guide

To ensure proper security in your Supabase integration, you need to set up your environment variables correctly. This guide explains how to properly configure your environment variables for both development and production.

## Required Environment Variables

Create or update your `.env.local` file with the following variables:

```
# Public variables (accessible in browser)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Server-side only variables (not exposed to browser)
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Other environment variables
SHOP2SHOP_API_URL=your-shop2shop-api-url
SHOP2SHOP_MERCHANT_ID=your-merchant-id
SHOP2SHOP_API_KEY=your-api-key
SHOP2SHOP_RETURN_URL=your-return-url
```

## Important Security Notes

1. **NEVER** use the `NEXT_PUBLIC_` prefix for sensitive keys like the service role key. This would expose them to the client.

2. The `NEXT_PUBLIC_SUPABASE_ANON_KEY` is designed to be public and is used for client-side operations with Row Level Security (RLS) policies.

3. The `SUPABASE_SERVICE_ROLE_KEY` should only be used server-side and can bypass RLS policies. Keep this secret!

## Environment Variables in Production

When deploying to production (e.g., Vercel), make sure to:

1. Set all the environment variables in your hosting platform's dashboard
2. Double-check that you haven't accidentally prefixed sensitive variables with `NEXT_PUBLIC_`
3. Ensure your production environment has all the required variables

## Verifying Your Setup

To verify your environment variables are set up correctly:

1. Make sure your client-side code only uses `NEXT_PUBLIC_SUPABASE_ANON_KEY`
2. Ensure server-side admin operations use `SUPABASE_SERVICE_ROLE_KEY`
3. Test authentication and data access to confirm RLS policies are working
