"use client"

import { useCart } from "@/contexts/cart-context"
import { useRouter } from "next/navigation"
import { ShoppingC<PERSON>, ArrowRight } from "lucide-react"
import { useEffect, useState } from "react"

export function FloatingCart() {
  const { cartItems, cartCount, cartTotal } = useCart()
  const router = useRouter()
  const [visible, setVisible] = useState(false)
  const [animateIn, setAnimateIn] = useState(false)

  // Show the floating cart only if there are items in the cart
  useEffect(() => {
    if (cartCount > 0) {
      setVisible(true)
      // Add a small delay before animating in for a smoother effect
      const timer = setTimeout(() => setAnimateIn(true), 100)
      return () => clearTimeout(timer)
    } else {
      setAnimateIn(false)
      // Add a delay before hiding to allow for animation
      const timer = setTimeout(() => setVisible(false), 300)
      return () => clearTimeout(timer)
    }
  }, [cartCount])

  // Add a subtle animation effect when the cart total changes
  const [highlight, setHighlight] = useState(false)

  useEffect(() => {
    // Skip the initial render
    if (cartTotal > 0) {
      setHighlight(true)
      const timer = setTimeout(() => setHighlight(false), 500)
      return () => clearTimeout(timer)
    }
  }, [cartTotal])

  if (!visible) return null

  return (
    <div
      className={`fixed bottom-16 left-0 right-0 z-40 transition-all duration-300 transform ${
        animateIn ? "translate-y-0 opacity-100" : "translate-y-16 opacity-0"
      }`}
    >
      <div className="max-w-md mx-auto px-4">
        <div className="bg-blue-600 text-white rounded-lg shadow-lg p-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="relative p-2">
              <ShoppingCart size={24} />
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {cartCount > 99 ? '99+' : cartCount}
              </span>
            </div>
            <div>
              <div className="text-sm font-medium">Your Cart</div>
              <div className="text-xs opacity-90">{cartCount} {cartCount === 1 ? 'item' : 'items'}</div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className={`font-bold transition-colors duration-300 ${highlight ? 'text-yellow-300 scale-110' : 'text-white'}`}>
              R {cartTotal.toFixed(2)}
            </div>
            <button
              onClick={() => router.push('/cart')}
              className="bg-white text-blue-600 px-4 py-2 rounded-full text-sm font-medium flex items-center gap-1 hover:bg-blue-50 transition-colors min-h-[44px] min-w-[80px] touch-target"
            >
              View
              <ArrowRight size={16} />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
