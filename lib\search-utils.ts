import type { Product, Category } from "./types"

/**
 * Search options for filtering products
 */
export interface SearchOptions {
  query?: string
  categoryId?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
  sortBy?: 'name' | 'price' | 'popularity'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

/**
 * Search result with pagination info
 */
export interface SearchResult<T> {
  items: T[]
  total: number
  hasMore: boolean
}

/**
 * Filter products based on search options
 */
export function filterProducts(
  products: Product[],
  options: SearchOptions = {}
): SearchResult<Product> {
  let filtered = [...products]

  // Filter by search query
  if (options.query) {
    const query = options.query.toLowerCase()
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(query) ||
      (product.description && product.description.toLowerCase().includes(query))
    )
  }

  // Filter by category
  if (options.categoryId) {
    filtered = filtered.filter(product =>
      product.category_id === options.categoryId
    )
  }

  // Filter by price range
  if (options.minPrice !== undefined) {
    const minPrice = options.minPrice
    filtered = filtered.filter(product => {
      const price = typeof product.price === 'string'
        ? parseFloat(product.price)
        : product.price
      return price >= minPrice
    })
  }

  if (options.maxPrice !== undefined) {
    const maxPrice = options.maxPrice
    filtered = filtered.filter(product => {
      const price = typeof product.price === 'string'
        ? parseFloat(product.price)
        : product.price
      return price <= maxPrice
    })
  }

  // Filter by stock status
  if (options.inStock !== undefined) {
    filtered = filtered.filter(product =>
      product.in_stock === options.inStock
    )
  }

  // Sort products
  if (options.sortBy) {
    const sortOrder = options.sortOrder === 'desc' ? -1 : 1

    filtered.sort((a, b) => {
      if (options.sortBy === 'name') {
        return sortOrder * a.name.localeCompare(b.name)
      }

      if (options.sortBy === 'price') {
        const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price
        const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price
        return sortOrder * (priceA - priceB)
      }

      // Default to name sorting
      return sortOrder * a.name.localeCompare(b.name)
    })
  }

  // Calculate pagination
  const total = filtered.length

  // Apply pagination if specified
  if (options.limit !== undefined) {
    const offset = options.offset || 0
    filtered = filtered.slice(offset, offset + options.limit)
  }

  return {
    items: filtered,
    total,
    hasMore: options.limit !== undefined && options.offset !== undefined
      ? options.offset + options.limit < total
      : false
  }
}

/**
 * Get price range (min and max) from products
 */
export function getPriceRange(products: Product[]): { min: number; max: number } {
  if (!products.length) {
    return { min: 0, max: 0 }
  }

  let min = Number.MAX_VALUE
  let max = Number.MIN_VALUE

  for (const product of products) {
    const price = typeof product.price === 'string'
      ? parseFloat(product.price)
      : product.price

    if (price < min) min = price
    if (price > max) max = price
  }

  return { min, max }
}

/**
 * Extract unique values for a specific field from products
 */
export function extractUniqueValues<T>(
  products: Product[],
  field: keyof Product
): T[] {
  const uniqueValues = new Set<T>()

  for (const product of products) {
    const value = product[field] as unknown as T
    if (value !== undefined && value !== null) {
      uniqueValues.add(value)
    }
  }

  return Array.from(uniqueValues)
}

/**
 * Search products from the API
 */
export async function searchProducts(
  options: SearchOptions = {}
): Promise<SearchResult<Product>> {
  try {
    // Build query parameters
    const params = new URLSearchParams()

    if (options.query) params.append('query', options.query)
    if (options.categoryId) params.append('categoryId', options.categoryId)
    if (options.minPrice !== undefined) params.append('minPrice', options.minPrice.toString())
    if (options.maxPrice !== undefined) params.append('maxPrice', options.maxPrice.toString())
    if (options.inStock !== undefined) params.append('inStock', options.inStock.toString())
    if (options.sortBy) params.append('sortBy', options.sortBy)
    if (options.sortOrder) params.append('sortOrder', options.sortOrder)
    if (options.limit !== undefined) params.append('limit', options.limit.toString())
    if (options.offset !== undefined) params.append('offset', options.offset.toString())

    // Make API request
    const response = await fetch(`/api/products/search?${params.toString()}`)

    if (!response.ok) {
      throw new Error('Failed to search products')
    }

    return await response.json()
  } catch (error) {
    // Return empty results on error
    return { items: [], total: 0, hasMore: false }
  }
}
