"use client"

import { useState, useEffect, useCallback, useRef } from "react"

interface UseApiOptions<T> {
  initialData?: T
  debounceMs?: number
  dependencies?: any[]
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
}

/**
 * Custom hook for debounced API calls
 * @param apiFunction The API function to call
 * @param options Configuration options
 * @returns Object containing data, loading state, error, and refetch function
 */
export function useDebouncedApi<T, P extends any[]>(
  apiFunction: (...args: P) => Promise<T>,
  options: UseApiOptions<T> = {}
) {
  const {
    initialData,
    debounceMs = 300,
    dependencies = [],
    onSuccess,
    onError
  } = options
  
  const [data, setData] = useState<T | undefined>(initialData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  // Use refs to avoid stale closures in debounced functions
  const apiRef = useRef(apiFunction)
  const onSuccessRef = useRef(onSuccess)
  const onErrorRef = useRef(onError)
  
  // Update refs when dependencies change
  useEffect(() => {
    apiRef.current = apiFunction
    onSuccessRef.current = onSuccess
    onErrorRef.current = onError
  }, [apiFunction, onSuccess, onError])
  
  // Debounced fetch function
  const debouncedFetch = useCallback(
    async (...args: P) => {
      // Clear any existing timeout
      const timeoutId = (debouncedFetch as any).timeoutId
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      // Set a new timeout
      (debouncedFetch as any).timeoutId = setTimeout(async () => {
        setLoading(true)
        setError(null)
        
        try {
          const result = await apiRef.current(...args)
          setData(result)
          onSuccessRef.current?.(result)
        } catch (err) {
          const error = err instanceof Error ? err : new Error(String(err))
          setError(error)
          onErrorRef.current?.(error)
        } finally {
          setLoading(false)
        }
      }, debounceMs)
    },
    [debounceMs, ...dependencies]
  )
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      const timeoutId = (debouncedFetch as any).timeoutId
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [debouncedFetch])
  
  // Immediate fetch function (no debounce)
  const fetchImmediate = useCallback(
    async (...args: P) => {
      setLoading(true)
      setError(null)
      
      try {
        const result = await apiRef.current(...args)
        setData(result)
        onSuccessRef.current?.(result)
        return result
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err))
        setError(error)
        onErrorRef.current?.(error)
        throw error
      } finally {
        setLoading(false)
      }
    },
    [...dependencies]
  )
  
  return {
    data,
    loading,
    error,
    fetch: debouncedFetch,
    fetchImmediate
  }
}
