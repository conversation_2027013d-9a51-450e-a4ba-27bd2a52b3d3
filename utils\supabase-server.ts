"use server"

import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { createServerClient as createSupabaseServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

/**
 * Creates a Supabase client for server components with cookie handling
 * This uses the ANON key for regular user operations
 */
export async function createServerClient() {
  if (typeof window !== 'undefined') {
    throw new Error('Server client cannot be used on the client')
  }

  const cookieStore = await cookies()

  return createSupabaseServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options: any) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // The `set` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
        remove(name: string, options: any) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // The `delete` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
      global: {
        // Add request timeout to prevent hanging requests
        fetch: (url, options) => {
          const timeout = 60000; // 60 seconds timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);

          return fetch(url, {
            ...options,
            signal: controller.signal,
            // Add cache control to prevent stale responses
            headers: {
              ...options?.headers,
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          }).finally(() => clearTimeout(timeoutId));
        }
      }
    }
  )
}

/**
 * Create a Supabase server-side admin client with service role key
 * IMPORTANT: Only use this for admin operations that require bypassing RLS
 * Never expose this client to the client-side
 */
export async function createAdminClient() {
  if (typeof window !== 'undefined') {
    throw new Error('Admin client cannot be used on the client')
  }

  return createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
      global: {
        headers: {
          'x-client-info': 'spaza-smart-order-admin',
        },
        // Add request timeout to prevent hanging requests
        fetch: (url, options) => {
          const timeout = 60000; // 60 seconds timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), timeout);

          return fetch(url, {
            ...options,
            signal: controller.signal,
            // Add cache control to prevent stale responses
            headers: {
              ...options?.headers,
              'x-client-info': 'spaza-smart-order-admin',
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          }).finally(() => clearTimeout(timeoutId));
        } 
      }
    }
  )
}
