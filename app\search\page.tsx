import { MainHeader } from "@/components/main-header"
import { MainFooter } from "@/components/main-footer"
import { SearchBar } from "@/components/search-bar"
import { ProductList } from "@/components/product-list"
import { FloatingCart } from "@/components/floating-cart"
import { searchProducts } from "@/lib/data-service"
import Link from "next/link"

export default async function SearchPage({
  searchParams,
}: {
  searchParams: { q: string }
}) {
  const query = searchParams.q || ""
  const products = await searchProducts(query)

  return (
    <>
      <MainHeader />
      <SearchBar />
      <div className="p-4 flex-grow overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-xl font-bold">Search Results</h1>
          <Link href="/products" className="text-blue-600 text-sm">
            Back to Products
          </Link>
        </div>

        {query ? (
          <>
            <p className="text-gray-600 mb-4">
              {products.length} result{products.length !== 1 ? "s" : ""} for "{query}"
            </p>

            {products.length > 0 ? (
              <ProductList products={products} />
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 mb-4">No products found matching your search</p>
                <Link
                  href="/products"
                  className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Browse All Products
                </Link>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Please enter a search term</p>
          </div>
        )}
      </div>
      <FloatingCart />
      <MainFooter />
    </>
  )
}
