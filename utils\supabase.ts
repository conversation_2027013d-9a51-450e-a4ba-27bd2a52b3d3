import { createClient as createSupabase<PERSON>lient } from '@supabase/supabase-js'
import { createBrowserClient as createSupabaseBrowserClient } from '@supabase/ssr'

// Singleton pattern for client-side
let browserClient: ReturnType<typeof createSupabaseBrowserClient> | null = null

/**
 * Creates a Supabase client for client-side usage
 * Uses singleton pattern to prevent multiple instances
 */
export function createBrowserClient() {
  try {
    // Return existing client if already initialized
    if (browserClient) {
      console.log("Using existing Supabase client instance");
      return browserClient
    }

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error("Missing Supabase environment variables")
      throw new Error("Supabase configuration missing")
    }

    console.log("Creating new Supabase client with URL:", supabaseUrl);

    // Create new client
    browserClient = createSupabaseBrowserClient(
      supabaseUrl,
      supabaseAnonKey,
      {
        auth: {
          persistSession: true,
          storageKey: 'supabase-auth',
          autoRefreshToken: true,
        },
        realtime: {
          params: {
            eventsPerSecond: 10
          },
          timeout: 60000,
          heartbeatIntervalMs: 20000,
        },
      }
    )

    // Add connection status listener
    browserClient.auth.onAuthStateChange((event: string, session: any) => {
      console.log("Auth state changed:", event)
      if (session) {
        console.log("User is authenticated")
      } else {
        console.log("User is not authenticated")
      }
    })

    console.log("Supabase client created successfully");
    return browserClient
  } catch (error) {
    console.error("Error initializing Supabase client:", error)
    throw error
  }
}


export function getSupabaseClient() {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      console.error("Missing Supabase environment variables")
      throw new Error("Missing Supabase environment variables")
    }

    return createSupabaseBrowserClient(supabaseUrl, supabaseAnonKey)
  } catch (error) {
    console.error("Error creating Supabase client:", error)
    throw error
  }
}
