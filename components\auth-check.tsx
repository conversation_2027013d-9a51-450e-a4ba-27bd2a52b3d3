"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/multi-shop-auth-context'

export function AuthCheck() {
  const router = useRouter()
  const [checking, setChecking] = useState(true)
  const { user, isAuthenticated, loading } = useAuth()

  useEffect(() => {
    // Wait for auth context to finish loading
    if (loading) return

    if (!isAuthenticated || !user) {
      console.log('AuthCheck: User not authenticated, redirecting to login...')
      router.push('/login')
      return
    }

    // User is authenticated, stop checking
    setChecking(false)
    console.log('AuthCheck: User authenticated successfully')
  }, [isAuthenticated, user, loading, router])

  // Show loading while checking authentication
  if (checking || loading) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-3"></div>
          <p className="text-blue-600 font-medium text-sm">Checking authentication...</p>
        </div>
      </div>
    )
  }

  // Return null when authentication is confirmed
  return null
}


