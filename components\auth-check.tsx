"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/utils/supabase/client'

export function AuthCheck() {
  const router = useRouter()
  const [checking, setChecking] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check if user is logged in via localStorage first (fast check)
        const isLoggedIn = localStorage.getItem('userLoggedIn') === 'true'

        if (!isLoggedIn) {
          console.log('AuthCheck: Not logged in according to localStorage, redirecting...')
          router.push('/login')
          return
        }

        // Immediately set checking to false to improve user experience
        // This makes the UI responsive immediately
        setChecking(false)

        // Continue with auth verification in the background
        // Use a short timeout to allow the UI to update first
        setTimeout(async () => {
          try {
            const supabase = createClient()
            if (!supabase) {
              console.error('AuthCheck: Failed to create Supabase client')
              localStorage.removeItem('userLoggedIn')
              window.location.href = '/login'
              return
            }

            const { data: { user }, error } = await supabase.auth.getUser()

            if (error || !user) {
              console.log('AuthCheck: Not logged in according to Supabase, redirecting...')
              localStorage.removeItem('userLoggedIn') // Clear incorrect localStorage state
              window.location.href = '/login'
              return
            }
          } catch (err) {
            console.error('AuthCheck: Error verifying auth:', err)
            // Don't redirect on error to prevent loops
          }
        }, 100)

        // Check if user has a profile in the users table in the background
        // This won't block the UI
        setTimeout(async () => {
          try {
            const supabase = createClient()
            if (!supabase) return;

            // Get current user ID from localStorage for faster access
            const userId = localStorage.getItem('currentUserId')
            if (!userId) return;

            // Get user metadata from Supabase
            const { data: { user } } = await supabase.auth.getUser()
            if (!user) return;

            // Update profile
            await supabase
              .from('users')
              .upsert([
                {
                  id: user.id,
                  mobile: user.user_metadata?.mobile || 'unknown',
                  full_name: user.user_metadata?.full_name || '',
                  created_at: new Date().toISOString(),
                  updated_at: new Date().toISOString()
                }
              ],
              {
                onConflict: 'id',
                ignoreDuplicates: true
              })

            console.log('AuthCheck: Profile created or updated')
          } catch (err) {
            console.error('AuthCheck: Error with profile:', err)
            // Don't block UI for profile errors
          }
        }, 200)
      } catch (err) {
        console.error('AuthCheck: Unexpected error:', err)
        setError('Unexpected error occurred')
        setChecking(false) // Make sure to set checking to false on error
      }
    }

    checkAuth()
  }, [router])

  // Show a loading indicator while checking auth
  if (checking) {
    return (
      <div className="fixed inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-blue-600 font-medium">Loading your profile...</p>
        </div>
      </div>
    )
  }

  // Show error message if there is one
  if (error) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-yellow-100 border-t border-yellow-300 p-3 text-center text-yellow-800 text-sm">
        {error}
        <button
          className="ml-2 underline text-blue-600"
          onClick={() => setError(null)}
        >
          Dismiss
        </button>
      </div>
    )
  }

  // Return null when done checking and no errors
  return null
}


