/**
 * Utility for conditional logging based on environment
 * Optimized to minimize logs in production
 */

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Track error frequency to avoid flooding logs
const errorCache: Record<string, { count: number, lastTime: number }> = {};

/**
 * Log a message only in development mode
 * Optionally filter out certain repetitive messages
 */
export function devLog(...args: any[]): void {
  if (isDevelopment) {
    // Skip logging periodic connection checks to reduce noise
    if (args[0] && typeof args[0] === 'string' && 
        args[0].includes('Performing periodic connection check')) {
      return;
    }
    console.log(...args);
  }
}

/**
 * Log an info message only in development mode
 */
export function devInfo(...args: any[]): void {
  if (isDevelopment) {
    console.info(...args);
  }
}

/**
 * Log a warning message only in development mode
 */
export function devWarn(...args: any[]): void {
  if (isDevelopment) {
    console.warn(...args);
  }
}

/**
 * Log an error message with rate limiting in production
 * In development, all errors are logged
 * In production, identical errors are rate-limited to avoid flooding logs
 */
export function logError(...args: any[]): void {
  // Ensure we don't have empty objects in the args
  const processedArgs = args.map(arg => {
    // Handle empty objects
    if (arg && typeof arg === 'object' && Object.keys(arg).length === 0) {
      return 'Empty object';
    }

    // Handle Error objects
    if (arg instanceof Error) {
      // Special handling for timeout errors
      if (arg.message.includes('timeout') || arg.name === 'TimeoutError') {
        return `Timeout Error: ${arg.message}`;
      }
      return `${arg.name}: ${arg.message}${arg.stack ? `\n${arg.stack}` : ''}`;
    }

    // Handle null or undefined
    if (arg === null) {
      return 'null';
    }

    if (arg === undefined) {
      return 'undefined';
    }

    return arg;
  });

  if (isDevelopment) {
    // In development, log all errors
    // console.error(...processedArgs);
    return;
  }

  // In production, rate-limit identical errors
  const errorKey = processedArgs.map(arg =>
    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
  ).join('|');

  const now = Date.now();
  const cached = errorCache[errorKey];

  if (!cached) {
    // First occurrence of this error
    errorCache[errorKey] = { count: 1, lastTime: now };
    console.error(...processedArgs);
    return;
  }

  // Only log the same error once per minute in production
  // and include a count of occurrences
  if (now - cached.lastTime > 60000) {
    cached.count++;
    cached.lastTime = now;

    if (cached.count > 1) {
      console.error(`[Repeated ${cached.count} times]`, ...processedArgs);
    } else {
      console.error(...processedArgs);
    }

    // Clean up old entries
    cleanErrorCache();
  }
}

/**
 * Always log a message (use sparingly)
 * In production, this should only be used for critical user-facing information
 */
export function alwaysLog(...args: any[]): void {
  console.log(...args);
}

/**
 * Clean up error cache to prevent memory leaks
 */
function cleanErrorCache(): void {
  const now = Date.now();
  const maxAge = 3600000; // 1 hour

  Object.keys(errorCache).forEach(key => {
    if (now - errorCache[key].lastTime > maxAge) {
      delete errorCache[key];
    }
  });
}

