"use server"

import { createClient } from '@/utils/supabase/client' // Remove the self-import
// Remove the self-import

/**
 * Check if the database schema matches what we expect
 * This helps identify any missing tables or columns
 */
export type SchemaCheckResult = {
  tables: Record<string, { exists: boolean, columns: Record<string, boolean> }>,
  missingTables: string[],
  missingColumns: { table: string, column: string }[],
  success: boolean,
  error?: string
}

export async function checkDatabaseSchema(): Promise<SchemaCheckResult> {
  try {
    const supabase = await createClient()
    const results = {
      tables: {} as Record<string, { exists: boolean, columns: Record<string, boolean> }>,
      missingTables: [] as string[],
      missingColumns: [] as { table: string, column: string }[],
      success: true
    }

    // Define expected tables and columns
    const expectedSchema = {
      users: ['id', 'mobile', 'shop_name', 'address', 'is_admin', 'created_at'],
      categories: ['id', 'name', 'created_at'],
      products: ['id', 'name', 'description', 'price', 'image_url', 'category_id', 'in_stock', 'barcode', 'stock_quantity', 'last_stock_update', 'created_at'],
      cart_items: ['id', 'user_id', 'product_id', 'quantity', 'created_at'],
      orders: ['id', 'user_id', 'status', 'total_amount', 'delivery_address', 'created_at', 'delivery_status', 'estimated_delivery', 'payment_method', 'payment_status', 'updated_at'],
      order_items: ['id', 'order_id', 'product_id', 'quantity', 'price_per_unit', 'created_at'],
      payments: ['id', 'order_id', 'amount', 'payment_method', 'status', 'transaction_id', 'payment_details', 'payment_url', 'payment_gateway_id', 'reference', 'created_at', 'updated_at'],
      receipts: ['id', 'payment_id', 'receipt_number', 'amount', 'issued_date', 'receipt_url', 'created_at', 'updated_at']
    }

    // Check each table
    for (const [tableName, expectedColumns] of Object.entries(expectedSchema)) {
      // Check if table exists
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
        .match(() => ({ data: null, error: { message: `Table ${tableName} does not exist` } }))

      const tableExists = !error
      results.tables[tableName] = { exists: tableExists, columns: {} }

      if (!tableExists) {
        results.missingTables.push(tableName)
        results.success = false
        continue
      }

      // Check columns by trying to select each one
      for (const column of expectedColumns) {
        try {
          const { error: columnError } = await supabase
            .from(tableName)
            .select(column)
            .limit(1)
            .match(() => ({ error: { message: `Column ${column} does not exist in table ${tableName}` } }))

          const columnExists = !columnError
          results.tables[tableName].columns[column] = columnExists

          if (!columnExists) {
            results.missingColumns.push({ table: tableName, column })
            results.success = false
          }
        } catch (e) {
          results.tables[tableName].columns[column] = false
          results.missingColumns.push({ table: tableName, column })
          results.success = false
        }
      }
    }

    return results
  } catch (error) {
    console.error('Error checking database schema:', error)
    return {
      tables: {},
      missingTables: [],
      missingColumns: [],
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

/**
 * Fix missing tables and columns in the database
 */
export async function fixDatabaseSchema() {
  try {
    const schemaCheck = await checkDatabaseSchema()
    
    if (schemaCheck.success) {
      return { success: true, message: 'Database schema is already correct' }
    }
    
    const supabase = await createClient()
    const results = {
      createdTables: [] as string[],
      addedColumns: [] as { table: string, column: string }[],
      errors: [] as string[],
      success: true
    }
    
    // Create missing tables
    for (const tableName of schemaCheck.missingTables) {
      try {
        let createTableSQL = ''
        
        switch (tableName) {
          case 'users':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                mobile TEXT NOT NULL,
                shop_name TEXT,
                address TEXT,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          case 'categories':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS categories (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name TEXT NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          case 'products':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS products (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name TEXT NOT NULL,
                description TEXT,
                price NUMERIC(10, 2) NOT NULL,
                image_url TEXT,
                category_id UUID,
                in_stock BOOLEAN DEFAULT TRUE,
                barcode TEXT,
                stock_quantity INTEGER,
                last_stock_update TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          case 'cart_items':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS cart_items (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL,
                product_id UUID NOT NULL,
                quantity INTEGER NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          case 'orders':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS orders (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL,
                status TEXT NOT NULL,
                total_amount NUMERIC(10, 2) NOT NULL,
                delivery_address TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                delivery_status TEXT,
                estimated_delivery TEXT,
                payment_method TEXT,
                payment_status TEXT,
                updated_at TIMESTAMP WITH TIME ZONE
              );
            `
            break
            
          case 'order_items':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS order_items (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                order_id UUID NOT NULL,
                product_id UUID NOT NULL,
                quantity INTEGER NOT NULL,
                price_per_unit NUMERIC(10, 2) NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          case 'payments':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS payments (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                order_id UUID NOT NULL,
                amount NUMERIC(10, 2) NOT NULL,
                payment_method TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'Pending',
                transaction_id TEXT,
                payment_details JSONB,
                payment_url TEXT,
                payment_gateway_id TEXT,
                reference TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          case 'receipts':
            createTableSQL = `
              CREATE TABLE IF NOT EXISTS receipts (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                payment_id UUID NOT NULL,
                receipt_number TEXT NOT NULL,
                amount NUMERIC(10, 2) NOT NULL,
                issued_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                receipt_url TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
              );
            `
            break
            
          default:
            results.errors.push(`No schema definition for table ${tableName}`)
            continue
        }

        // Execute the SQL to create the table
        const { error } = await supabase.rpc('execute_sql', { sql: createTableSQL })

        if (error) {
          results.errors.push(`Error creating table ${tableName}: ${error.message}`)
          results.success = false
        } else {
          results.createdTables.push(tableName)
        }
      } catch (error) {
        results.errors.push(`Exception creating table ${tableName}: ${error instanceof Error ? error.message : String(error)}`)
        results.success = false
      }
    }

    // Add missing columns
    for (const { table, column } of schemaCheck.missingColumns) {
      // Skip tables that don't exist yet
      if (schemaCheck.missingTables.includes(table as string)) {
        continue
      }

      try {
        let dataType = 'TEXT'

        // Determine the data type based on column name and table
        if (column === 'id' || column.endsWith('_id')) {
          dataType = 'UUID'
        } else if (column === 'price' || column === 'amount' || column === 'price_per_unit') {
          dataType = 'NUMERIC(10, 2)'
        } else if (column === 'quantity' || column === 'stock_quantity') {
          dataType = 'INTEGER'
        } else if (column === 'in_stock' || column === 'is_admin') {
          dataType = 'BOOLEAN'
        } else if (column === 'created_at' || column === 'updated_at' || column === 'last_stock_update' || column === 'issued_date') {
          dataType = 'TIMESTAMP WITH TIME ZONE'
        } else if (column === 'payment_details') {
          dataType = 'JSONB'
        }

        const alterTableSQL = `
          ALTER TABLE ${table} 
          ADD COLUMN IF NOT EXISTS ${column} ${dataType};
        `

        const { error } = await supabase.rpc('execute_sql', { sql: alterTableSQL })

        if (error) {
          results.errors.push(`Error adding column ${column} to table ${table}: ${error.message}`)
          results.success = false
        } else {
          results.addedColumns.push({ table, column })
        }
      } catch (error) {
        results.errors.push(`Exception adding column ${column} to table ${table}: ${error instanceof Error ? error.message : String(error)}`)
        results.success = false
      }
    }

    return results
  } catch (error) {
    console.error('Error fixing database schema:', error)
    return {
      createdTables: [],
      addedColumns: [],
      errors: [error instanceof Error ? error.message : String(error)],
      success: false
    }
  }
}







