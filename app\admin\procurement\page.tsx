"use client"

import { getOrdersForWholesalerProcurement } from "@/lib/order-service"
import { getCategories } from "@/lib/data-service"

export default async function ProcurementPage() {
  const procurementList = await getOrdersForWholesalerProcurement()
  const categories = await getCategories()

  // Create a map of category IDs to names for easier lookup
  const categoryMap = categories.reduce(
    (acc, category) => {
      acc[category.id] = category.name
      return acc
    },
    {} as Record<string, string>,
  )

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Wholesaler Procurement List</h1>

      <div className="bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h2 className="text-lg font-bold">Products to Order</h2>
          <button
            onClick={() => window.print()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors print:hidden"
          >
            Print List
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product Name
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider print:hidden">
                  Ordered
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {procurementList.length > 0 ? (
                procurementList.map((item) => (
                  <tr key={item.product_id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{categoryMap[item.category_id] || "Uncategorized"}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{item.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm font-medium text-gray-900">{item.total_quantity}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right print:hidden">
                      <input type="checkbox" className="h-4 w-4 text-blue-600 border-gray-300 rounded" />
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                    No products to order
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
