"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
// Simple PWA detection function
const isRunningAsPWA = () => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true;
};

// Simple storage functions for routes
const saveLastAuthenticatedRoute = (route: string) => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('lastAuthRoute', route);
};

const getLastAuthenticatedRoute = () => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('lastAuthRoute');
};

interface AuthRedirectProps {
  redirectLoggedInTo?: string
  redirectLoggedOutTo?: string
  children: React.ReactNode
}

export function AuthRedirect({
  redirectLoggedInTo,
  redirectLoggedOutTo,
  children
}: AuthRedirectProps) {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [isPWA, setIsPWA] = useState(false)

  // Check if running as PWA on component mount
  useEffect(() => {
    setIsPWA(isRunningAsPWA())
  }, [])

  useEffect(() => {
    // Only redirect after auth state is determined
    if (!loading) {
      if (user && redirectLoggedInTo) {
        // User is logged in and we want to redirect them

        // If the app is installed as PWA, check for a saved route
        if (isPWA) {
          const lastRoute = getLastAuthenticatedRoute()
          if (lastRoute) {
            router.push(lastRoute)
            return
          }
        }

        // Otherwise use the default redirect
        router.push(redirectLoggedInTo)

        // Save this route for future PWA launches
        if (isPWA) {
          saveLastAuthenticatedRoute(redirectLoggedInTo)
        }
      } else if (!user && redirectLoggedOutTo) {
        // User is not logged in and we want to redirect them
        router.push(redirectLoggedOutTo)
      }
    }
  }, [user, loading, redirectLoggedInTo, redirectLoggedOutTo, router, isPWA])

  // If still loading or about to redirect, you could show a loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show children in these cases:
  // 1. If we're still loading (children might have their own loading state)
  // 2. If we're not redirecting based on auth state
  // 3. If we're on the landing page and not logged in
  if (loading || (user && !redirectLoggedInTo) || (!user && !redirectLoggedOutTo)) {
    return <>{children}</>
  }

  // Add a timeout to prevent infinite loading
  useEffect(() => {
    const timeout = setTimeout(() => {
      // If we're still on the loading screen after 5 seconds, just show the content
      console.log('Redirect timeout - showing content')
      // We can't set loading directly, so we'll just force a re-render
      router.refresh()
    }, 5000)

    return () => clearTimeout(timeout)
  }, [])

  // Otherwise, show a minimal loading state while the redirect happens
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting...</p>
      </div>
    </div>
  )
}
