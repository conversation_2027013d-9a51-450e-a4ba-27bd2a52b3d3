"use client"

import { useEffect, useState } from "react"
import { useOffline } from "@/contexts/offline-context"
import { WifiOff, Wif<PERSON>, <PERSON>, BellOff } from "lucide-react"
import Link from "next/link"

export function OfflineIndicator() {
  const { offline, hasOfflineSupport, notificationsEnabled, enableNotifications } = useOffline()
  const [visible, setVisible] = useState(false)
  const [showNotificationPrompt, setShowNotificationPrompt] = useState(false)

  // Show the indicator when offline status changes - optimized for performance
  useEffect(() => {
    if (offline) {
      setVisible(true)
      // Keep visible while offline
      return () => {}
    } else {
      // When coming back online, show for 2 seconds then hide (reduced from 3s)
      if (visible) {
        const timer = setTimeout(() => {
          setVisible(false)
        }, 2000) // Reduced from 3000ms to 2000ms
        return () => clearTimeout(timer)
      }
    }
  }, [offline, visible])

  // Show notification prompt after 7 days if not enabled (extended from 3 days)
  useEffect(() => {
    if (hasOfflineSupport && !notificationsEnabled) {
      const lastPrompt = localStorage.getItem('notificationPromptShown')
      const sevenDays = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds

      if (!lastPrompt || Date.now() - parseInt(lastPrompt) > sevenDays) {
        const timer = setTimeout(() => {
          setShowNotificationPrompt(true)
          localStorage.setItem('notificationPromptShown', Date.now().toString())
        }, 60000) // Show after 60 seconds of using the app (increased from 30s)

        return () => clearTimeout(timer)
      }
    }
  }, [hasOfflineSupport, notificationsEnabled])

  // Handle enabling notifications
  const handleEnableNotifications = async () => {
    await enableNotifications()
    setShowNotificationPrompt(false)
  }

  // Don't render if not visible and not offline
  if (!visible && !offline) {
    return null
  }

  return (
    <>
      {/* Offline indicator */}
      <div
        className={`fixed bottom-24 left-1/2 transform -translate-x-1/2 z-40 transition-all duration-300 ${
          visible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
        }`}
      >
        <div className={`flex items-center gap-2 px-4 py-2 rounded-full shadow-lg ${
          offline
            ? 'bg-red-100 text-red-700 border border-red-200'
            : 'bg-green-100 text-green-700 border border-green-200'
        }`}>
          {offline ? (
            <>
              <WifiOff size={16} />
              <span className="text-sm font-medium">You're offline</span>
              <Link
                href="/offline"
                className="text-xs bg-white bg-opacity-50 px-2 py-0.5 rounded-full ml-1"
              >
                Details
              </Link>
            </>
          ) : (
            <>
              <Wifi size={16} />
              <span className="text-sm font-medium">Back online</span>
            </>
          )}
        </div>
      </div>

      {/* Notification permission prompt */}
      {showNotificationPrompt && (
        <div className="fixed bottom-24 left-1/2 transform -translate-x-1/2 z-40 w-[90%] max-w-xs">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-lg">
            <div className="flex items-start gap-3">
              <div className="bg-blue-100 rounded-full p-2">
                <Bell size={20} className="text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-blue-900 text-sm">Enable notifications</h3>
                <p className="text-blue-700 text-xs mt-1">
                  Get notified about order updates and special offers, even when you're offline.
                </p>
                <div className="flex gap-2 mt-3">
                  <button
                    onClick={handleEnableNotifications}
                    className="bg-blue-600 text-white text-xs px-3 py-1.5 rounded-md flex-1 flex items-center justify-center gap-1"
                  >
                    <Bell size={14} />
                    Enable
                  </button>
                  <button
                    onClick={() => setShowNotificationPrompt(false)}
                    className="bg-gray-200 text-gray-700 text-xs px-3 py-1.5 rounded-md flex-1"
                  >
                    Not now
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
