"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"

export default function MockLoginPage() {
  const [mobileNumber, setMobileNumber] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()

  const handleLogin = async () => {
    if (!mobileNumber || !password) {
      setError("Please enter both mobile number and password")
      return
    }

    setLoading(true)
    setError("")

    try {
      // Simulate successful login
      localStorage.setItem("mockUser", JSON.stringify({
        id: "mock-user-id",
        mobile: mobileNumber,
        shop_name: "Mock Shop",
        is_admin: false,
        created_at: new Date().toISOString()
      }))

      // Redirect to products page
      window.location.href = "/mock-products"
    } catch (err) {
      console.error("Login error:", err)
      setError("An unexpected error occurred. Please try again later.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 flex-grow bg-gray-50">
      <h2 className="text-2xl font-bold text-center mb-6">Mock Login</h2>
      <p className="mb-4 text-center text-gray-600">This is a mock login page that bypasses Supabase authentication</p>

      {error && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">{error}</div>}

      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-gray-700 mb-1">Mobile Number</label>
          <input
            type="tel"
            value={mobileNumber}
            onChange={(e) => setMobileNumber(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Enter your mobile number"
          />
        </div>

        <div>
          <label className="block text-gray-700 mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Enter your password"
          />
        </div>
      </div>

      <div className="space-y-3">
        <button
          onClick={handleLogin}
          disabled={loading}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md text-lg font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400"
        >
          {loading ? "Logging In..." : "Mock Login"}
        </button>
      </div>

      <p className="text-center mt-6">
        <Link href="/" className="text-blue-600 hover:underline">
          ← Back to Welcome Screen
        </Link>
      </p>
    </div>
  )
}
