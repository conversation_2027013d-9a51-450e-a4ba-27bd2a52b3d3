import { NextRequest, NextResponse } from 'next/server'
import { checkDatabaseSchema, fixDatabaseSchema } from '@/lib/db-schema-check'
import { createServerClient } from '@/utils/supabase/client'

export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Get user details to check if admin
    const { data: userData } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()
      
    if (!userData?.is_admin) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
    }
    
    // Check database schema
    const schemaCheck = await checkDatabaseSchema()
    
    return NextResponse.json({
      success: true,
      schemaCheck
    })
  } catch (error) {
    console.error('Error in check-schema API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const supabase = await createServerClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Get user details to check if admin
    const { data: userData } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()
      
    if (!userData?.is_admin) {
      return NextResponse.json({ error: 'Forbidden - Admin access required' }, { status: 403 })
    }
    
    // Fix database schema
    const fixResults = await fixDatabaseSchema()
    
    return NextResponse.json({
      success: true,
      fixResults
    })
  } catch (error) {
    console.error('Error in fix-schema API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}
