"use client"

import { createClient } from '@/utils/supabase/client'

/**
 * Format mobile number to standard format
 * @param mobile The mobile number to format
 * @returns Formatted mobile number
 */
export function formatMobile(mobile: string): string {
  // Remove any non-digit characters
  const digits = mobile.replace(/\D/g, '')
  
  // If it starts with 0, convert to international format
  if (digits.startsWith('0')) {
    return `27${digits.substring(1)}`
  }
  
  // If it already starts with 27, keep it as is
  if (digits.startsWith('27')) {
    return digits
  }
  
  // If it's just the number without country code or leading 0
  if (digits.length === 9) {
    return `27${digits}`
  }
  
  // If it's already in international format
  if (digits.length === 11 && digits.startsWith('27')) {
    return digits
  }
  
  // If we can't determine the format, return as is
  return digits
}

/**
 * Validate if mobile number is valid
 * @param mobile The mobile number to validate
 * @returns true if valid, false otherwise
 */
export function isValidMobile(mobile: string): boolean {
  // Check if it's a valid South African mobile number
  // Accept both local (08...) and international (278...) formats
  const regex = /^(27[678]|0[678])\d{8}$/
  return regex.test(mobile)
}

/**
 * Performs a direct login with Supabase and handles profile creation
 * @param mobileNumber The user's mobile number
 * @param password The user's password
 * @returns Object with success status and error message if applicable
 */
export async function performDirectLogin(mobileNumber: string, password: string) {
  try {
    // Validate mobile number
    const formattedMobile = formatMobile(mobileNumber)
    if (!isValidMobile(formattedMobile)) {
      return {
        success: false,
        error: 'Invalid mobile number format'
      }
    }

    // Get Supabase client
    const supabase = createClient()

    // Sign in with email and password
    const { data, error: signInError } = await supabase.auth.signInWithPassword({
      email: formattedMobile,  // Use mobile directly as email
      password
    })

    if (signInError) {
      return {
        success: false,
        error: signInError.message || "Login failed"
      }
    }

    if (!data?.user) {
      return {
        success: false,
        error: "Login failed - no user returned"
      }
    }

    // Create or update user profile with proper schema fields
    const { error: profileError } = await supabase
      .from('users')
      .upsert([
        {
          id: data.user.id,
          mobile: formattedMobile,
          full_name: data.user.user_metadata?.full_name || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      {
        onConflict: 'id',
        ignoreDuplicates: true
      })

    if (profileError) {
      return {
        success: false,
        error: profileError.message || 'Failed to create/update profile'
      }
    }

    // Store login state
    localStorage.setItem('userLoggedIn', 'true')
    localStorage.setItem('userId', data.user.id)

    return { success: true }
  } catch (err) {
    console.error('Login error:', err)
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.'
    }
  }
}

// Helper function to validate mobile number format

