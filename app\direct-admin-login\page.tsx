"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { createClient } from '@/utils/supabase/client'

export default function DirectAdminLoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const handleAdminLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    if (!email || !password) {
      setError("Please enter both email and password")
      return
    }
    setLoading(true)
    try {
      const supabase = createClient()
      // Sign in
      const { data, error: authError } = await supabase.auth.signInWithPassword({ email, password })
      if (authError || !data.user) {
        setError(authError?.message || "<PERSON><PERSON> failed. Please check your credentials.")
        return
      }
      // Check admin flag
      const { data: userData, error: fetchError } = await supabase
        .from('users')
        .select('is_admin')
        .eq('id', data.user.id)
        .single()
      if (fetchError || !userData?.is_admin) {
        await supabase.auth.signOut()
        setError('Not authorized as admin')
        return
      }
      setSuccess('Login successful! Redirecting to admin portal...')
      setTimeout(() => { window.location.href = '/admin' }, 1000)
    } catch (err) {
      console.error("Admin login error:", err)
      setError("An unexpected error occurred.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6 flex-grow bg-gray-50 flex flex-col">
      <div className="mb-6">
        <Link href="/" className="inline-flex items-center text-blue-600 hover:text-blue-800">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Home
        </Link>
      </div>

      <div className="max-w-md w-full mx-auto bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-center mb-6">Direct Admin Login</h1>
        <p className="text-center text-gray-600 mb-6">
          This is a direct admin login that bypasses middleware checks.
        </p>

        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
            {success}
          </div>
        )}

        <form onSubmit={handleAdminLogin}>
          <div className="mb-4">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
              placeholder="••••••••"
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-400"
          >
            {loading ? "Logging in..." : "Login to Admin Portal"}
          </button>
        </form>

        <div className="mt-6 text-center text-sm text-gray-500">
          <p>This login is for authorized administrators only.</p>
          <div className="flex justify-between mt-2">
            <Link href="/auth" className="text-blue-600 hover:text-blue-800">
              Regular user login
            </Link>
            <a href="mailto:<EMAIL>?subject=Admin%20Access%20Request" className="text-blue-600 hover:text-blue-800">
              Request admin access
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
