// "use server"

// import { createServerClient } from '@/utils/supabase/client'
// import type { Product, Category, Order } from "./types"
// import { hasRoleInShop, isShopAdmin, isShopOwner } from '@/utils/role-utils'
// import { z } from 'zod'

// // Define schemas for input validation
// const productNameSchema = z.string().min(2, "Product name must be at least 2 characters")
// const categoryNameSchema = z.string().min(2, "Category name must be at least 2 characters")

// // Check if user is an admin of a shop
// export async function isShopAdminOrOwner(userId: string, shopId: string) {
//   try {
//     return await hasRoleInShop(userId, shopId, ['Owner', 'Admin'])
//   } catch (error) {
//     console.error("Exception checking shop admin status:", error)
//     return false
//   }
// }

// // Check if user is an owner of a shop
// export async function isShopOwnerCheck(userId: string, shopId: string) {
//   try {
//     return await hasRoleInShop(userId, shopId, ['Owner'])
//   } catch (error) {
//     console.error("Exception checking shop owner status:", error)
//     return false
//   }
// }

// // Check if user is an admin of any shop
// export async function isAdminOfAnyShop(userId: string) {
//   try {
//     return await isShopAdmin(userId)
//   } catch (error) {
//     console.error("Exception checking admin status:", error)
//     return false
//   }
// }

// // Check if user is an owner of any shop
// export async function isOwnerOfAnyShop(userId: string) {
//   try {
//     return await isShopOwner(userId)
//   } catch (error) {
//     console.error("Exception checking owner status:", error)
//     return false
//   }
// }

// // Product management
// export async function createProduct(shopId: string, product: Omit<Product, "id" | "created_at" | "shop_id">) {
//   const supabase = await createServerClient()

//   // Validate product name
//   const validatedProductName = productNameSchema.safeParse(product.name)

//   if (!validatedProductName.success) {
//     console.error("Error validating product name:", validatedProductName.error)
//     return null
//   }

//   const { data, error } = await supabase.from("products").insert({
//     ...product,
//     shop_id: shopId
//   }).select().single()

//   if (error) {
//     console.error("Error creating product:", error)
//     return null
//   }

//   return data as Product
// }

// export async function updateProduct(id: string, product: Partial<Omit<Product, "id" | "created_at">>) {
//   const supabase = await createServerClient()
//   const { data, error } = await supabase.from("products").update(product).eq("id", id).select().single()

//   if (error) {
//     console.error("Error updating product:", error)
//     return null
//   }

//   return data as Product
// }

// export async function deleteProduct(id: string) {
//   const supabase = await createServerClient()
//   const { error } = await supabase.from("products").delete().eq("id", id)

//   if (error) {
//     console.error("Error deleting product:", error)
//     return false
//   }

//   return true
// }

// // Product retrieval
// export async function getProduct(id: string) {
//   const supabase = await createServerClient()
//   const { data, error } = await supabase.from('products').select('*').eq('id', id).single()
//   if (error || !data) {
//     console.error('Error fetching product:', error)
//     return null
//   }
//   return data
// }

// // Category management
// export async function createCategory(name: string) {
//   const supabase = await createServerClient()

//   // Validate category name
//   const validatedCategoryName = categoryNameSchema.safeParse(name)

//   if (!validatedCategoryName.success) {
//     console.error("Error validating category name:", validatedCategoryName.error)
//     return null
//   }

//   const { data, error } = await supabase.from("categories").insert({ name }).select().single()

//   if (error) {
//     console.error("Error creating category:", error)
//     return null
//   }

//   return data as Category
// }

// export async function updateCategory(id: string, name: string) {
//   const supabase = await createServerClient()

//   // Validate category name
//   const validatedCategoryName = categoryNameSchema.safeParse(name)

//   if (!validatedCategoryName.success) {
//     console.error("Error validating category name:", validatedCategoryName.error)
//     return null
//   }

//   const { data, error } = await supabase.from("categories").update({ name }).eq("id", id).select().single()

//   if (error) {
//     console.error("Error updating category:", error)
//     return null
//   }

//   return data as Category
// }

// export async function deleteCategory(id: string) {
//   const supabase = await createServerClient()

//   // First check if there are products using this category
//   const { count, error: countError } = await supabase
//     .from("products")
//     .select("*", { count: "exact", head: true })
//     .eq("category_id", id)

//   if (countError) {
//     console.error("Error checking products:", countError)
//     return { success: false, message: "Error checking if category is in use" }
//   }

//   if (count && count > 0) {
//     return { success: false, message: "Cannot delete category that has products" }
//   }

//   const { error } = await supabase.from("categories").delete().eq("id", id)

//   if (error) {
//     console.error("Error deleting category:", error)
//     return { success: false, message: "Error deleting category" }
//   }

//   return { success: true, message: "Category deleted successfully" }
// }

// // Order management
// export async function getAllOrders() {
//   const supabase = await createServerClient()
//   const { data, error } = await supabase
//     .from("orders")
//     .select(`
//       *,
//       user:users(id, mobile, shop_name)
//     `)
//     .order("created_at", { ascending: false })

//   if (error) {
//     console.error("Error fetching all orders:", error)
//     return []
//   }

//   return data
// }

// export async function updateOrderStatus(orderId: string, status: "Processing" | "Delivered" | "Cancelled") {
//   const supabase = await createServerClient()
//   const { data, error } = await supabase.from("orders").update({ status }).eq("id", orderId).select().single()

//   if (error) {
//     console.error("Error updating order status:", error)
//     return null
//   }

//   return data as Order
// }

// // User management
// export async function getAllUsers() {
//   const supabase = await createServerClient()
//   const { data, error } = await supabase
//     .from("users")
//     .select(`
//       *,
//       roles:user_roles(
//         role_id,
//         shop_id,
//         roles:roles(name)
//       )
//     `)
//     .order("created_at", { ascending: false })

//   if (error) {
//     console.error("Error fetching all users:", error)
//     return []
//   }

//   return data
// }

// export async function assignUserRole(userId: string, shopId: string, roleId: number) {
//   const supabase = await createServerClient()
//   const { data, error } = await supabase
//     .from("user_roles")
//     .upsert({
//       user_id: userId,
//       shop_id: shopId,
//       role_id: roleId
//     })
//     .select()
//     .single()

//   if (error) {
//     console.error("Error assigning user role:", error)
//     return null
//   }

//   return data
// }

// export async function removeUserRole(userId: string, shopId: string, roleId: number) {
//   const supabase = await createServerClient()
//   const { error } = await supabase
//     .from("user_roles")
//     .delete()
//     .match({
//       user_id: userId,
//       shop_id: shopId,
//       role_id: roleId
//     })

//   if (error) {
//     console.error("Error removing user role:", error)
//     return false
//   }

//   return true
// }

// // Dashboard stats
// export async function getDashboardStats(supabase: unknown, p0: { includeRevenue: boolean }, shopId: string) {
//   const supabase = await createServerClient()

//   // Get total users (members of this shop)
//   const { count: userCount, error: userError } = await supabase
//     .from("user_roles")
//     .select("*", { count: "exact", head: true })
//     .eq("shop_id", shopId)

//   // Get total products for this shop
//   const { count: productCount, error: productError } = await supabase
//     .from("products")
//     .select("*", { count: "exact", head: true })
//     .eq("shop_id", shopId)

//   // Get total orders for this shop
//   const { count: orderCount, error: orderError } = await supabase
//     .from("orders")
//     .select("*", { count: "exact", head: true })
//     .eq("shop_id", shopId)

//   // Get total revenue for this shop
//   const { data: revenueData, error: revenueError } = await supabase
//     .from("orders")
//     .select("total_amount")
//     .eq("shop_id", shopId)
//     .eq("status", "Delivered")

//   const totalRevenue = revenueData?.reduce((sum, order) => sum + Number(order.total_amount), 0) || 0

//   if (userError || productError || orderError || revenueError) {
//     console.error("Error fetching dashboard stats:", userError || productError || orderError || revenueError)
//   }

//   return {
//     userCount: userCount || 0,
//     productCount: productCount || 0,
//     orderCount: orderCount || 0,
//     totalRevenue,
//   }
// }

// // Get product by barcode
// export async function getProductByBarcode(shopId: string, barcode: string) {
//   try {
//     const supabase = await createServerClient()

//     const { data, error } = await supabase
//       .from("products")
//       .select("*")
//       .eq("shop_id", shopId)
//       .eq("barcode", barcode)
//       .single()

//     if (error) {
//       console.error("Error fetching product by barcode:", error)
//       return null
//     }

//     return data as Product
//   } catch (err) {
//     console.error("Exception fetching product by barcode:", err)
//     return null
//   }
// }

// // Update product stock
// export async function updateProductStock(productId: string, quantity: number) {
//   try {
//     const supabase = await createServerClient()

//     // First get the current product
//     const { data: product, error: fetchError } = await supabase
//       .from("products")
//       .select("*")
//       .eq("id", productId)
//       .single()

//     if (fetchError) {
//       console.error("Error fetching product for stock update:", fetchError)
//       throw new Error("Product not found")
//     }

//     // Update the stock quantity
//     const { error: updateError } = await supabase
//       .from("products")
//       .update({
//         stock_quantity: quantity,
//         is_available: quantity > 0,
//         last_stock_update: new Date().toISOString()
//       })
//       .eq("id", productId)

//     if (updateError) {
//       console.error("Error updating product stock:", updateError)
//       throw new Error("Failed to update stock")
//     }

//     // Create stock history entry
//     const { error: historyError } = await supabase
//       .from("stock_history")
//       .insert({
//         product_id: productId,
//         quantity_change: quantity - (product.stock_quantity || 0),
//         new_quantity: quantity,
//         action: "manual_update",
//         notes: "Updated via barcode scanner"
//       })

//     if (historyError) {
//       console.error("Error creating stock history:", historyError)
//       // We don't throw here as the main update was successful
//     }

//     return true
//   } catch (err) {
//     console.error("Exception updating product stock:", err)
//     throw err
//   }
// }

// // Get stock sheet data
// export async function getStockSheetData(shopId: string) {
//   try {
//     const supabase = await createServerClient()

//     const { data: products, error: productsError } = await supabase
//       .from("products")
//       .select(`
//         id,
//         name,
//         barcode,
//         stock_quantity,
//         is_available,
//         price,
//         last_stock_update,
//         category:categories(name)
//       `)
//       .eq("shop_id", shopId)
//       .order("name")

//     if (productsError) {
//       console.error("Error fetching products for stock sheet:", productsError)
//       return []
//     }

//     return products
//   } catch (err) {
//     console.error("Exception fetching stock sheet data:", err)
//     return []
//   }
// }

// // Get stock history for a product
// export async function getProductStockHistory(productId: string) {
//   try {
//     const supabase = await createServerClient()

//     const { data, error } = await supabase
//       .from("stock_history")
//       .select("*")
//       .eq("product_id", productId)
//       .order("created_at", { ascending: false })

//     if (error) {
//       console.error("Error fetching stock history:", error)
//       return []
//     }

//     return data
//   } catch (err) {
//     console.error("Exception fetching stock history:", err)
//     return []
//   }
// }

// // Get inventory alerts
// export async function getInventoryAlerts(shopId: string) {
//   try {
//     const supabase = await createServerClient()

//     // Get alert settings
//     const { data: settingsData } = await supabase
//       .from("system_settings")
//       .select("*")
//       .eq("key", "inventory_alerts")
//       .eq("shop_id", shopId)
//       .single()

//     // Default settings if not found
//     const settings = {
//       lowStockThreshold: 5,
//       enableNotifications: true,
//       emailNotifications: false,
//       ...settingsData?.value
//     }

//     // Get products with low stock
//     const { data: products, error: productsError } = await supabase
//       .from("products")
//       .select(`
//         id,
//         name,
//         barcode,
//         stock_quantity,
//         last_stock_update,
//         category:categories(name)
//       `)
//       .eq("shop_id", shopId)
//       .or(`stock_quantity.lte.${settings.lowStockThreshold},stock_quantity.eq.0`)
//       .order("stock_quantity", { ascending: true })

//     if (productsError) {
//       console.error("Error fetching low stock products:", productsError)
//       return { alerts: [], settings }
//     }

//     // Format alerts
//     const alerts = products.map(product => ({
//       id: product.id,
//       name: product.name,
//       barcode: product.barcode,
//       category: product.category ? (product.category as any).name || "Uncategorized" : "Uncategorized",
//       currentStock: product.stock_quantity || 0,
//       threshold: settings.lowStockThreshold,
//       lastUpdated: product.last_stock_update || new Date().toISOString()
//     }))

//     return { alerts, settings }
//   } catch (err) {
//     console.error("Exception fetching inventory alerts:", err)
//     return {
//       alerts: [],
//       settings: {
//         lowStockThreshold: 5,
//         enableNotifications: true,
//         emailNotifications: false
//       }
//     }
//   }
// }

// // Update alert settings
// export async function updateAlertSettings(shopId: string, settings: any) {
//   try {
//     const supabase = await createServerClient()

//     // Check if settings exist
//     const { data: existingSettings, error: checkError } = await supabase
//       .from("system_settings")
//       .select("id")
//       .eq("key", "inventory_alerts")
//       .eq("shop_id", shopId)
//       .single()

//     if (checkError && checkError.code !== "PGRST116") {
//       console.error("Error checking alert settings:", checkError)
//       throw new Error("Failed to check alert settings")
//     }

//     if (existingSettings) {
//       // Update existing settings
//       const { error: updateError } = await supabase
//         .from("system_settings")
//         .update({ value: settings })
//         .eq("key", "inventory_alerts")
//         .eq("shop_id", shopId)

//       if (updateError) {
//         console.error("Error updating alert settings:", updateError)
//         throw new Error("Failed to update alert settings")
//       }
//     } else {
//       // Create new settings
//       const { error: insertError } = await supabase
//         .from("system_settings")
//         .insert({
//           key: "inventory_alerts",
//           shop_id: shopId,
//           value: settings
//         })

//       if (insertError) {
//         console.error("Error creating alert settings:", insertError)
//         throw new Error("Failed to create alert settings")
//       }
//     }

//     return true
//   } catch (err) {
//     console.error("Exception updating alert settings:", err)
//     throw err
//   }
// }

// // Get analytics data
// export async function getAnalyticsData(shopId: string, period: "7days" | "30days" | "90days") {
//   try {
//     const supabase = await createServerClient()

//     // Calculate date range
//     const endDate = new Date()
//     const startDate = new Date()

//     switch (period) {
//       case "7days":
//         startDate.setDate(startDate.getDate() - 7)
//         break
//       case "30days":
//         startDate.setDate(startDate.getDate() - 30)
//         break
//       case "90days":
//         startDate.setDate(startDate.getDate() - 90)
//         break
//     }

//     // Format dates for Supabase query
//     const startDateStr = startDate.toISOString()
//     const endDateStr = endDate.toISOString()

//     // Get previous period for comparison
//     const previousStartDate = new Date(startDate)
//     previousStartDate.setDate(previousStartDate.getDate() - (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
//     const previousStartDateStr = previousStartDate.toISOString()

//     // Get orders in current period
//     const { data: currentOrders, error: ordersError } = await supabase
//       .from("orders")
//       .select(`
//         id,
//         total_amount,
//         created_at,
//         order_items(product_id, quantity, price_per_unit)
//       `)
//       .eq("shop_id", shopId)
//       .gte("created_at", startDateStr)
//       .lte("created_at", endDateStr)

//     if (ordersError) {
//       console.error("Error fetching orders for analytics:", ordersError)
//       throw new Error("Failed to fetch orders data")
//     }

//     // Get orders in previous period for comparison
//     const { data: previousOrders, error: previousOrdersError } = await supabase
//       .from("orders")
//       .select("id, total_amount")
//       .eq("shop_id", shopId)
//       .gte("created_at", previousStartDateStr)
//       .lt("created_at", startDateStr)

//     if (previousOrdersError) {
//       console.error("Error fetching previous orders for analytics:", previousOrdersError)
//       // Continue without comparison data
//     }

//     // Get products data for names
//     const { data: products, error: productsError } = await supabase
//       .from("products")
//       .select("id, name, category_id")
//       .eq("shop_id", shopId)

//     if (productsError) {
//       console.error("Error fetching products for analytics:", productsError)
//       // Continue with limited product info
//     }

//     // Get categories data
//     const { data: categories, error: categoriesError } = await supabase
//       .from("categories")
//       .select("id, name")

//     if (categoriesError) {
//       console.error("Error fetching categories for analytics:", categoriesError)
//       // Continue with limited category info
//     }

//     // Create product lookup map
//     const productMap = (products || []).reduce((acc, product) => {
//       acc[product.id] = product
//       return acc
//     }, {} as Record<string, any>)

//     // Create category lookup map
//     const categoryMap = (categories || []).reduce((acc, category) => {
//       acc[category.id] = category.name
//       return acc
//     }, {} as Record<string, string>)

//     // Calculate sales overview
//     const totalSales = currentOrders.reduce((sum, order) => sum + Number(order.total_amount), 0)
//     const totalOrders = currentOrders.length
//     const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0

//     // Calculate previous period metrics for comparison
//     const previousTotalSales = (previousOrders || []).reduce((sum, order) => sum + Number(order.total_amount), 0)
//     const previousTotalOrders = (previousOrders || []).length

//     // Calculate growth percentages
//     const salesGrowth = previousTotalSales > 0
//       ? ((totalSales - previousTotalSales) / previousTotalSales) * 100
//       : 0
//     const ordersGrowth = previousTotalOrders > 0
//       ? ((totalOrders - previousTotalOrders) / previousTotalOrders) * 100
//       : 0

//     // Calculate product sales
//     const productSales: Record<string, { totalSold: number, revenue: number }> = {}

//     currentOrders.forEach(order => {
//       if (!order.order_items) return

//       order.order_items.forEach((item: any) => {
//         if (!productSales[item.product_id]) {
//           productSales[item.product_id] = { totalSold: 0, revenue: 0 }
//         }

//         productSales[item.product_id].totalSold += item.quantity
//         productSales[item.product_id].revenue += item.quantity * Number(item.price_per_unit)
//       })
//     })

//     // Format top products
//     const topProducts = Object.entries(productSales)
//       .map(([productId, stats]) => ({
//         id: productId,
//         name: productMap[productId]?.name || "Unknown Product",
//         totalSold: stats.totalSold,
//         revenue: stats.revenue
//       }))
//       .sort((a, b) => b.revenue - a.revenue)

//     // Calculate sales by category
//     const categorySales: Record<string, number> = {}
//     let totalCategorySales = 0

//     Object.entries(productSales).forEach(([productId, stats]) => {
//       const product = productMap[productId]
//       if (!product) return

//       const categoryId = product.category_id
//       const categoryName = categoryId ? (categoryMap[categoryId] || "Uncategorized") : "Uncategorized"

//       if (!categorySales[categoryName]) {
//         categorySales[categoryName] = 0
//       }

//       categorySales[categoryName] += stats.revenue
//       totalCategorySales += stats.revenue
//     })

//     // Format sales by category
//     const salesByCategory = Object.entries(categorySales)
//       .map(([category, sales]) => ({
//         category,
//         sales,
//         percentage: Math.round((sales / totalCategorySales) * 100)
//       }))
//       .sort((a, b) => b.sales - a.sales)

//     // Calculate sales by day
//     const salesByDay: Record<string, { sales: number, orders: number }> = {}

//     // Initialize all days in the period
//     const dayCount = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
//     for (let i = 0; i < dayCount; i++) {
//       const date = new Date(startDate)
//       date.setDate(date.getDate() + i)
//       const dateStr = date.toISOString().split('T')[0]
//       salesByDay[dateStr] = { sales: 0, orders: 0 }
//     }

//     // Fill in actual sales data
//     currentOrders.forEach(order => {
//       const dateStr = new Date(order.created_at).toISOString().split('T')[0]

//       if (!salesByDay[dateStr]) {
//         salesByDay[dateStr] = { sales: 0, orders: 0 }
//       }

//       salesByDay[dateStr].sales += Number(order.total_amount)
//       salesByDay[dateStr].orders += 1
//     })

//     // Format sales by day
//     const salesByDayArray = Object.entries(salesByDay)
//       .map(([date, data]) => ({
//         date,
//         sales: data.sales,
//         orders: data.orders
//       }))
//       .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

//     return {
//       salesOverview: {
//         totalSales,
//         totalOrders,
//         averageOrderValue,
//         comparisonPeriod: {
//           salesGrowth,
//           ordersGrowth
//         }
//       },
//       topProducts,
//       salesByCategory,
//       salesByDay: salesByDayArray
//     }
//   } catch (err) {
//     console.error("Exception fetching analytics data:", err)
//     throw err
//   }
// }
