"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, BarChart3, TrendingUp, DollarSign, ShoppingCart, Package, Users, Calendar, Download } from "lucide-react"
import { AdminHeader } from "@/components/admin/admin-header"
import { getAnalyticsData } from "@/lib/admin-service"

interface AnalyticsData {
  salesOverview: {
    totalSales: number
    totalOrders: number
    averageOrderValue: number
    comparisonPeriod: {
      salesGrowth: number
      ordersGrowth: number
    }
  }
  topProducts: {
    id: string
    name: string
    totalSold: number
    revenue: number
  }[]
  salesByCategory: {
    category: string
    sales: number
    percentage: number
  }[]
  salesByDay: {
    date: string
    sales: number
    orders: number
  }[]
}

export default function AnalyticsPage() {
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [period, setPeriod] = useState<"7days" | "30days" | "90days">("30days")

  // Load analytics data
  useEffect(() => {
    loadAnalyticsData()
  }, [period])

  const loadAnalyticsData = async () => {
    setLoading(true)
    setError("")
    
    try {
      const analyticsData = await getAnalyticsData(period)
      setData(analyticsData)
    } catch (err) {
      console.error("Error loading analytics data:", err)
      setError("Failed to load analytics data. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return `R ${amount.toFixed(2)}`
  }

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  // Generate chart colors
  const getChartColors = (index: number) => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 
      'bg-pink-500', 'bg-indigo-500', 'bg-red-500', 'bg-teal-500'
    ]
    return colors[index % colors.length]
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <AdminHeader title="Analytics Dashboard" />
      
      <div className="p-4 flex-grow">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/admin" className="mr-2">
              <ArrowLeft className="h-6 w-6" />
            </Link>
            <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          </div>
          
          <div className="flex">
            <div className="bg-white rounded-md shadow-sm p-1 flex">
              <button 
                onClick={() => setPeriod("7days")}
                className={`px-3 py-1 rounded-md text-sm ${
                  period === "7days" 
                    ? "bg-blue-600 text-white" 
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                7 Days
              </button>
              <button 
                onClick={() => setPeriod("30days")}
                className={`px-3 py-1 rounded-md text-sm ${
                  period === "30days" 
                    ? "bg-blue-600 text-white" 
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                30 Days
              </button>
              <button 
                onClick={() => setPeriod("90days")}
                className={`px-3 py-1 rounded-md text-sm ${
                  period === "90days" 
                    ? "bg-blue-600 text-white" 
                    : "text-gray-600 hover:bg-gray-100"
                }`}
              >
                90 Days
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4">
            {error}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : data ? (
          <div className="space-y-6">
            {/* Overview cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-500">Total Sales</h3>
                  <div className="bg-blue-100 p-2 rounded-md">
                    <DollarSign className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold mb-1">{formatCurrency(data.salesOverview.totalSales)}</div>
                <div className={`text-sm ${
                  data.salesOverview.comparisonPeriod.salesGrowth >= 0 
                    ? "text-green-600" 
                    : "text-red-600"
                }`}>
                  {formatPercentage(data.salesOverview.comparisonPeriod.salesGrowth)} from previous period
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-500">Total Orders</h3>
                  <div className="bg-green-100 p-2 rounded-md">
                    <ShoppingCart className="h-5 w-5 text-green-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold mb-1">{data.salesOverview.totalOrders}</div>
                <div className={`text-sm ${
                  data.salesOverview.comparisonPeriod.ordersGrowth >= 0 
                    ? "text-green-600" 
                    : "text-red-600"
                }`}>
                  {formatPercentage(data.salesOverview.comparisonPeriod.ordersGrowth)} from previous period
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-500">Average Order</h3>
                  <div className="bg-purple-100 p-2 rounded-md">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                </div>
                <div className="text-2xl font-bold mb-1">{formatCurrency(data.salesOverview.averageOrderValue)}</div>
                <div className="text-sm text-gray-500">
                  Per order average
                </div>
              </div>
              
              <div className="bg-white rounded-lg shadow-sm p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-500">Top Product</h3>
                  <div className="bg-yellow-100 p-2 rounded-md">
                    <Package className="h-5 w-5 text-yellow-600" />
                  </div>
                </div>
                <div className="text-lg font-bold mb-1 truncate">
                  {data.topProducts[0]?.name || "No data"}
                </div>
                <div className="text-sm text-gray-500">
                  {data.topProducts[0] ? `${data.topProducts[0].totalSold} units sold` : ""}
                </div>
              </div>
            </div>
            
            {/* Sales by day chart */}
            <div className="bg-white rounded-lg shadow-sm p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-medium">Sales Trend</h2>
                <button className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  Export
                </button>
              </div>
              
              <div className="h-64 relative">
                {/* Simple bar chart */}
                <div className="absolute inset-0 flex items-end">
                  {data.salesByDay.map((day, index) => (
                    <div key={index} className="flex-1 flex flex-col items-center">
                      <div 
                        className="w-4/5 bg-blue-500 rounded-t-sm" 
                        style={{ 
                          height: `${Math.max(5, (day.sales / Math.max(...data.salesByDay.map(d => d.sales))) * 100)}%` 
                        }}
                      ></div>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(day.date).toLocaleDateString(undefined, { weekday: 'short' })}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Two column layout */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top products */}
              <div className="bg-white rounded-lg shadow-sm p-4">
                <h2 className="text-lg font-medium mb-4">Top Products</h2>
                
                <div className="space-y-4">
                  {data.topProducts.slice(0, 5).map((product, index) => (
                    <div key={product.id} className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.totalSold} units · {formatCurrency(product.revenue)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Sales by category */}
              <div className="bg-white rounded-lg shadow-sm p-4">
                <h2 className="text-lg font-medium mb-4">Sales by Category</h2>
                
                <div className="space-y-4">
                  {data.salesByCategory.map((category, index) => (
                    <div key={index}>
                      <div className="flex justify-between mb-1">
                        <span className="font-medium">{category.category}</span>
                        <span className="text-gray-500">{formatCurrency(category.sales)} ({category.percentage}%)</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div 
                          className={`h-2.5 rounded-full ${getChartColors(index)}`} 
                          style={{ width: `${category.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">No Analytics Data</h3>
            <p className="text-gray-500">There is no sales data available for the selected period.</p>
          </div>
        )}
      </div>
    </div>
  )
}
