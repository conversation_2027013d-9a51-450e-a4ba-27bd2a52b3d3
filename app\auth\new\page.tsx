"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { createClient } from '@/utils/supabase/client'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Loader2, ArrowLeft } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function AuthPage() {
  const [activeTab, setActiveTab] = useState("login")
  const router = useRouter()

  // Login state
  const [loginMobile, setLoginMobile] = useState("")
  const [loginPassword, setLoginPassword] = useState("")
  const [loginLoading, setLoginLoading] = useState(false)
  const [loginError, setLoginError] = useState("")
  const [loginSuccess, setLoginSuccess] = useState("")

  // Register state
  const [registerMobile, setRegisterMobile] = useState("")
  const [registerPassword, setRegisterPassword] = useState("")
  const [registerConfirmPassword, setRegisterConfirmPassword] = useState("")
  const [fullName, setFullName] = useState("")
  const [shopName, setShopName] = useState("")
  const [shopAddress, setShopAddress] = useState("")
  const [registerLoading, setRegisterLoading] = useState(false)
  const [registerError, setRegisterError] = useState("")
  const [registerSuccess, setRegisterSuccess] = useState("")
  const [createShop, setCreateShop] = useState(true)

  // Validate mobile number format
  const validateMobile = (mobile: string) => {
    const cleaned = mobile.replace(/\D/g, "")
    return cleaned.length >= 10 && cleaned.length <= 12
  }

  // Format mobile number for consistency
  const formatMobile = (mobile: string) => {
    let cleaned = mobile.replace(/\D/g, "")

    // Ensure it starts with country code
    if (cleaned.startsWith("0")) {
      cleaned = "27" + cleaned.substring(1)
    } else if (!cleaned.startsWith("27")) {
      cleaned = "27" + cleaned
    }

    return cleaned
  }

  // Generate a valid email from mobile number
  const generateEmail = (mobile: string) => {
    const cleanedMobile = mobile.replace(/\D/g, "")
    return `mobile${cleanedMobile}@gmail.com`
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!loginMobile || !loginPassword) {
      setLoginError("Please enter both mobile number and password")
      return
    }

    if (!validateMobile(loginMobile)) {
      setLoginError("Please enter a valid mobile number")
      return
    }

    setLoginLoading(true)
    setLoginError("")
    setLoginSuccess("")

    try {
      const formattedMobile = formatMobile(loginMobile)
      const email = generateEmail(formattedMobile)
      const supabase = createClient()

      // Attempt to sign in
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password: loginPassword
      })

      if (error || !data.session) {
        setLoginError(error?.message || 'Login failed')
        setLoginLoading(false)
        return
      }

      setLoginSuccess('Login successful! Redirecting...')

      // Store login state in localStorage for better persistence
      localStorage.setItem('userLoggedIn', 'true')
      localStorage.setItem('currentUserId', data.user.id)

      // Check if user has a selected shop
      const { data: shopData } = await supabase
        .from('user_shops')
        .select('shop_id')
        .eq('user_id', data.user.id)
        .single()

      setTimeout(() => {
        setLoginLoading(false)
        // If user has a shop, go to products, otherwise go to shop selection
        if (shopData?.shop_id) {
          localStorage.setItem('currentShopId', shopData.shop_id)
          router.push('/products')
        } else {
          router.push('/shops')
        }
      }, 1000)
    } catch (err) {
      console.error("Login error:", err)
      setLoginError("An unexpected error occurred. Please try again later.")
      setLoginLoading(false)
    }
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!registerMobile || !registerPassword) {
      setRegisterError("Please enter both mobile number and password")
      return
    }

    if (!validateMobile(registerMobile)) {
      setRegisterError("Please enter a valid mobile number")
      return
    }

    if (registerPassword.length < 6) {
      setRegisterError("Password must be at least 6 characters long")
      return
    }

    if (registerPassword !== registerConfirmPassword) {
      setRegisterError("Passwords do not match")
      return
    }

    if (createShop && !shopName) {
      setRegisterError("Please enter a shop name")
      return
    }

    setRegisterLoading(true)
    setRegisterError("")
    setRegisterSuccess("")

    try {
      const formattedMobile = formatMobile(registerMobile)
      const email = generateEmail(formattedMobile)
      const supabase = createClient()

      // Sign up the user
      const { data, error } = await supabase.auth.signUp({
        email,
        password: registerPassword,
        options: {
          data: {
            mobile: formattedMobile,
            full_name: fullName
          }
        }
      })

      if (error || !data.user) {
        setRegisterError(error?.message || 'Registration failed')
        setRegisterLoading(false)
        return
      }

      // Create user profile
      const { error: profileError } = await supabase
        .from('users')
        .insert({
          id: data.user.id,
          mobile: formattedMobile,
          full_name: fullName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

      // Create a default shop for the user
      if (!profileError) {
        const { data: shopData, error: shopError } = await supabase
          .from('shops')
          .insert({
            owner_user_id: data.user.id,
            name: `${fullName}'s Shop`,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()

        if (!shopError && shopData) {
          // Assign owner role to the user for this shop
          const { data: roleData } = await supabase
            .from('roles')
            .select('id')
            .eq('name', 'Owner')
            .single()

          if (roleData) {
            await supabase
              .from('user_roles')
              .insert({
                user_id: data.user.id,
                shop_id: shopData.id,
                role_id: roleData.id,
                created_at: new Date().toISOString()
              })
          }
        }
      }

      if (profileError) {
        console.error("Error creating user profile:", profileError)
        // Continue anyway, as the auth user was created
      }

      // If creating a shop, create it now
      if (createShop && shopName) {
        const { error: shopError } = await supabase
          .from('shops')
          .insert({
            owner_user_id: data.user.id,
            name: shopName,
            address: shopAddress,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single()

        if (shopError) {
          console.error("Error creating shop:", shopError)
          setRegisterError("Registration successful, but there was an error creating your shop. Please try again later.")
          setRegisterLoading(false)
          return
        }

        // Get the shop ID
        const { data: shopData, error: getShopError } = await supabase
          .from('shops')
          .select('id')
          .eq('owner_user_id', data.user.id)
          .single()

        if (!getShopError && shopData) {
          // Add user as owner of the shop
          const { error: roleError } = await supabase
            .from('user_roles')
            .insert({
              user_id: data.user.id,
              shop_id: shopData.id,
              role_id: 1, // Assuming 1 is the ID for the Owner role
              created_at: new Date().toISOString()
            })

          if (roleError) {
            console.error("Error assigning owner role:", roleError)
            // Continue anyway, as the shop was created
          }
        }
      }

      setRegisterSuccess('Registration successful! You can now log in.')

      // Switch to login tab after a short delay
      setTimeout(() => {
        setActiveTab("login")
        setRegisterLoading(false)
      }, 2000)
    } catch (err) {
      console.error("Registration error:", err)
      setRegisterError("An unexpected error occurred. Please try again later.")
      setRegisterLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
      <div className="w-full max-w-md">
        <div className="mb-6 flex justify-center">
          <Link href="/" className="flex items-center text-blue-600 hover:text-blue-800">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Welcome Screen
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-2xl text-center">Spaza Smart Order</CardTitle>
            <CardDescription className="text-center">
              Sign in to your account or create a new one
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="login">Login</TabsTrigger>
                <TabsTrigger value="register">Register</TabsTrigger>
              </TabsList>

              <TabsContent value="login">
                {loginError && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{loginError}</AlertDescription>
                  </Alert>
                )}

                {loginSuccess && (
                  <Alert className="mb-4 bg-green-50 border-green-200">
                    <AlertDescription className="text-green-800">{loginSuccess}</AlertDescription>
                  </Alert>
                )}

                <form onSubmit={handleLogin} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-mobile">Mobile Number</Label>
                    <Input
                      id="login-mobile"
                      type="tel"
                      value={loginMobile}
                      onChange={(e) => setLoginMobile(e.target.value)}
                      placeholder="Enter your mobile number"
                      disabled={loginLoading}
                      required
                    />
                    <p className="text-xs text-gray-500">Format: 0721234567 or 27721234567</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="login-password">Password</Label>
                    <Input
                      id="login-password"
                      type="password"
                      value={loginPassword}
                      onChange={(e) => setLoginPassword(e.target.value)}
                      placeholder="Enter your password"
                      disabled={loginLoading}
                      required
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={loginLoading}>
                    {loginLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Logging In...
                      </>
                    ) : (
                      "Login"
                    )}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="register">
                {registerError && (
                  <Alert variant="destructive" className="mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{registerError}</AlertDescription>
                  </Alert>
                )}

                {registerSuccess && (
                  <Alert className="mb-4 bg-green-50 border-green-200">
                    <AlertDescription className="text-green-800">{registerSuccess}</AlertDescription>
                  </Alert>
                )}

                <form onSubmit={handleRegister} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="register-mobile">Mobile Number</Label>
                    <Input
                      id="register-mobile"
                      type="tel"
                      value={registerMobile}
                      onChange={(e) => setRegisterMobile(e.target.value)}
                      placeholder="Enter your mobile number"
                      disabled={registerLoading}
                      required
                    />
                    <p className="text-xs text-gray-500">Format: 0721234567 or 27721234567</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="full-name">Full Name</Label>
                    <Input
                      id="full-name"
                      type="text"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      placeholder="Enter your full name"
                      disabled={registerLoading}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-password">Password</Label>
                    <Input
                      id="register-password"
                      type="password"
                      value={registerPassword}
                      onChange={(e) => setRegisterPassword(e.target.value)}
                      placeholder="Enter your password"
                      disabled={registerLoading}
                      required
                    />
                    <p className="text-xs text-gray-500">Must be at least 6 characters</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm Password</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      value={registerConfirmPassword}
                      onChange={(e) => setRegisterConfirmPassword(e.target.value)}
                      placeholder="Confirm your password"
                      disabled={registerLoading}
                      required
                    />
                  </div>

                  <div className="pt-2">
                    <div className="flex items-center mb-2">
                      <input
                        id="create-shop"
                        type="checkbox"
                        checked={createShop}
                        onChange={(e) => setCreateShop(e.target.checked)}
                        className="h-4 w-4 text-blue-600 rounded border-gray-300"
                        disabled={registerLoading}
                      />
                      <label htmlFor="create-shop" className="ml-2 text-sm font-medium text-gray-700">
                        Create a shop
                      </label>
                    </div>

                    {createShop && (
                      <div className="space-y-4 mt-4 p-4 bg-gray-50 rounded-md">
                        <div className="space-y-2">
                          <Label htmlFor="shop-name">Shop Name</Label>
                          <Input
                            id="shop-name"
                            type="text"
                            value={shopName}
                            onChange={(e) => setShopName(e.target.value)}
                            placeholder="Enter your shop name"
                            disabled={registerLoading}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="shop-address">Shop Address</Label>
                          <Textarea
                            id="shop-address"
                            value={shopAddress}
                            onChange={(e) => setShopAddress(e.target.value)}
                            placeholder="Enter your shop address"
                            disabled={registerLoading}
                            rows={3}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  <Button type="submit" className="w-full" disabled={registerLoading}>
                    {registerLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      "Register"
                    )}
                  </Button>
                </form>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
