"use client"

import { useCart } from "@/contexts/cart-context"
import {
  ShoppingCart, Search, User,
  Grid, Clock, Heart, Home, Barcode
} from "lucide-react"

interface QuickAccessMenuProps {
  onAction: (action: string) => void
  onClose: () => void
}

export function QuickAccessMenu({ onAction, onClose }: QuickAccessMenuProps) {
  const { cartCount } = useCart()

  const menuItems = [
    {
      id: "home",
      label: "Home",
      icon: <Home size={20} />,
      color: "bg-blue-600",
    },
    {
      id: "cart",
      label: "Cart",
      icon: <ShoppingCart size={20} />,
      color: "bg-green-600",
      badge: cartCount > 0 ? cartCount : undefined,
    },
    {
      id: "barcode",
      label: "Scan",
      icon: <Barcode size={20} />,
      color: "bg-indigo-600",
    },
    {
      id: "search",
      label: "Search",
      icon: <Search size={20} />,
      color: "bg-purple-600",
    },
    {
      id: "profile",
      label: "Profile",
      icon: <User size={20} />,
      color: "bg-orange-600",
    },
    {
      id: "categories",
      label: "Categories",
      icon: <Grid size={20} />,
      color: "bg-teal-600",
    },
    {
      id: "recent",
      label: "Recent",
      icon: <Clock size={20} />,
      color: "bg-pink-600",
    },
    {
      id: "favorites",
      label: "Favorites",
      icon: <Heart size={20} />,
      color: "bg-red-600",
    },
  ]

  return (
    <div className="fixed bottom-20 right-4 z-50 flex flex-col-reverse gap-3 items-end">
      {menuItems.map((item, index) => (
        <button
          key={item.id}
          onClick={() => onAction(item.id)}
          className={`${item.color} text-white rounded-full flex items-center gap-3 px-4 py-3 shadow-md animate-in slide-in-from-right-5 duration-200 touch-target`}
          style={{ animationDelay: `${index * 50}ms` }}
        >
          <span className="font-medium">{item.label}</span>
          <span className="rounded-full bg-white/20 p-2 flex items-center justify-center min-w-[44px] min-h-[44px] relative">
            {item.icon}
            {item.badge && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {item.badge > 99 ? '99+' : item.badge}
              </span>
            )}
          </span>
        </button>
      ))}
    </div>
  )
}
