"use client"

import { memo, useMemo } from "react"
import type { Product } from "@/lib/types"
import { ProductCardImproved } from "./product-card-improved"

interface ProductListProps {
  products: Product[]
  searchTerm?: string
  categoryFilter?: string
}

// Memoized individual product card
const MemoizedProductCard = memo(ProductCardImproved)

export function OptimizedProductList({ 
  products, 
  searchTerm = "", 
  categoryFilter = "" 
}: ProductListProps) {
  // Memoize filtered products to prevent unnecessary recalculations
  const filteredProducts = useMemo(() => {
    if (!searchTerm && !categoryFilter) return products
    
    return products.filter(product => {
      const matchesSearch = !searchTerm || 
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = !categoryFilter || product.category_id === categoryFilter
      
      return matchesSearch && matchesCategory
    })
  }, [products, searchTerm, categoryFilter])
  
  // Early return for empty state
  if (!filteredProducts.length) {
    return (
      <div className="text-center py-4">
        <p className="text-gray-500">No products available</p>
        {(searchTerm || categoryFilter) && (
          <p className="text-sm text-gray-400 mt-1">
            Try adjusting your filters
          </p>
        )}
      </div>
    )
  }
  
  return (
    <div className="space-y-3">
      {filteredProducts.map((product) => (
        <MemoizedProductCard 
          key={product.id} 
          product={product} 
        />
      ))}
    </div>
  )
}

// Export a memoized version of the component
export const MemoizedProductList = memo(OptimizedProductList)
