"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Download, Printer, RefreshCw, Search } from "lucide-react"
import { AdminHeader } from "@/components/admin/admin-header"
import { getStockSheetData } from "@/lib/admin-service"

export default function StockSheetPage() {
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [filterCategory, setFilterCategory] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<"name" | "category" | "stock">("name")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")

  // Load stock data
  useEffect(() => {
    loadStockData()
  }, [])

  const loadStockData = async () => {
    setLoading(true)
    setError("")
    
    try {
      const data = await getStockSheetData()
      setProducts(data)
    } catch (err) {
      console.error("Error loading stock data:", err)
      setError("Failed to load stock data. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Get unique categories from products
  const categories = [...new Set(products.map(p => p.category?.name || "Uncategorized"))]

  // Filter and sort products
  const filteredProducts = products
    .filter(product => {
      // Apply search filter
      const matchesSearch = searchQuery === "" || 
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.barcode && product.barcode.includes(searchQuery))
      
      // Apply category filter
      const matchesCategory = !filterCategory || 
        (product.category?.name || "Uncategorized") === filterCategory
      
      return matchesSearch && matchesCategory
    })
    .sort((a, b) => {
      // Apply sorting
      if (sortBy === "name") {
        return sortDirection === "asc" 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name)
      } else if (sortBy === "category") {
        const catA = a.category?.name || "Uncategorized"
        const catB = b.category?.name || "Uncategorized"
        return sortDirection === "asc"
          ? catA.localeCompare(catB)
          : catB.localeCompare(catA)
      } else if (sortBy === "stock") {
        const stockA = a.stock_quantity || 0
        const stockB = b.stock_quantity || 0
        return sortDirection === "asc"
          ? stockA - stockB
          : stockB - stockA
      }
      return 0
    })

  // Handle CSV export
  const exportToCSV = () => {
    // Create CSV content
    const headers = ["Product Name", "Category", "Barcode", "Stock Quantity", "Price", "Last Updated"]
    
    const csvContent = [
      headers.join(","),
      ...filteredProducts.map(product => [
        `"${product.name.replace(/"/g, '""')}"`,
        `"${(product.category?.name || "Uncategorized").replace(/"/g, '""')}"`,
        product.barcode || "",
        product.stock_quantity || "0",
        product.price || "0",
        new Date(product.last_stock_update || Date.now()).toLocaleDateString()
      ].join(","))
    ].join("\n")
    
    // Create download link
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.setAttribute("href", url)
    link.setAttribute("download", `stock-sheet-${new Date().toISOString().split("T")[0]}.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Handle print
  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <AdminHeader title="Stock Sheet" toggleSidebar={function (): void {
        throw new Error("Function not implemented.")
      } } />
      
      <div className="p-4 flex-grow">
        <div className="mb-4 flex items-center">
          <Link href="/admin/products" className="mr-2">
            <ArrowLeft className="h-6 w-6" />
          </Link>
          <h1 className="text-2xl font-bold">Stock Sheet</h1>
        </div>

        {/* Controls - hidden when printing */}
        <div className="mb-4 flex flex-wrap gap-2 print:hidden">
          <button 
            onClick={loadStockData}
            className="bg-gray-200 text-gray-800 px-3 py-2 rounded-md flex items-center gap-1 hover:bg-gray-300"
          >
            <RefreshCw size={16} />
            Refresh
          </button>
          
          <button 
            onClick={exportToCSV}
            className="bg-green-600 text-white px-3 py-2 rounded-md flex items-center gap-1 hover:bg-green-700"
          >
            <Download size={16} />
            Export CSV
          </button>
          
          <button 
            onClick={handlePrint}
            className="bg-blue-600 text-white px-3 py-2 rounded-md flex items-center gap-1 hover:bg-blue-700"
          >
            <Printer size={16} />
            Print
          </button>
        </div>

        {/* Filters - hidden when printing */}
        <div className="mb-4 bg-white p-4 rounded-md shadow-sm print:hidden">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by name or barcode"
                  className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md"
                />
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                value={filterCategory || ""}
                onChange={(e) => setFilterCategory(e.target.value || null)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sort By
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="name">Name</option>
                <option value="category">Category</option>
                <option value="stock">Stock Quantity</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Order
              </label>
              <select
                value={sortDirection}
                onChange={(e) => setSortDirection(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
            </div>
          </div>
        </div>

        {/* Print header - only visible when printing */}
        <div className="hidden print:block mb-4">
          <h2 className="text-xl font-bold">Stock Sheet</h2>
          <p className="text-gray-600">Generated on {new Date().toLocaleDateString()}</p>
        </div>

        {/* Error message */}
        {error && (
          <div className="bg-red-100 text-red-700 p-3 rounded-md mb-4 print:hidden">
            {error}
          </div>
        )}

        {/* Loading state */}
        {loading ? (
          <div className="flex justify-center py-8 print:hidden">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {/* Stock table */}
            {filteredProducts.length === 0 ? (
              <div className="bg-white rounded-md p-8 text-center">
                <p className="text-gray-500">No products found.</p>
              </div>
            ) : (
              <div className="bg-white rounded-md shadow-sm overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Category
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Barcode
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Stock
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider print:hidden">
                          Last Updated
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredProducts.map((product) => (
                        <tr key={product.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{product.category?.name || "Uncategorized"}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500 font-mono">{product.barcode || "-"}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className={`text-sm font-medium ${
                              (product.stock_quantity || 0) === 0 
                                ? "text-red-600" 
                                : (product.stock_quantity || 0) < 5 
                                  ? "text-yellow-600" 
                                  : "text-green-600"
                            }`}>
                              {product.stock_quantity || 0}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">R {product.price?.toFixed(2) || "0.00"}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap print:hidden">
                            <div className="text-sm text-gray-500">
                              {product.last_stock_update 
                                ? new Date(product.last_stock_update).toLocaleDateString() 
                                : "-"}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}