"use client"

import { useEffect } from 'react'
import { createClient } from '@/utils/supabase/client'

export function AuthStateCleaner() {
  useEffect(() => {
    // Only run if localStorage indicates logged in
    // This avoids unnecessary Supabase calls for logged-out users
    if (localStorage.getItem('userLoggedIn') === 'true') {
      // Use a timeout to delay the check slightly
      // This prevents it from competing with more important resources during page load
      setTimeout(() => {
        const cleanupAuthState = async () => {
          try {
            // Check if user is actually logged in with Supabase
            const supabase = createClient()
            const { data: { user }, error } = await supabase.auth.getUser()

            // If there's no user but localStorage says logged in, clear it
            if (!user || error) {
              console.log('AuthStateCleaner: Detected stale login state, clearing...')
              localStorage.removeItem('userLoggedIn')

              // If we're on a protected page, redirect to login
              const protectedPaths = ["/products", "/cart", "/orders", "/profile", "/checkout"]
              const currentPath = window.location.pathname
              const isProtectedPath = protectedPaths.some(path => currentPath.startsWith(path))

              if (isProtectedPath) {
                window.location.href = '/login'
              }
            }
          } catch (err) {
            console.error('AuthStateCleaner: Error checking auth state:', err)
            // On error, better to clear the state to avoid login issues
            localStorage.removeItem('userLoggedIn')
          }
        }

        // Run the cleanup but don't block page rendering
        cleanupAuthState()
      }, 500) // Delay by 500ms to prioritize UI rendering
    }
  }, [])

  return null
}
