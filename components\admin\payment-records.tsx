"use client"

import { useState, useEffect } from "react"
import { getPaymentStats } from "@/lib/payment-service"

export default function PaymentRecords() {
  const [stats, setStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    async function loadStats() {
      setLoading(true)
      const data = await getPaymentStats()
      setStats(data)
      setLoading(false)
    }
    
    loadStats()
  }, [])
  
  if (loading) {
    return <div className="p-4">Loading payment statistics...</div>
  }
  
  if (!stats) {
    return <div className="p-4">Failed to load payment statistics</div>
  }
  
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Payment Statistics</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="bg-white p-4 rounded shadow">
          <h3 className="font-semibold mb-2">Payment Status</h3>
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Method</th>
                <th className="text-left py-2">Status</th>
                <th className="text-right py-2">Count</th>
              </tr>
            </thead>
            <tbody>
              {stats.counts?.map((item: any, index: number) => (
                <tr key={index} className="border-b">
                  <td className="py-2">{item.payment_method}</td>
                  <td className="py-2">{item.status}</td>
                  <td className="text-right py-2">{item.count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        <div className="bg-white p-4 rounded shadow">
          <h3 className="font-semibold mb-2">Revenue by Payment Method</h3>
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">Method</th>
                <th className="text-right py-2">Total Amount</th>
              </tr>
            </thead>
            <tbody>
              {stats.totals?.map((item: any, index: number) => (
                <tr key={index} className="border-b">
                  <td className="py-2">{item.payment_method}</td>
                  <td className="text-right py-2">
                    {new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: 'ZAR'
                    }).format(item.total_amount)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}