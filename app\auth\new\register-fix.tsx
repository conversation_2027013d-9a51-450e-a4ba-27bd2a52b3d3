"use client"

import { useState } from "react"
import { createClient } from '@/utils/supabase/client'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function RegisterFix() {
  const [mobile, setMobile] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [fullName, setFullName] = useState("")
  const [shopName, setShopName] = useState("")
  const [shopAddress, setShopAddress] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [createShop, setCreateShop] = useState(true)
  const [debugInfo, setDebugInfo] = useState<any>(null)

  // Validate mobile number format
  const validateMobile = (mobile: string) => {
    const cleaned = mobile.replace(/\D/g, "")
    return cleaned.length >= 10 && cleaned.length <= 12
  }

  // Format mobile number for consistency
  const formatMobile = (mobile: string) => {
    let cleaned = mobile.replace(/\D/g, "")

    // Ensure it starts with country code
    if (cleaned.startsWith("0")) {
      cleaned = "27" + cleaned.substring(1)
    } else if (!cleaned.startsWith("27")) {
      cleaned = "27" + cleaned
    }

    return cleaned
  }

  // Generate a valid email from mobile number
  const generateEmail = (mobile: string) => {
    const cleanedMobile = mobile.replace(/\D/g, "")
    return `mobile${cleanedMobile}@gmail.com`
  }

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!mobile || !password) {
      setError("Please enter both mobile number and password")
      return
    }

    if (!validateMobile(mobile)) {
      setError("Please enter a valid mobile number")
      return
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters long")
      return
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return
    }

    if (createShop && !shopName) {
      setError("Please enter a shop name")
      return
    }

    setLoading(true)
    setError("")
    setSuccess("")
    setDebugInfo(null)

    try {
      const formattedMobile = formatMobile(mobile)
      const email = generateEmail(formattedMobile)
      const supabase = createClient()
      
      // Step 1: Sign up the user with auth
      console.log("Step 1: Signing up user with auth")
      const { data: authData, error: authError } = await supabase.auth.signUp({ 
        email, 
        password, 
        options: { 
          data: { 
            mobile: formattedMobile,
            full_name: fullName
          } 
        } 
      })

      if (authError || !authData.user) {
        console.error("Auth signup error:", authError)
        setError(authError?.message || 'Registration failed')
        setLoading(false)
        return
      }

      // Log the user data for debugging
      console.log("Auth user created:", authData.user.id)
      
      // Step 2: Create user profile with service role client
      console.log("Step 2: Creating user profile")
      
      // Use the auth user's ID to create the profile
      const userId = authData.user.id
      
      // Create user profile directly with a fetch request to bypass RLS
      const profileResponse = await fetch('/api/auth/create-user-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          mobile: formattedMobile,
          fullName,
        }),
      })
      
      const profileResult = await profileResponse.json()
      
      if (!profileResult.success) {
        console.error("Error creating user profile:", profileResult.error)
        setDebugInfo({
          authUser: authData.user,
          profileError: profileResult.error
        })
      } else {
        console.log("User profile created successfully")
      }

      // Step 3: If creating a shop, create it now
      if (createShop && shopName) {
        console.log("Step 3: Creating shop")
        
        // Create shop with a fetch request to bypass RLS
        const shopResponse = await fetch('/api/auth/create-shop', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            shopName,
            shopAddress,
          }),
        })
        
        const shopResult = await shopResponse.json()
        
        if (!shopResult.success) {
          console.error("Error creating shop:", shopResult.error)
          setDebugInfo(prev => ({
            ...prev,
            shopError: shopResult.error
          }))
        } else {
          console.log("Shop created successfully:", shopResult.shopId)
          
          // Step 4: Assign user as owner of the shop
          console.log("Step 4: Assigning user as owner")
          
          const roleResponse = await fetch('/api/auth/assign-role', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId,
              shopId: shopResult.shopId,
              roleId: 1, // Owner role
            }),
          })
          
          const roleResult = await roleResponse.json()
          
          if (!roleResult.success) {
            console.error("Error assigning owner role:", roleResult.error)
            setDebugInfo(prev => ({
              ...prev,
              roleError: roleResult.error
            }))
          } else {
            console.log("User assigned as owner successfully")
          }
        }
      }

      setSuccess('Registration successful! You can now log in.')
      
      // Switch to login tab after a short delay
      setTimeout(() => {
        setLoading(false)
        window.location.href = '/auth/new?tab=login'
      }, 2000)
    } catch (err) {
      console.error("Registration error:", err)
      setError("An unexpected error occurred. Please try again later.")
      setLoading(false)
    }
  }

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-6 text-center">Register New Account</h1>
      
      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {success && (
        <Alert className="mb-4 bg-green-50 border-green-200">
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}
      
      <form onSubmit={handleRegister} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="register-mobile">Mobile Number</Label>
          <Input
            id="register-mobile"
            type="tel"
            value={mobile}
            onChange={(e) => setMobile(e.target.value)}
            placeholder="Enter your mobile number"
            disabled={loading}
            required
          />
          <p className="text-xs text-gray-500">Format: ********** or ***********</p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="full-name">Full Name</Label>
          <Input
            id="full-name"
            type="text"
            value={fullName}
            onChange={(e) => setFullName(e.target.value)}
            placeholder="Enter your full name"
            disabled={loading}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="register-password">Password</Label>
          <Input
            id="register-password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="Enter your password"
            disabled={loading}
            required
          />
          <p className="text-xs text-gray-500">Must be at least 6 characters</p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="confirm-password">Confirm Password</Label>
          <Input
            id="confirm-password"
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            placeholder="Confirm your password"
            disabled={loading}
            required
          />
        </div>
        
        <div className="pt-2">
          <div className="flex items-center mb-2">
            <input
              id="create-shop"
              type="checkbox"
              checked={createShop}
              onChange={(e) => setCreateShop(e.target.checked)}
              className="h-4 w-4 text-blue-600 rounded border-gray-300"
              disabled={loading}
            />
            <label htmlFor="create-shop" className="ml-2 text-sm font-medium text-gray-700">
              Create a shop
            </label>
          </div>
          
          {createShop && (
            <div className="space-y-4 mt-4 p-4 bg-gray-50 rounded-md">
              <div className="space-y-2">
                <Label htmlFor="shop-name">Shop Name</Label>
                <Input
                  id="shop-name"
                  type="text"
                  value={shopName}
                  onChange={(e) => setShopName(e.target.value)}
                  placeholder="Enter your shop name"
                  disabled={loading}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="shop-address">Shop Address</Label>
                <Textarea
                  id="shop-address"
                  value={shopAddress}
                  onChange={(e) => setShopAddress(e.target.value)}
                  placeholder="Enter your shop address"
                  disabled={loading}
                  rows={3}
                />
              </div>
            </div>
          )}
        </div>
        
        <Button type="submit" className="w-full" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Account...
            </>
          ) : (
            "Register"
          )}
        </Button>
        
        <div className="text-center mt-4">
          <a href="/auth/new" className="text-blue-600 hover:text-blue-800">
            Back to Login
          </a>
        </div>
      </form>
      
      {debugInfo && (
        <div className="mt-6 p-4 bg-gray-100 rounded-md">
          <h3 className="font-bold mb-2">Debug Information</h3>
          <pre className="text-xs overflow-auto max-h-40">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
