"use server"

import { createClient } from './supabase/client'
import { UserRoleType, UserWithShopRoles } from '@/lib/types'

/**
 * Check if a user has a specific role in a shop
 */
export async function hasRoleInShop(
  userId: string,
  shopId: string,
  requiredRoles: UserRoleType[]
): Promise<boolean> {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        role_id,
        roles:roles(name)
      `)
      .eq('user_id', userId)
      .eq('shop_id', shopId)
    
    if (error || !data || data.length === 0) {
      return false
    }
    
    // Check if user has any of the required roles
    return data.some((userRole: { role_id: any; roles: { name: any }[] }) => { 
      const roleName = userRole.roles?.[0]?.name || 'Unknown Role' as string
      return requiredRoles.includes(roleName as UserRoleType)
    })
  } catch (error) {
    console.error('Error checking user role:', error)
    return false
  }
}

/**
 * Get all shops where a user has a specific role
 */
export async function getShopsWithUserRole(
  userId: string,
  requiredRoles?: UserRoleType[]
): Promise<{ shop_id: string; shop_name: string; role: UserRoleType }[]> {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        shop_id,
        role:roles(id, name),
        shop:shops(id, name)
      `)
      .eq('user_id', userId)
    
    if (error || !data || data.length === 0) {
      return []
    }
    
    const shopRoles = data
      .filter((item: { shop_id: any; role: { id: any; name: any }[]; shop: { id: any; name: any }[] }) => {
        // If no required roles specified, include all
        if (!requiredRoles || requiredRoles.length === 0) {
          return true
        }
        // Otherwise, filter by required roles
        const roleName = item.role?.[0]?.name as string
        return requiredRoles.includes(roleName as UserRoleType)
      })
      .map((item: { shop_id: any; role: { id: any; name: any }[]; shop: { id: any; name: any }[] }) => ({
        shop_id: item.shop_id,
        shop_name: item.shop?.[0]?.name || 'Unknown Shop',
        role: item.role?.[0]?.name as UserRoleType
      }))
    
    return shopRoles
  } catch (error) {
    console.error('Error getting user shops with roles:', error)
    return []
  }
}

/**
 * Get user with their roles in all shops
 */
export async function getUserWithShopRoles(userId: string): Promise<UserWithShopRoles | null> {
  try {
    const supabase = await createClient()
    
    // Get user details
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (userError || !userData) {
      return null
    }
    
    // Get user's roles in shops
    const shopRoles = await getShopsWithUserRole(userId)
    
    return {
      ...userData,
      shopRoles
    }
  } catch (error) {
    console.error('Error getting user with shop roles:', error)
    return null
  }
}

/**
 * Get all members of a shop with their roles
 */
export async function getShopMembers(shopId: string) {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        role_id,
        role:roles(id, name),
        user:users(id, full_name, mobile)
      `)
      .eq('shop_id', shopId)
    
    if (error || !data) {
      return []
    }
    
    return data.map((item: { user_id: any; role_id: any; role: { id: any; name: any }[]; user: { id: any; full_name: any; mobile: any }[] }) => ({
      user_id: item.user_id,
      role_id: item.role_id,
      role: item.role?.[0]?.name as UserRoleType,
      full_name: item.user?.[0]?.full_name,
      mobile: item.user?.[0]?.mobile
    }))
  } catch (error) {
    console.error('Error getting shop members:', error)
    return []
  }
}

/**
 * Check if user is an owner of any shop
 */
export async function isShopOwner(userId: string): Promise<boolean> {
  return hasAnyRole(userId, [UserRoleType.Owner])
}

/**
 * Check if user is an admin of any shop
 */
export async function isShopAdmin(userId: string): Promise<boolean> {
  return hasAnyRole(userId, [UserRoleType.Owner, UserRoleType.Admin])
}

/**
 * Check if user has any of the specified roles in any shop
 */
export async function hasAnyRole(userId: string, roles: UserRoleType[]): Promise<boolean> {
  try {
    const supabase = await createClient()
    
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        role:roles(name)
      `)
      .eq('user_id', userId)
    
    if (error || !data || data.length === 0) {
      return false
    }
    
    return data.some((item: { role: { name: any }[] }) => {
      const roleName = item.role?.[0]?.name as string
      return roles.includes(roleName as UserRoleType)
    })
  } catch (error) {
    console.error('Error checking if user has any role:', error)
    return false
  }
}

/**
 * Get the default shop for a user (first shop where they have a role)
 */
export async function getDefaultShopForUser(userId: string): Promise<string | null> {
  try {
    const shopRoles = await getShopsWithUserRole(userId)
    
    if (shopRoles.length === 0) {
      return null
    }
    
    // Prioritize shops where user is Owner, then Admin, then Staff
    const priorityOrder: UserRoleType[] = [UserRoleType.Owner, UserRoleType.Admin, UserRoleType.Staff]
    
    for (const roleType of priorityOrder) {
      const shopWithRole = shopRoles.find(sr => sr.role === roleType)
      if (shopWithRole) {
        return shopWithRole.shop_id
      }
    }
    
    // If no prioritized role found, return the first shop
    return shopRoles[0].shop_id
  } catch (error) {
    console.error('Error getting default shop for user:', error)
    return null
  }
}












