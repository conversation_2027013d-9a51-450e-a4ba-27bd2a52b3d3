/* Mobile touch target improvements */
@media (max-width: 640px) {
  /* Ensure all interactive elements have adequate touch targets */
  button,
  [role="button"],
  a[href],
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  input[type="checkbox"],
  input[type="radio"],
  select,
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Add padding to smaller elements to increase touch area */
  .icon-button {
    padding: 10px;
  }

  /* Increase spacing between interactive elements */
  .button-group > * {
    margin-right: 8px;
  }
  
  /* Ensure form controls are properly sized */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px; /* Prevents iOS zoom on focus */
    padding: 12px;
    height: 44px;
  }

  /* Ensure proper spacing in lists and grids */
  .list-item,
  .grid-item {
    padding: 12px;
    margin-bottom: 8px;
  }
}
