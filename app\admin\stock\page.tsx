"use client"

import { useState, useEffect } from "react"
import { createClient } from "@/utils/supabase/client"
import { 
  Package, Search, Filter, ArrowUpDown, ChevronLeft, ChevronRight, 
  Loader2, AlertTriangle, Plus, Minus, Save, Download, Upload, FileText
} from "lucide-react"
import { format, parseISO } from "date-fns"

interface Product {
  id: string
  name: string
  stock_quantity: number
  in_stock: boolean
  last_stock_update: string | null
  category: {
    name: string
  } | null
}

interface StockHistory {
  id: string
  product_id: string
  quantity_change: number
  new_quantity: number
  action: string
  notes: string | null
  created_at: string
}

export default function StockManagementPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalProducts, setTotalProducts] = useState(0)
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [categories, setCategories] = useState<{id: string, name: string}[]>([])
  const [sortField, setSortField] = useState<string>("name")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc")
  const [stockFilter, setStockFilter] = useState<"all" | "in_stock" | "out_of_stock" | "low_stock">("all")
  const [stockUpdates, setStockUpdates] = useState<Record<string, number>>({})
  const [saving, setSaving] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<string | null>(null)
  const [stockHistory, setStockHistory] = useState<StockHistory[]>([])
  const [loadingHistory, setLoadingHistory] = useState(false)
  
  const productsPerPage = 10

  useEffect(() => {
    fetchCategories()
    fetchProducts()
  }, [currentPage, searchQuery, selectedCategory, sortField, sortDirection, stockFilter])

  const fetchCategories = async () => {
    try {
      const supabase = createClient()
      const { data, error } = await supabase
        .from("categories")
        .select("id, name")
        .order("name")
      
      if (error) throw error
      setCategories(data || [])
    } catch (error) {
      console.error("Error fetching categories:", error)
    }
  }

  const fetchProducts = async () => {
    setLoading(true)
    try {
      const supabase = createClient()
      
      // Start building the query
      let query = supabase
        .from("products")
        .select(`
          id, 
          name, 
          stock_quantity, 
          in_stock,
          last_stock_update,
          category:categories(name)
        `, { count: 'exact' })
      
      // Apply search filter if provided
      if (searchQuery) {
        query = query.ilike("name", `%${searchQuery}%`)
      }
      
      // Apply category filter if selected
      if (selectedCategory) {
        query = query.eq("category_id", selectedCategory)
      }
      
      // Apply stock filter
      if (stockFilter === "in_stock") {
        query = query.eq("in_stock", true)
      } else if (stockFilter === "out_of_stock") {
        query = query.eq("in_stock", false)
      } else if (stockFilter === "low_stock") {
        query = query.lte("stock_quantity", 10).gt("stock_quantity", 0)
      }
      
      // Apply sorting
      query = query.order(sortField, { ascending: sortDirection === "asc" })
      
      // Apply pagination
      const from = (currentPage - 1) * productsPerPage
      const to = from + productsPerPage - 1
      query = query.range(from, to)
      
      const { data, error, count } = await query
      
      if (error) throw error
      
      setProducts(data?.map(item => ({
        ...item,
        category: item.category?.[0] || null
      })) || [])
      setTotalProducts(count || 0)
      
      // Initialize stock updates with current values
      const updates: Record<string, number> = {}
      data?.forEach((product: any) => {
        updates[product.id] = product.stock_quantity
      })
      setStockUpdates(updates)
    } catch (error) {
      console.error("Error fetching products:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStockHistory = async (productId: string) => {
    setLoadingHistory(true)
    try {
      const supabase = createClient()
      
      const { data, error } = await supabase
        .from("stock_history")
        .select("*")
        .eq("product_id", productId)
        .order("created_at", { ascending: false })
        .limit(10)
      
      if (error) throw error
      
      setStockHistory(data || [])
      setSelectedProduct(productId)
      setShowHistory(true)
    } catch (error) {
      console.error("Error fetching stock history:", error)
    } finally {
      setLoadingHistory(false)
    }
  }

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  const handleStockChange = (id: string, value: number) => {
    setStockUpdates(prev => ({
      ...prev,
      [id]: Math.max(0, value) // Ensure stock is not negative
    }))
  }

  const handleIncrement = (id: string) => {
    setStockUpdates(prev => ({
      ...prev,
      [id]: (prev[id] || 0) + 1
    }))
  }

  const handleDecrement = (id: string) => {
    setStockUpdates(prev => ({
      ...prev,
      [id]: Math.max(0, (prev[id] || 0) - 1) // Ensure stock is not negative
    }))
  }

  const saveStockUpdates = async () => {
    setSaving(true)
    try {
      const supabase = createClient()
      
      // Find products with changed stock levels
      const updates = products.filter(product => 
        stockUpdates[product.id] !== product.stock_quantity
      )
      
      if (updates.length === 0) {
        alert("No stock changes to save")
        setSaving(false)
        return
      }
      
      // Process each update
      for (const product of updates) {
        const newQuantity = stockUpdates[product.id]
        const quantityChange = newQuantity - product.stock_quantity
        
        // Update product stock
        const { error: updateError } = await supabase
          .from("products")
          .update({
            stock_quantity: newQuantity,
            in_stock: newQuantity > 0,
            last_stock_update: new Date().toISOString()
          })
          .eq("id", product.id)
        
        if (updateError) throw updateError
        
        // Record in stock history
        const { error: historyError } = await supabase
          .from("stock_history")
          .insert([{
            product_id: product.id,
            quantity_change: quantityChange,
            new_quantity: newQuantity,
            action: quantityChange > 0 ? "Stock Added" : "Stock Removed",
            notes: `Manual stock update: ${quantityChange > 0 ? "Added" : "Removed"} ${Math.abs(quantityChange)} units`
          }])
        
        if (historyError) throw historyError
      }
      
      // Refresh products list
      fetchProducts()
      
      alert(`Successfully updated stock for ${updates.length} products`)
    } catch (error) {
      console.error("Error updating stock:", error)
      alert("Failed to update stock. Please try again.")
    } finally {
      setSaving(false)
    }
  }

  const exportStockSheet = () => {
    // Create CSV content
    let csvContent = "Product Name,Category,Current Stock,Status\n"
    
    products.forEach(product => {
      const status = product.in_stock ? "In Stock" : "Out of Stock"
      const category = product.category?.name || "Uncategorized"
      csvContent += `"${product.name}","${category}",${product.stock_quantity},"${status}"\n`
    })
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `stock-sheet-${format(new Date(), 'yyyy-MM-dd')}.csv`)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never"
    
    try {
      return format(parseISO(dateString), "MMM d, yyyy h:mm a")
    } catch (error) {
      return dateString
    }
  }

  const totalPages = Math.ceil(totalProducts / productsPerPage)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">Stock Management</h1>
        <div className="flex gap-2">
          <button
            onClick={exportStockSheet}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center gap-2"
          >
            <Download size={16} />
            <span>Export Stock Sheet</span>
          </button>
          <button
            onClick={saveStockUpdates}
            disabled={saving}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? <Loader2 size={16} className="animate-spin" /> : <Save size={16} />}
            <span>Save Changes</span>
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search products..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
          </div>
          
          {/* Category Filter */}
          <div>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={selectedCategory || ""}
              onChange={(e) => setSelectedCategory(e.target.value || null)}
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Stock Filter */}
          <div>
            <select
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={stockFilter}
              onChange={(e) => setStockFilter(e.target.value as any)}
            >
              <option value="all">All Stock</option>
              <option value="in_stock">In Stock</option>
              <option value="out_of_stock">Out of Stock</option>
              <option value="low_stock">Low Stock</option>
            </select>
          </div>
          
          {/* Reset Filters */}
          <div>
            <button
              onClick={() => {
                setSearchQuery("")
                setSelectedCategory(null)
                setStockFilter("all")
                setSortField("name")
                setSortDirection("asc")
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-100"
            >
              Reset Filters
            </button>
          </div>
        </div>
      </div>

      {/* Stock History Modal */}
      {showHistory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-3xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-lg font-semibold">Stock History</h2>
              <button 
                onClick={() => setShowHistory(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                &times;
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-[calc(80vh-8rem)]">
              {loadingHistory ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                </div>
              ) : stockHistory.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No stock history found for this product</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {stockHistory.map((entry) => (
                    <div key={entry.id} className="border border-gray-200 rounded-md p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                            entry.quantity_change > 0 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {entry.action}
                          </span>
                          <p className="mt-2 text-sm font-medium">
                            {entry.quantity_change > 0 ? 'Added' : 'Removed'} {Math.abs(entry.quantity_change)} units
                          </p>
                          {entry.notes && (
                            <p className="text-sm text-gray-500 mt-1">{entry.notes}</p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">{formatDate(entry.created_at)}</p>
                          <p className="text-sm font-medium mt-1">New quantity: {entry.new_quantity}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            <div className="p-4 border-t border-gray-200 flex justify-end">
              <button
                onClick={() => setShowHistory(false)}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center gap-1">
                    Product Name
                    {sortField === "name" && (
                      <ArrowUpDown size={14} className={sortDirection === "asc" ? "transform rotate-180" : ""} />
                    )}
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("stock_quantity")}
                >
                  <div className="flex items-center gap-1">
                    Current Stock
                    {sortField === "stock_quantity" && (
                      <ArrowUpDown size={14} className={sortDirection === "asc" ? "transform rotate-180" : ""} />
                    )}
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Update Stock
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th 
                  scope="col" 
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("last_stock_update")}
                >
                  <div className="flex items-center gap-1">
                    Last Updated
                    {sortField === "last_stock_update" && (
                      <ArrowUpDown size={14} className={sortDirection === "asc" ? "transform rotate-180" : ""} />
                    )}
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                    </div>
                  </td>
                </tr>
              ) : products.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    No products found
                  </td>
                </tr>
              ) : (
                products.map((product) => (
                  <tr key={product.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-md flex items-center justify-center">
                          <Package className="h-5 w-5 text-gray-500" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{product.category?.name || "Uncategorized"}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`text-sm ${product.stock_quantity <= 10 && product.stock_quantity > 0 ? 'text-yellow-600' : product.stock_quantity === 0 ? 'text-red-600' : 'text-gray-900'}`}>
                        {product.stock_quantity} units
                        {product.stock_quantity <= 10 && product.stock_quantity > 0 && (
                          <span className="ml-2 inline-flex items-center">
                            <AlertTriangle size={14} className="text-yellow-600" />
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <button
                          onClick={() => handleDecrement(product.id)}
                          className="p-1 rounded-l-md border border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                          <Minus size={16} className="text-gray-500" />
                        </button>
                        <input
                          type="number"
                          min="0"
                          value={stockUpdates[product.id] || 0}
                          onChange={(e) => handleStockChange(product.id, parseInt(e.target.value) || 0)}
                          className="w-16 text-center border-t border-b border-gray-300 py-1"
                        />
                        <button
                          onClick={() => handleIncrement(product.id)}
                          className="p-1 rounded-r-md border border-gray-300 bg-gray-50 hover:bg-gray-100"
                        >
                          <Plus size={16} className="text-gray-500" />
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        product.in_stock ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {product.in_stock ? 'In Stock' : 'Out of Stock'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{formatDate(product.last_stock_update)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => fetchStockHistory(product.id)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View History
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
        
        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{Math.min(1 + (currentPage - 1) * productsPerPage, totalProducts)}</span> to{" "}
                  <span className="font-medium">{Math.min(currentPage * productsPerPage, totalProducts)}</span> of{" "}
                  <span className="font-medium">{totalProducts}</span> products
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                  </button>
                  
                  {/* Page numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    // Show pages around current page
                    let pageNum = currentPage;
                    if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    // Ensure page number is within valid range
                    if (pageNum <= 0 || pageNum > totalPages) return null;
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === pageNum
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" aria-hidden="true" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}


