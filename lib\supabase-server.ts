// "use server"

// import { createClient } from "@supabase/supabase-js"
// import { createServerClient } from "@supabase/ssr"
// import { cookies } from "next/headers"

// // Create a Supabase client for use in server actions and API routes
// export function createServerSupabaseClient() {
//   try {
//     const cookieStore = cookies()

//     const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
//     const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

//     if (!supabaseUrl || !supabaseAnonKey) {
//       console.error("Missing Supabase environment variables")
//       throw new Error("Missing Supabase environment variables")
//     }

//     return createServerClient(supabaseUrl, supabaseAnonKey, {
//       cookies: {
//         get(name) {
//           return cookieStore.get(name)?.value
//         },
//         set(name, value, options) {
//           cookieStore.set({ name, value, ...options })
//         },
//         remove(name, options) {
//           cookieStore.set({ name, value: "", ...options })
//         },
//       },
//     })
//   } catch (error) {
//     console.error("Error creating server Supabase client:", error)
//     throw error
//   }
// }