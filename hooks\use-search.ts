"use client"

import { useState, useEffect } from "react"
import { useDebouncedApi } from "./use-debounced-api"
import { searchProducts } from "@/lib/data-service"
import type { Product } from "@/lib/types"

/**
 * Custom hook for product search with debouncing
 * @param initialQuery Initial search query
 * @returns Object containing search state and functions
 */
export function useSearch(initialQuery: string = "") {
  const [query, setQuery] = useState(initialQuery)
  
  const {
    data: searchResults,
    loading: isSearching,
    error: searchError,
    fetch: debouncedSearch
  } = useDebouncedApi<Product[], [string]>(
    searchProducts,
    {
      initialData: [],
      debounceMs: 300,
      dependencies: []
    }
  )
  
  // Trigger search when query changes
  useEffect(() => {
    if (query.trim().length >= 2) {
      debouncedSearch(query)
    }
  }, [query, debouncedSearch])
  
  return {
    query,
    setQuery,
    searchResults: searchResults || [],
    isSearching,
    searchError,
    hasResults: (searchResults || []).length > 0,
    hasQuery: query.trim().length >= 2
  }
}
