import { createClient } from "./supabase/client"

/**
 * Performs a complete logout by:
 * 1. Signing out from Supabase client
 * 2. Calling the server-side logout API
 * 3. Clearing local storage
 * 4. Redirecting to home page
 */
export async function performLogout() {
  try {
    // First, try client-side logout
    const supabase = createClient()
    await supabase.auth.signOut()
    
    // Then call the server-side logout API
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error('Server-side logout failed')
    }
    
    // Clear any local storage items that might be related to auth
    if (typeof window !== 'undefined') {
      localStorage.removeItem('supabase-auth')
    }
    
    return { success: true }
  } catch (error) {
    console.error('Logout error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error during logout'
    }
  }
}