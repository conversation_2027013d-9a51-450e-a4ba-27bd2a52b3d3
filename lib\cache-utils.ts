/**
 * Simple in-memory cache with TTL and invalidation
 */
export class MemoryCache<T> {
  private cache: Map<string, { data: T; timestamp: number }> = new Map()
  private ttl: number // Time to live in milliseconds
  
  constructor(ttlSeconds: number = 60) {
    this.ttl = ttlSeconds * 1000
  }
  
  /**
   * Get an item from the cache
   * @param key Cache key
   * @returns The cached item or undefined if not found or expired
   */
  get(key: string): T | undefined {
    const item = this.cache.get(key)
    
    if (!item) return undefined
    
    const now = Date.now()
    if (now - item.timestamp > this.ttl) {
      // Item has expired
      this.cache.delete(key)
      return undefined
    }
    
    return item.data
  }
  
  /**
   * Set an item in the cache
   * @param key Cache key
   * @param data Data to cache
   */
  set(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  /**
   * Remove an item from the cache
   * @param key Cache key
   */
  delete(key: string): void {
    this.cache.delete(key)
  }
  
  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear()
  }
  
  /**
   * Invalidate cache entries by prefix
   * @param prefix Key prefix to match
   */
  invalidateByPrefix(prefix: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key)
      }
    }
  }
  
  /**
   * Get or set cache item with a factory function
   * @param key Cache key
   * @param factory Function to create the item if not in cache
   * @returns The cached or newly created item
   */
  async getOrSet(key: string, factory: () => Promise<T>): Promise<T> {
    const cachedItem = this.get(key)
    if (cachedItem !== undefined) {
      return cachedItem
    }
    
    const newItem = await factory()
    this.set(key, newItem)
    return newItem
  }
}

// Create cache instances for different data types
export const productCache = new MemoryCache<any>(300) // 5 minutes
export const categoryCache = new MemoryCache<any>(600) // 10 minutes
export const cartCache = new MemoryCache<any>(10) // 10 seconds
export const orderCache = new MemoryCache<any>(60) // 1 minute
