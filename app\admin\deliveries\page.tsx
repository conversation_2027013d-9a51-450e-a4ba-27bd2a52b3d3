import { getDeliveryAssignments, getDeliveryPartners } from "@/lib/delivery-service"
import { getOrdersByStatus } from "@/lib/order-service"
import { DeliveryManagement } from "@/components/admin/delivery-management"

export default async function AdminDeliveriesPage() {
  const pendingOrders = await getOrdersByStatus("Processing")
  const deliveryPartners = await getDeliveryPartners()
  const activeDeliveries = await getDeliveryAssignments()

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Delivery Management</h1>
      <DeliveryManagement
        pendingOrders={pendingOrders}
        deliveryPartners={deliveryPartners}
        activeDeliveries={activeDeliveries}
      />
    </div>
  )
}
