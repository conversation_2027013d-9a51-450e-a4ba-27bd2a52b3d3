"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/contexts/auth-context"
import { useShop } from "@/contexts/shop-context"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { ShopSelector } from "@/components/shop-selector"

export default function TestAuthNewPage() {
  const { user, userWithRoles, loading, isAuthenticated, signOut } = useAuth()
  const { currentShop, userShops, loading: shopLoading } = useShop()
  const [authState, setAuthState] = useState<string>("Checking authentication...")

  useEffect(() => {
    if (!loading) {
      if (isAuthenticated) {
        setAuthState(`Authenticated as: ${user?.mobile || 'Unknown'} (${user?.id})`)
      } else {
        setAuthState("Not authenticated")
      }
    }
  }, [user, loading, isAuthenticated])

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Test Page (New Schema)</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Authentication Status</CardTitle>
          <CardDescription>Current authentication state</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-gray-100 rounded-md">
            <p className="font-mono">{loading ? "Loading..." : authState}</p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          {isAuthenticated ? (
            <Button variant="destructive" onClick={signOut}>Sign Out</Button>
          ) : (
            <Link href="/auth/new" passHref>
              <Button>Sign In</Button>
            </Link>
          )}
        </CardFooter>
      </Card>
      
      {isAuthenticated && (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>User Details</CardTitle>
              <CardDescription>Information about the authenticated user</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <span className="font-semibold">User ID:</span> {user?.id}
                </div>
                <div>
                  <span className="font-semibold">Mobile:</span> {user?.mobile || 'Not set'}
                </div>
                <div>
                  <span className="font-semibold">Full Name:</span> {user?.full_name || 'Not set'}
                </div>
                <div>
                  <span className="font-semibold">Created At:</span> {user?.created_at ? new Date(user.created_at).toLocaleString() : 'Unknown'}
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>Shop Information</span>
                <ShopSelector />
              </CardTitle>
              <CardDescription>Information about user's shops</CardDescription>
            </CardHeader>
            <CardContent>
              {shopLoading ? (
                <p>Loading shops...</p>
              ) : (
                <div className="space-y-4">
                  <div>
                    <span className="font-semibold">Number of Shops:</span> {userShops.length}
                  </div>
                  
                  {currentShop && (
                    <div className="p-4 bg-blue-50 rounded-md">
                      <h3 className="font-semibold mb-2">Current Shop</h3>
                      <div className="space-y-1">
                        <div>
                          <span className="font-semibold">Shop ID:</span> {currentShop.id}
                        </div>
                        <div>
                          <span className="font-semibold">Name:</span> {currentShop.name}
                        </div>
                        <div>
                          <span className="font-semibold">Address:</span> {currentShop.address || 'Not set'}
                        </div>
                        <div>
                          <span className="font-semibold">Owner ID:</span> {currentShop.owner_user_id}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {userShops.length > 0 && (
                    <div>
                      <h3 className="font-semibold mb-2">All Shops</h3>
                      <div className="space-y-2">
                        {userShops.map(shop => (
                          <div key={shop.id} className="p-3 bg-gray-100 rounded-md">
                            <div className="font-medium">{shop.name}</div>
                            <div className="text-sm text-gray-600">{shop.address || 'No address'}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>User Roles</CardTitle>
              <CardDescription>Roles assigned to the user in different shops</CardDescription>
            </CardHeader>
            <CardContent>
              {userWithRoles?.roles && userWithRoles.roles.length > 0 ? (
                <div className="space-y-2">
                  {userWithRoles.roles.map((role, index) => (
                    <div key={index} className="p-3 bg-gray-100 rounded-md">
                      <div className="font-medium">
                        Shop: {role.shop?.name || role.shop_id}
                      </div>
                      <div className="text-sm">
                        Role: <span className="font-semibold">{role.role}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p>No roles assigned</p>
              )}
            </CardContent>
          </Card>
        </>
      )}
      
      <div className="flex gap-4 mt-6">
        <Link href="/" passHref>
          <Button variant="outline">Home</Button>
        </Link>
        <Link href="/products" passHref>
          <Button variant="outline">Products</Button>
        </Link>
        <Link href="/shops" passHref>
          <Button variant="outline">Shops</Button>
        </Link>
      </div>
    </div>
  )
}
