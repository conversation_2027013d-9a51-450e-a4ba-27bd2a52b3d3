"use client"

import { createBrowserClient } from './supabase'
import { devLog, logError } from './logger'

/**
 * Utility function to check if Supabase realtime is properly configured
 * This can be used to diagnose realtime connection issues
 */
export async function checkSupabaseRealtime(): Promise<{
  success: boolean
  message: string
  details?: any
}> {
  try {
    devLog('Checking Supabase realtime configuration...')
    
    // Create a temporary client for testing
    const supabase = createBrowserClient()
    
    // Step 1: Check if we can connect to Supabase at all
    const { data: healthData, error: healthError } = await supabase.from('categories').select('count').limit(1)
    
    if (healthError) {
      return {
        success: false,
        message: 'Failed to connect to Supabase database',
        details: healthError
      }
    }
    
    // Step 2: Check if realtime is enabled by creating a test channel
    try {
      const channel = supabase.channel('realtime-test', {
        config: {
          broadcast: { self: true },
          presence: { key: '' }
        }
      })
      
      // Add system event handlers to track connection status
      let connectionStatus = 'CONNECTING'
      
      channel.on('system', { event: 'connected' }, () => {
        connectionStatus = 'CONNECTED'
      })
      
      // Subscribe to the channel
      const status = await channel.subscribe((status) => {
        devLog(`Realtime test subscription status: ${status}`)
      })
      
      // Check the subscription status
      if (status === 'SUBSCRIBED') {
        // Try to subscribe to a table
        const tableChannel = supabase.channel('table-test')
        
        tableChannel.on('postgres_changes', 
          { event: '*', schema: 'public', table: 'categories' },
          (payload) => {
            devLog('Received postgres change:', payload)
          }
        )
        
        const tableStatus = await tableChannel.subscribe()
        
        // Clean up test channels
        await tableChannel.unsubscribe()
        await channel.unsubscribe()
        
        if (tableStatus === 'SUBSCRIBED') {
          return {
            success: true,
            message: 'Supabase realtime is properly configured and working',
            details: {
              connectionStatus,
              channelStatus: status,
              tableStatus
            }
          }
        } else {
          return {
            success: false,
            message: 'Connected to realtime but failed to subscribe to database changes',
            details: {
              connectionStatus,
              channelStatus: status,
              tableStatus
            }
          }
        }
      } else {
        // Clean up test channel
        await channel.unsubscribe()
        
        return {
          success: false,
          message: 'Failed to subscribe to realtime channel',
          details: {
            connectionStatus,
            channelStatus: status
          }
        }
      }
    } catch (realtimeError) {
      return {
        success: false,
        message: 'Error testing realtime connection',
        details: realtimeError
      }
    }
  } catch (error) {
    logError('Error checking Supabase realtime:', error)
    return {
      success: false,
      message: 'Unexpected error checking realtime configuration',
      details: error
    }
  }
}
