"use client"

import { createContext, useContext, useEffect, useState, ReactNode } from "react"
import {
  isOffline,
  setupOfflineListeners,
  registerServiceWorker,
  requestNotificationPermission
} from "@/lib/service-worker"

interface OfflineContextType {
  offline: boolean
  hasOfflineSupport: boolean
  notificationsEnabled: boolean
  enableNotifications: () => Promise<boolean>
}

const OfflineContext = createContext<OfflineContextType>({
  offline: false,
  hasOfflineSupport: false,
  notificationsEnabled: false,
  enableNotifications: async () => false
})

export const useOffline = () => useContext(OfflineContext)

export function OfflineProvider({ children }: { children: ReactNode }) {
  const [offline, setOffline] = useState(false)
  const [hasOfflineSupport, setHasOfflineSupport] = useState(false)
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)

  // Initialize offline state and register service worker
  useEffect(() => {
    let isMounted = true;

    // Safely execute a function with error handling
    const safeExecute = (fn: Function, fallback: any = null) => {
      try {
        return fn();
      } catch (error) {
        console.warn('Error in OfflineProvider:', error);
        return fallback;
      }
    };

    // Check initial offline state
    safeExecute(() => {
      if (isMounted) {
        setOffline(isOffline());
      }
    });

    // Check if service workers are supported
    const serviceWorkersSupported = safeExecute(
      () => typeof window !== 'undefined' && 'serviceWorker' in navigator,
      false
    );

    if (isMounted) {
      setHasOfflineSupport(serviceWorkersSupported);
    }

    // Register service worker if supported - with a delay to prevent blocking initial render
    if (serviceWorkersSupported) {
      setTimeout(() => {
        safeExecute(() => {
          if (isMounted) {
            registerServiceWorker();
          }
        });
      }, 2000);

      // Check if notifications are already enabled
      safeExecute(() => {
        if (isMounted && 'Notification' in window) {
          setNotificationsEnabled(Notification.permission === 'granted');
        }
      });
    }

    // Set up online/offline listeners
    const cleanup = safeExecute(
      () => setupOfflineListeners(
        () => isMounted && setOffline(false),
        () => isMounted && setOffline(true)
      ),
      () => {}
    );

    return () => {
      isMounted = false;
      cleanup();
    };
  }, [])

  // Function to request notification permission
  const enableNotifications = async (): Promise<boolean> => {
    const granted = await requestNotificationPermission()
    setNotificationsEnabled(granted)
    return granted
  }

  return (
    <OfflineContext.Provider
      value={{
        offline,
        hasOfflineSupport,
        notificationsEnabled,
        enableNotifications
      }}
    >
      {children}
    </OfflineContext.Provider>
  )
}
