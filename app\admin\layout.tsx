"use client"

import { useState, useEffect } from "react"
import type React from "react"
import { useRouter, usePathname } from "next/navigation"
import { AdminBreadcrumb } from "@/components/admin/admin-breadcrumb"
import { AdminAuthCheck } from "@/components/admin/admin-auth-check"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header" 
import { createClient } from '@/utils/supabase/client'

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [isAdmin, setIsAdmin] = useState(false)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const supabase = createClient()
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
          // Not logged in, redirect to admin login
          if (pathname !== '/admin/login') {
            router.push('/admin/login')
          }
          setLoading(false)
          return
        }

        // Check if user has admin or owner role in any shop
        const { data: userRoles } = await supabase
          .from('user_roles')
          .select(`
            role_id,
            roles:roles(name)
          `)
          .eq('user_id', user.id)

        const isAdminUser = userRoles?.some((role: any) => {
          const roleName = role.roles?.name;
          return roleName === 'Owner' || roleName === 'Admin';
        }) || false;

        if (!isAdminUser) {
          // User is not an admin, redirect to admin login
          if (pathname !== '/admin/login') {
            router.push('/admin/login')
          }
        } else {
          setIsAdmin(true)
          // If on login page, redirect to dashboard
          if (pathname === '/admin/login') {
            router.push('/admin/dashboard')
          }
        }

        setLoading(false)
      } catch (error) {
        console.error("Error checking admin status:", error)
        setLoading(false)
        if (pathname !== '/admin/login') {
          router.push('/admin/login')
        }
      }
    }

    checkAdminStatus()
  }, [pathname, router])

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // If not admin and not on login page, don't render anything (will redirect)
  if (!isAdmin && pathname !== '/admin/login') {
    return null
  }

  // For login page, render without AdminAuthCheck
  if (pathname === '/admin/login') {
    return <div className="min-h-screen bg-gray-100">{children}</div>
  }

  return (
    <AdminAuthCheck>
      <div className="flex min-h-screen">
        {/* Sidebar - hidden on mobile by default, always visible on desktop */}
        <div className={`md:block ${sidebarOpen ? 'block' : 'hidden'} md:w-64 flex-shrink-0`}>
          <AdminSidebar />
        </div>

        {/* Overlay to close sidebar on mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main content area - takes full remaining width */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminHeader toggleSidebar={toggleSidebar} />
          <div className="flex-1 overflow-auto p-4 md:p-6 lg:p-8">
            <AdminBreadcrumb />
            <main className="mt-4">
              {children}
            </main>
          </div>
        </div>
      </div>
    </AdminAuthCheck>
  )
}
