"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { createClient } from "@/utils/supabase/client"
import { performDirectLogin } from "@/utils/direct-login"

export default function LoginPage() {
  const [mobileNumber, setMobileNumber] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [redirecting, setRedirecting] = useState(false)
  const [countdown, setCountdown] = useState(3)

  // Check if already logged in
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createClient()
        const { data } = await supabase.auth.getUser()

        if (data.user) {
          console.log("User already logged in, redirecting to products")

          // First try router navigation
          try {
            router.push("/products")
          } catch (routerErr) {
            console.error("Router navigation failed:", routerErr)

            // Fallback to window.location as a last resort
            setTimeout(() => {
              window.location.href = "/products"
            }, 500)
          }
        }
      } catch (err) {
        console.error("Auth check error:", err)
      }
    }

    checkAuth()
  }, [router])

  // Countdown timer for redirection
  useEffect(() => {
    if (redirecting && countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)

      return () => clearTimeout(timer)
    } else if (redirecting && countdown === 0) {
      try {
        router.push("/products")
      } catch (routerErr) {
        console.error("Countdown router navigation failed:", routerErr)
        // Fallback to window.location
        window.location.href = "/products"
      }
    }
  }, [redirecting, countdown, router])

  // Format mobile number for consistency
  const formatMobile = (mobile: string) => {
    let cleaned = mobile.replace(/\D/g, "")

    // Ensure it starts with country code
    if (cleaned.startsWith("0")) {
      cleaned = "27" + cleaned.substring(1)
    } else if (!cleaned.startsWith("27")) {
      cleaned = "27" + cleaned
    }

    return cleaned
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!mobileNumber || !password) {
      setError("Please enter both mobile number and password")
      return
    }

    setLoading(true)
    setError("")
    setSuccess("")

    try {
      // Use the direct login helper
      const result = await performDirectLogin(mobileNumber, password)

      if (!result.success) {
        setError(result.error || "Login failed")
        setLoading(false)
        return
      }

      // Show success and start countdown
      setSuccess("Login successful! Redirecting in " + countdown + " seconds...")
      setLoading(false)
      setRedirecting(true)

      // Also set a backup direct navigation in case the countdown doesn't work
      const backupTimeout = setTimeout(() => {
        if (document.location.pathname !== "/products") {
          console.log("Backup navigation triggered")
          try {
            router.push("/products")
          } catch (routerErr) {
            console.error("Backup router navigation failed:", routerErr)
            window.location.href = "/products"
          }
        }
      }, 5000) // 5 second backup timeout

      // Clean up the timeout if component unmounts
      return () => clearTimeout(backupTimeout)

    } catch (err) {
      console.error("Unexpected login error:", err)
      setError("An unexpected error occurred. Please try again.")
      setLoading(false)
    }
  }

  // Manual redirect function
  const handleManualRedirect = () => {
    try {
      router.push("/products")
    } catch (routerErr) {
      console.error("Manual router navigation failed:", routerErr)
      // Fallback to window.location
      window.location.href = "/products"
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">Login to Your Account</h1>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {redirecting ? (
          <div className="text-center">
            <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
              Login successful! Redirecting in {countdown} seconds...
            </div>
            <button
              onClick={handleManualRedirect}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors mb-4"
            >
              Go to Products Now
            </button>
          </div>
        ) : (
          <>
            {success && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
                {success}
              </div>
            )}

            <form onSubmit={handleLogin}>
          <div className="mb-4">
            <label htmlFor="mobile" className="block mb-1 font-medium text-gray-700">
              Mobile Number
            </label>
            <input
              type="tel"
              id="mobile"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md"
              placeholder="Enter your mobile number"
              disabled={loading}
              required
            />
            <p className="text-xs text-gray-500 mt-1">Format: 0721234567 or 27721234567</p>
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block mb-1 font-medium text-gray-700">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md"
              placeholder="Enter your password"
              disabled={loading}
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Logging In...
              </>
            ) : (
              "Login"
            )}
          </button>
        </form>
          </>
        )}

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <Link href="/auth" className="text-blue-600 hover:underline">
              Register here
            </Link>
          </p>

          <p className="mt-4">
            <Link href="/" className="text-blue-600 hover:underline text-sm">
              ← Back to Welcome Screen
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}