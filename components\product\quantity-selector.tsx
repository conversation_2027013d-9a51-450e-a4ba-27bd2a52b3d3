"use client"

import { useState, useEffect } from "react"
import { Plus, Minus } from "lucide-react"

interface QuantitySelectorProps {
  initialQuantity?: number
  min?: number
  max?: number
  onChange: (quantity: number) => void
  disabled?: boolean
  compact?: boolean
}

export function QuantitySelector({
  initialQuantity = 1,
  min = 1,
  max = 99,
  onChange,
  disabled = false,
  compact = false
}: QuantitySelectorProps) {
  const [quantity, setQuantity] = useState(initialQuantity)
  const [highlight, setHighlight] = useState(false)
  
  // Update internal state when initialQuantity prop changes
  useEffect(() => {
    if (initialQuantity !== quantity) {
      setQuantity(initialQuantity)
    }
  }, [initialQuantity])
  
  const handleQuantityChange = (newQuantity: number) => {
    // Ensure quantity is within bounds
    const validQuantity = Math.max(min, Math.min(max, newQuantity))
    
    if (validQuantity !== quantity) {
      setQuantity(validQuantity)
      setHighlight(true)
      setTimeout(() => setHighlight(false), 500)
      onChange(validQuantity)
    }
  }
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuantity = parseInt(e.target.value) || min
    handleQuantityChange(newQuantity)
  }
  
  const buttonSize = compact ? "w-6 h-6" : "w-7 h-7"
  const inputWidth = compact ? "w-6" : "w-8"
  
  return (
    <div className="flex flex-col items-center gap-1">
      <div className="flex items-center border border-gray-300 rounded-full overflow-hidden">
        <button
          onClick={() => handleQuantityChange(quantity - 1)}
          className={`bg-gray-100 p-1 flex items-center justify-center ${buttonSize}`}
          type="button"
          disabled={disabled || quantity <= min}
          aria-label="Decrease quantity"
        >
          <Minus size={12} />
        </button>
        <input
          type="number"
          min={min}
          max={max}
          value={quantity}
          onChange={handleInputChange}
          className={`${inputWidth} p-0.5 text-center border-x border-gray-300 text-xs`}
          disabled={disabled}
          aria-label="Quantity"
        />
        <button
          onClick={() => handleQuantityChange(quantity + 1)}
          className={`bg-gray-100 p-1 flex items-center justify-center ${buttonSize}`}
          type="button"
          disabled={disabled || quantity >= max}
          aria-label="Increase quantity"
        >
          <Plus size={12} />
        </button>
      </div>
      <div className={`text-xs font-medium transition-all duration-300 ${highlight ? 'text-green-600 scale-110' : 'text-blue-600'}`}>
        {/* Optional price display can be added here if needed */}
      </div>
    </div>
  )
}
