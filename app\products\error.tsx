"use client"

import { useEffect } from "react"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import Link from "next/link"

export default function ProductsError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Products page error:", error)
  }, [error])

  return (
    <>
      <MainHeader />
      <div className="p-6 flex-grow flex flex-col items-center justify-center">
        <h2 className="text-2xl font-bold text-red-600 mb-4">Something went wrong!</h2>
        <p className="text-gray-600 mb-6 text-center">
          {error.message || "We couldn't load the products. Please try again."}
        </p>
        <div className="flex flex-col gap-3 w-full max-w-xs">
          <button
            onClick={() => reset()}
            className="bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
          >
            Try again
          </button>
          <Link
            href="/"
            className="bg-gray-200 text-gray-800 py-3 px-4 rounded-md font-medium text-center hover:bg-gray-300 transition-colors"
          >
            Go to home page
          </Link>
        </div>
      </div>
      <MainFooter />
    </>
  )
}
