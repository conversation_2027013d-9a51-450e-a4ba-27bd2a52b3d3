-- Updated Database Schema for Spaza Smart Order App

-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  mobile TEXT NOT NULL UNIQUE,
  shop_name TEXT,
  address TEXT,
  is_admin BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table (updated with barcode and stock management)
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  price DECIMAL(10, 2) NOT NULL,
  image_url TEXT,
  category_id UUID REFERENCES categories(id),
  in_stock BOOLEAN DEFAULT TRUE,
  barcode TEXT UNIQUE,
  stock_quantity INTEGER DEFAULT 0,
  last_stock_update TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stock history table (new)
CREATE TABLE stock_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  quantity_change INTEGER NOT NULL,
  new_quantity INTEGER NOT NULL,
  action TEXT NOT NULL, -- 'initial_stock', 'manual_update', 'order_placed', 'stock_adjustment'
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cart items table
CREATE TABLE cart_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, product_id)
);

-- Orders table
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'Processing', -- 'Processing', 'Delivered', 'Cancelled'
  total_amount DECIMAL(10, 2) NOT NULL,
  delivery_address TEXT,
  payment_method TEXT DEFAULT 'COD', -- 'COD', 'Shop2Shop'
  payment_status TEXT DEFAULT 'Pending', -- 'Pending', 'Paid', 'Failed'
  delivery_status TEXT DEFAULT 'Processing', -- 'Processing', 'In Transit', 'Delivered'
  estimated_delivery TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Order items table
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  quantity INTEGER NOT NULL DEFAULT 1,
  price_per_unit DECIMAL(10, 2) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments table
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  amount DECIMAL(10, 2) NOT NULL,
  payment_method TEXT NOT NULL, -- 'COD', 'Shop2Shop'
  status TEXT NOT NULL, -- 'Pending', 'Paid', 'Failed'
  transaction_id TEXT,
  payment_details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE,
  type TEXT, -- 'order_status', 'payment', 'system'
  reference_id UUID, -- Can be order_id or other reference
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_barcode ON products(barcode);
CREATE INDEX idx_products_stock ON products(in_stock, stock_quantity);
CREATE INDEX idx_cart_items_user ON cart_items(user_id);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_stock_history_product ON stock_history(product_id);
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_read ON notifications(user_id, is_read);

-- Create function to update stock when order is placed
CREATE OR REPLACE FUNCTION update_stock_on_order() RETURNS TRIGGER AS $$
BEGIN
  -- For each order item, update the product stock
  FOR item IN SELECT * FROM order_items WHERE order_id = NEW.id LOOP
    -- Get current stock
    DECLARE current_stock INTEGER;
    SELECT stock_quantity INTO current_stock FROM products WHERE id = item.product_id;
    
    -- Calculate new stock
    DECLARE new_stock INTEGER := GREATEST(0, current_stock - item.quantity);
    
    -- Update product stock
    UPDATE products 
    SET 
      stock_quantity = new_stock,
      in_stock = new_stock > 0,
      last_stock_update = NOW()
    WHERE id = item.product_id;
    
    -- Record stock history
    INSERT INTO stock_history (
      product_id, 
      quantity_change, 
      new_quantity, 
      action, 
      notes
    ) VALUES (
      item.product_id,
      -item.quantity,
      new_stock,
      'order_placed',
      'Order ID: ' || NEW.id
    );
  END LOOP;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update stock when order is created
CREATE TRIGGER update_stock_after_order
AFTER INSERT ON orders
FOR EACH ROW
EXECUTE FUNCTION update_stock_on_order();
