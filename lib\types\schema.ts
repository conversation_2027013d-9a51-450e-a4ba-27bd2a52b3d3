// Type definitions for the Supabase database schema

// Enum Types
export enum OrderStatus {
  Pending = 'Pending',
  AwaitingPayment = 'AwaitingPayment',
  Processing = 'Processing',
  ReadyForPickup = 'ReadyForPickup',
  OutForDelivery = 'OutForDelivery',
  Delivered = 'Delivered',
  Cancelled = 'Cancelled',
  Refunded = 'Refunded'
}

export enum PaymentStatus {
  Pending = 'Pending',
  Paid = 'Paid',
  Failed = 'Failed',
  Cancelled = 'Cancelled',
  Refunded = 'Refunded'
}

export enum StockAdjustmentType {
  Initial = 'Initial',
  Sale = 'Sale',
  Return = 'Return',
  ManualCount = 'ManualCount',
  Damage = 'Damage',
  ReceivedStock = 'ReceivedStock',
  Other = 'Other'
}

export enum UserRoleType {
  Owner = 'Owner',
  Admin = 'Admin',
  Staff = 'Staff'
}

// Database Tables
export interface User {
  roles: any
  id: string
  mobile?: string
  full_name?: string
  created_at: string
  updated_at: string
}

export interface Shop {
  id: string
  owner_user_id: string
  name: string
  address?: string
  created_at: string
  updated_at: string
}

export interface Role {
  id: number
  name: string
}

export interface UserRole {
  name: string
  user_id: string
  shop_id: string
  role_id: number
  created_at: string
}

export interface Category {
  id: string
  name: string
  created_at: string
}

export interface Product {
  id: string
  shop_id: string
  name: string
  description?: string
  price: number
  image_url?: string
  category_id?: string
  barcode?: string
  stock_quantity: number
  is_available: boolean
  low_stock_threshold?: number
  created_at: string
  updated_at: string
  last_stock_update?: string
  
  // Joined fields
  category?: Category
}

export interface Order {
  id: string
  shop_id: string
  created_by_user_id: string
  customer_identifier?: string
  status: OrderStatus
  total_amount: number
  delivery_address?: string
  notes?: string
  created_at: string
  updated_at: string
  
  // Joined fields
  items?: OrderItem[]
  payments?: Payment[]
  created_by_user?: User
}

export interface OrderItem {
  id: string
  order_id: string
  product_id: string
  quantity: number
  price_per_unit: number
  total_price: number
  created_at: string
  
  // Joined fields
  product?: Product
}

export interface Payment {
  id: string
  order_id: string
  shop_id: string
  recorded_by_user_id: string
  amount: number
  payment_method: string
  status: PaymentStatus
  transaction_id?: string
  reference?: string
  notes?: string
  payment_details?: any
  paid_at?: string
  created_at: string
  updated_at: string
  
  // Joined fields
  order?: Order
  recorded_by_user?: User
  receipt?: Receipt
}

export interface Receipt {
  id: string
  payment_id: string
  order_id: string
  shop_id: string
  issued_by_user_id: string
  receipt_number?: string
  issued_at: string
  receipt_data?: any
  receipt_url?: string
  created_at: string
  updated_at: string
  
  // Joined fields
  payment?: Payment
  order?: Order
  issued_by_user?: User
}

export interface CartItem {
  id: string
  user_id: string
  product_id: string
  quantity: number
  created_at: string
  updated_at: string
  
  // Joined fields
  product?: Product
}

export interface StockAdjustment {
  id: string
  product_id: string
  shop_id: string
  adjusted_by_user_id: string
  adjustment_type: StockAdjustmentType
  quantity_change: number
  reason?: string
  related_order_item_id?: string
  adjusted_at: string
  
  // Joined fields
  product?: Product
  adjusted_by_user?: User
  related_order_item?: OrderItem
}

// Application-specific types
export interface UserWithRoles extends User {
  roles: {
    shop_id: string;
    role: UserRole;
    shop?: Shop;
  }[];
}

export interface ShopWithMembers extends Shop {
  members: {
    user_id: string;
    role: UserRole;
    user?: User;
  }[];
}

