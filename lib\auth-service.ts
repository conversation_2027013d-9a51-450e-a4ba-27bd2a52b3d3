// "use server"

// import { createServerClient } from '@/utils/supabase/client'
// import { createClient } from '@supabase/supabase-js'
// import { z } from 'zod'

// // Define schemas for input validation
// const mobileSchema = z.string().regex(/^\d{10,12}$/, "Please enter a valid mobile number (10-12 digits)")
// const passwordSchema = z.string().min(8, "Password must be at least 8 characters")
// const fullNameSchema = z.string().min(2, "Full name must be at least 2 characters")
// const shopNameSchema = z.string().min(2, "Shop name must be at least 2 characters")

// // Generate a valid email that will pass validation
// function generateValidEmail(mobile: string): string {
//   // Clean the mobile number
//   const cleanedMobile = mobile.replace(/\D/g, "")
//   // Use a simple format with a real domain
//   return `mobile${cleanedMobile}@spazasmart.app`
// }

// // Sign up function with minimal required fields
// export async function signUp(mobile: string, password: string, fullName: string, shopName?: string, p0?: boolean) {
//   const supabase = await createServerClient()

//   // Validate inputs using Zod schemas
//   const validatedMobile = mobileSchema.safeParse(mobile)
//   const validatedPassword = passwordSchema.safeParse(password)
//   const validatedFullName = fullNameSchema.safeParse(fullName)
//   const validatedShopName = shopName ? shopNameSchema.safeParse(shopName) : { success: true, data: shopName } // Optional shopName

//   if (!validatedMobile.success) {
//     return { success: false, error: validatedMobile.error.message }
//   }

//   if (!validatedPassword.success) {
//     return { success: false, error: validatedPassword.error.message }
//   }

//   if (!validatedFullName.success) {
//     return { success: false, error: validatedFullName.error.message }
//   }

//   if (shopName && !validatedShopName.success) {
//     return { success: false, error: validatedShopName.error.message }
//   }

//   try {
//     // First check if a user with this mobile number already exists
//     const { data: existingUser, error: checkError } = await supabase
//       .from("users")
//       .select("id")
//       .eq("mobile", mobile)
//       .single()

//     if (existingUser) {
//       return {
//         success: false,
//         error: "A user with this mobile number already exists. Please login instead.",
//       }
//     }

//     if (checkError && checkError.code !== "PGRST116") {
//       // PGRST116 is the error code for "no rows returned" which is expected
//       console.error("Error checking for existing user:", checkError)
//       return { success: false, error: "Error checking user records" }
//     }

//     // Generate a deterministic email based on the mobile number
//     const email = generateValidEmail(mobile)

//     // Create auth user with the generated email
//     const { data: authData, error: authError } = await supabase.auth.signUp({
//       email: email,
//       password,
//       options: {
//         data: {
//           mobile: mobile,
//           full_name: fullName,
//         },
//       },
//     })

//     if (authError || !authData.user) {
//       console.error("Error signing up:", authError)
//       return { success: false, error: authError?.message || "Failed to sign up" }
//     }

//     // Create user profile with only the essential fields
//     const { error: profileError } = await supabase.from("users").insert({
//       id: authData.user.id,
//       mobile: mobile,
//     })

//     if (profileError) {
//       console.error("Error creating user profile:", profileError)
//       return { success: false, error: "Failed to create user profile: " + profileError.message }
//     }

//     // Try to update the full name separately
//     try {
//       await supabase.from("users").update({ full_name: fullName }).eq("id", authData.user.id)
//     } catch (err) {
//       console.error("Could not update full name, but continuing:", err)
//     }

//     // If shop name is provided, create a shop using admin client to bypass RLS
//     if (shopName) {
//       try {
//         // Create an admin client with the service role key to bypass RLS
//         const adminSupabase = createClient(
//           process.env.NEXT_PUBLIC_SUPABASE_URL!,
//           process.env.SUPABASE_SERVICE_ROLE_KEY!,
//           {
//             auth: {
//               autoRefreshToken: false,
//               persistSession: false,
//             },
//           },
//         )

//         const { data: shopData, error: shopError } = await adminSupabase
//           .from("shops")
//           .insert({
//             owner_user_id: authData.user.id,
//             name: shopName,
//           })
//           .select()
//           .single()

//         if (shopError) {
//           console.error("Error creating shop with admin client:", shopError)
//           // Continue anyway - the user is created
//         } else if (shopData) {
//           // Get the Owner role ID
//           const { data: roleData } = await adminSupabase.from("roles").select("id").eq("name", "Owner").single()

//           if (roleData) {
//             // Assign the user as the owner of the shop
//             await adminSupabase.from("user_roles").insert({
//               user_id: authData.user.id,
//               shop_id: shopData.id,
//               role_id: roleData.id,
//             })
//           }
//         }
//       } catch (error) {
//         console.error("Error in shop creation process:", error)
//         // Continue anyway - the user is created
//       }
//     }

//     return { success: true, userId: authData.user.id }
//   } catch (error) {
//     console.error("Unexpected error during signup:", error)
//     return { success: false, error: "An unexpected error occurred" }
//   }
// }

// export async function signIn(mobile: string, password: string) {
//   const supabase = await createServerClient()

//   // Validate inputs using Zod schemas
//   const validatedMobile = mobileSchema.safeParse(mobile)
//   const validatedPassword = passwordSchema.safeParse(password)

//   if (!validatedMobile.success) {
//     return { success: false, error: validatedMobile.error.message }
//   }

//   if (!validatedPassword.success) {
//     return { success: false, error: validatedPassword.error.message }
//   }

//   try {
//     // Generate the same email that was used during signup
//     const email = generateValidEmail(mobile)

//     // Sign in with the generated email and password
//     const { data, error } = await supabase.auth.signInWithPassword({
//       email: email,
//       password,
//     })

//     if (error || !data.user) {
//       console.error("Error signing in:", error)
//       return { success: false, error: "Invalid mobile number or password" }
//     }

//     // Ensure user profile exists
//     await ensureUserProfile(data.user.id, mobile)

//     return { success: true, userId: data.user.id }
//   } catch (error) {
//     console.error("Unexpected error during signin:", error)
//     return { success: false, error: "An unexpected error occurred" }
//   }
// }

// // Function to ensure user profile exists
// export async function ensureUserProfile(userId: string, mobile?: string) {
//   try {
//     const adminSupabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!, {
//       auth: {
//         autoRefreshToken: false,
//         persistSession: false,
//       },
//     })

//     // Check if user profile exists
//     const { data: existingUser, error: checkError } = await adminSupabase.from("users").select("id").eq("id", userId)

//     // If no error and user exists, return
//     if (!checkError && existingUser && existingUser.length > 0) {
//       return { success: true }
//     }

//     // If user doesn't exist, create a basic profile
//     const { error: createError } = await adminSupabase.from("users").insert({
//       id: userId,
//       mobile: mobile || "Unknown",
//     })

//     if (createError) {
//       console.error("Error creating user profile:", createError)
//       return { success: false, error: "Failed to create user profile" }
//     }

//     return { success: true }
//   } catch (error) {
//     console.error("Error ensuring user profile:", error)
//     return { success: false, error: "An unexpected error occurred" }
//   }
// }

// export async function signOut() {
//   const supabase = await createServerClient()
//   const { error } = await supabase.auth.signOut()

//   if (error) {
//     console.error("Error signing out:", error)
//     return { success: false, error: error.message }
//   }

//   return { success: true }
// }

// export async function getCurrentUser() {
//   const supabase = await createServerClient()
//   const {
//     data: { user },
//   } = await supabase.auth.getUser()

//   if (!user) {
//     return null
//   }

//   const { data } = await supabase.from("users").select("*").eq("id", user.id).single()

//   return data
// }

// export async function createShop(name: string, address?: string) {
//   const supabase = await createServerClient()

//   // Get the current user
//   const { data: userData } = await supabase.auth.getUser()
//   if (!userData.user) {
//     return { success: false, error: "Not authenticated" }
//   }

//   // Validate inputs using Zod schemas
//   const validatedShopName = shopNameSchema.safeParse(name)

//   if (!validatedShopName.success) {
//     return { success: false, error: validatedShopName.error.message }
//   }

//   try {
//     // Create an admin client with the service role key to bypass RLS
//     const adminSupabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.SUPABASE_SERVICE_ROLE_KEY!, {
//       auth: {
//         autoRefreshToken: false,
//         persistSession: false,
//       },
//     })

//     // Create the shop using admin client
//     const { data: shopData, error: shopError } = await adminSupabase
//       .from("shops")
//       .insert({
//         owner_user_id: userData.user.id,
//         name,
//         address,
//       })
//       .select()
//       .single()

//     if (shopError) {
//       console.error("Error creating shop:", shopError)
//       return { success: false, error: "Failed to create shop" }
//     }

//     // Get the Owner role ID
//     const { data: roleData } = await adminSupabase.from("roles").select("id").eq("name", "Owner").single()

//     if (!roleData) {
//       return { success: false, error: "Owner role not found" }
//     }

//     // Assign the user as the owner of the shop
//     const { error: userRoleError } = await adminSupabase.from("user_roles").insert({
//       user_id: userData.user.id,
//       shop_id: shopData.id,
//       role_id: roleData.id,
//     })

//     if (userRoleError) {
//       console.error("Error assigning user role:", userRoleError)
//       return { success: false, error: "Failed to assign user role" }
//     }

//     return { success: true, shop: shopData }
//   } catch (error) {
//     console.error("Unexpected error during shop creation:", error)
//     return { success: false, error: "An unexpected error occurred" }
//   }
// }
