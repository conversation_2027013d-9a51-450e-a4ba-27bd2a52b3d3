"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { assignDelivery, updateDeliveryStatus } from "@/lib/delivery-service"
import type { DeliveryPartner, DeliveryAssignment } from "@/lib/delivery-service"
import type { Order } from "@/lib/types"

interface DeliveryManagementProps {
  pendingOrders: (Order & { user: { mobile: string; shop_name?: string } })[]
  deliveryPartners: DeliveryPartner[]
  activeDeliveries: DeliveryAssignment[]
}

export function DeliveryManagement({ pendingOrders, deliveryPartners, activeDeliveries }: DeliveryManagementProps) {
  const [selectedOrder, setSelectedOrder] = useState<string>("")
  const [selectedPartner, setSelectedPartner] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [statusUpdating, setStatusUpdating] = useState<Record<string, boolean>>({})
  const router = useRouter()

  const handleAssignDelivery = async () => {
    if (!selectedOrder || !selectedPartner) {
      setError("Please select both an order and a delivery partner")
      return
    }

    setLoading(true)
    setError("")
    setSuccess("")

    try {
      const result = await assignDelivery(selectedOrder, selectedPartner)

      if (!result) {
        throw new Error("Failed to assign delivery")
      }

      setSuccess("Delivery assigned successfully")
      setSelectedOrder("")
      setSelectedPartner("")
      router.refresh()
    } catch (err) {
      console.error("Error assigning delivery:", err)
      setError("Failed to assign delivery")
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (
    assignmentId: string,
    status: "Assigned" | "Picked Up" | "Out for Delivery" | "Delivered" | "Failed",
    paymentCollected: boolean = false
  ) => {
    setStatusUpdating((prev) => ({ ...prev, [assignmentId]: true }))
    setError("")

    try {
      await updateDeliveryStatus(assignmentId, status, undefined, paymentCollected)
      router.refresh()
    } catch (err) {
      console.error("Error updating delivery status:", err)
      setError("Failed to update delivery status")
    } finally {
      setStatusUpdating((prev) => ({ ...prev, [assignmentId]: false }))
    }
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    return date.toLocaleDateString() + " " + date.toLocaleTimeString()
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h2 className="text-lg font-bold mb-4">Assign New Delivery</h2>

        {error && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">{error}</div>}
        {success && <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">{success}</div>}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="order" className="block text-sm font-medium text-gray-700 mb-1">
              Select Order
            </label>
            <select
              id="order"
              value={selectedOrder}
              onChange={(e) => setSelectedOrder(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">-- Select an order --</option>
              {pendingOrders.map((order) => (
                <option key={order.id} value={order.id}>
                  {order.id.slice(0, 8)} - {order.user.shop_name || order.user.mobile} - R
                  {Number(order.total_amount).toFixed(2)}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="partner" className="block text-sm font-medium text-gray-700 mb-1">
              Select Delivery Partner
            </label>
            <select
              id="partner"
              value={selectedPartner}
              onChange={(e) => setSelectedPartner(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">-- Select a delivery partner --</option>
              {deliveryPartners
                .filter((partner) => partner.is_active)
                .map((partner) => (
                  <option key={partner.id} value={partner.id}>
                    {partner.name} - {partner.mobile} {partner.vehicle_type ? `(${partner.vehicle_type})` : ""}
                  </option>
                ))}
            </select>
          </div>
        </div>

        <button
          onClick={handleAssignDelivery}
          disabled={loading || !selectedOrder || !selectedPartner}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-400"
        >
          {loading ? "Assigning..." : "Assign Delivery"}
        </button>
      </div>

      <div className="bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden">
        <h2 className="text-lg font-bold p-4 border-b border-gray-200">Active Deliveries</h2>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Delivery Partner
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Assigned At
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {activeDeliveries.length > 0 ? (
                activeDeliveries.map((delivery) => (
                  <tr key={delivery.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{delivery.order_id.slice(0, 8)}...</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{delivery.partner?.name || "Unknown"}</div>
                      <div className="text-sm text-gray-500">{delivery.partner?.mobile || "N/A"}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          delivery.status === "Delivered"
                            ? "bg-green-100 text-green-800"
                            : delivery.status === "Failed"
                              ? "bg-red-100 text-red-800"
                              : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {delivery.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(delivery.created_at)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      {delivery.status !== "Delivered" && delivery.status !== "Failed" && (
                        <div className="flex flex-col gap-2">
                          <select
                            value={delivery.status}
                            onChange={(e) => handleStatusUpdate(delivery.id, e.target.value as any)}
                            disabled={statusUpdating[delivery.id]}
                            className="text-sm rounded-md border border-gray-300 px-2 py-1"
                          >
                            <option value="Assigned">Assigned</option>
                            <option value="Picked Up">Picked Up</option>
                            <option value="Out for Delivery">Out for Delivery</option>
                            <option value="Delivered">Delivered</option>
                            <option value="Failed">Failed</option>
                          </select>
                          
                          {/* Show payment collection checkbox only when selecting "Delivered" and order is COD */}
                          {delivery.order?.payment_method === "COD" &&
                           delivery.order?.payment_status === "Pending" && (
                            <div className="flex items-center mt-1">
                              <input
                                type="checkbox"
                                id={`payment-collected-${delivery.id}`}
                                className="mr-2"
                                onChange={(e) => {
                                  if (e.target.checked && delivery.status === "Delivered") {
                                    handleStatusUpdate(delivery.id, "Delivered", true)
                                  }
                                }}
                              />
                              <label htmlFor={`payment-collected-${delivery.id}`} className="text-sm">
                                Payment collected
                              </label>
                            </div>
                          )}
                        </div>
                      )}

                      {/* For already delivered orders with pending payment */}
                      {(delivery.status === "Delivered" || delivery.status === "Failed") &&
                       delivery.order?.payment_method === "COD" && 
                       delivery.order?.payment_status === "Pending" && (
                        <div className="flex items-center mt-1">
                          <input
                            type="checkbox"
                            id={`payment-collected-${delivery.id}`}
                            className="mr-2"
                            onChange={(e) => {
                              if (e.target.checked) {
                                handleStatusUpdate(delivery.id, delivery.status, true)
                              }
                            }}
                          />
                          <label htmlFor={`payment-collected-${delivery.id}`} className="text-sm">
                            Payment collected
                          </label>
                        </div>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                    No active deliveries
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
