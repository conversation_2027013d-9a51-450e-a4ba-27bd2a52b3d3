// "use server"

// import { createServerClient } from '@/utils/supabase/client'
// import { updatePaymentStatus } from "./order-service"
// import { sendOrderStatusNotification } from "./notification-service"
// import { generateReceipt } from "./receipt-service"

// // Shop2Shop API configuration
// const SHOP2SHOP_API_URL = process.env.SHOP2SHOP_API_URL || "https://sandbox.shop2shop.co.za/api"
// const SHOP2SHOP_MERCHANT_ID = process.env.SHOP2SHOP_MERCHANT_ID || "test_merchant"
// const SHOP2SHOP_API_KEY = process.env.SHOP2SHOP_API_KEY || "test_key"
// const SHOP2SHOP_RETURN_URL = process.env.SHOP2SHOP_RETURN_URL || "https://spaza-smart-order.vercel.app/api/payment/callback"

// // Define result type for consistent error handling
// type PaymentResult<T> = {
//   success: boolean;
//   data: T | null;
//   error: string | null;
// }

// interface PaymentRequest {
//   orderId: string;
//   amount: number;
//   reference: string;
//   customerEmail?: string;
//   customerMobile: string;
//   customerName?: string;
// }

// /**
//  * Initiates a payment request to Shop2Shop
//  */
// export async function initiatePayment(request: PaymentRequest): Promise<PaymentResult<{
//   paymentUrl: string;
//   paymentId: string;
// }>> {
//   const supabase = await createServerClient()

//   try {
//     // Get order details to verify amount
//     const { data: order, error: orderError } = await supabase
//       .from("orders")
//       .select("*")
//       .eq("id", request.orderId)
//       .single()

//     if (orderError || !order) {
//       return {
//         success: false,
//         data: null,
//         error: "Order not found"
//       }
//     }

//     // Verify amount matches order total (convert to numbers to ensure proper comparison)
//     const orderAmount = typeof order.total_amount === 'string'
//       ? parseFloat(order.total_amount)
//       : order.total_amount

//     if (orderAmount !== request.amount) {
//       return {
//         success: false,
//         data: null,
//         error: "Payment amount does not match order total"
//       }
//     }

//     // Check if payment already exists for this order
//     const { data: existingPayment, error: existingPaymentError } = await supabase
//       .from("payments")
//       .select("*")
//       .eq("order_id", request.orderId)
//       .eq("status", "Pending")
//       .maybeSingle()

//     let payment

//     if (existingPayment) {
//       // Use existing payment record
//       payment = existingPayment

//       // Update the payment record with fresh data
//       await supabase
//         .from("payments")
//         .update({
//           updated_at: new Date().toISOString(),
//           reference: request.reference
//         })
//         .eq("id", existingPayment.id)
//     } else {
//       // Create new payment record in database
//       const { data: newPayment, error: paymentError } = await supabase
//         .from("payments")
//         .insert({
//           order_id: request.orderId,
//           amount: request.amount,
//           payment_method: "Shop2Shop",
//           status: "Pending",
//           reference: request.reference,
//         })
//         .select()
//         .single()

//       if (paymentError || !newPayment) {
//         return {
//           success: false,
//           data: null,
//           error: "Failed to create payment record"
//         }
//       }

//       payment = newPayment
//     }

//     // Generate a unique reference with timestamp to avoid duplicates
//     const uniqueReference = `${payment.id}-${Date.now()}`

//     // Prepare Shop2Shop API request
//     const paymentPayload = {
//       merchantId: SHOP2SHOP_MERCHANT_ID,
//       merchantReference: uniqueReference,
//       amount: Math.round(request.amount * 100), // Convert to cents and ensure it's an integer
//       currencyCode: "ZAR",
//       returnUrl: `${SHOP2SHOP_RETURN_URL}?paymentId=${payment.id}`,
//       cancelUrl: `${SHOP2SHOP_RETURN_URL}?paymentId=${payment.id}&status=cancelled`,
//       notifyUrl: `${SHOP2SHOP_RETURN_URL}/notify`,
//       customer: {
//         email: request.customerEmail || "",
//         mobile: request.customerMobile,
//         firstName: request.customerName || "Customer",
//       },
//       metadata: {
//         orderId: request.orderId
//       }
//     }

//     // In a real implementation, make the API call to Shop2Shop
//     // For now, we'll simulate a successful response with a more realistic payment URL

//     // Simulate Shop2Shop response
//     const mockTransactionId = `txn_${Date.now()}_${Math.floor(Math.random() * 1000000)}`
//     const paymentResponse = {
//       success: true,
//       paymentUrl: `${SHOP2SHOP_API_URL}/checkout/${mockTransactionId}?ref=${uniqueReference}`,
//       paymentId: mockTransactionId
//     }

//     // Update payment record with payment ID and URL
//     await supabase
//       .from("payments")
//       .update({
//         payment_gateway_id: paymentResponse.paymentId,
//         payment_url: paymentResponse.paymentUrl,
//         updated_at: new Date().toISOString()
//       })
//       .eq("id", payment.id)

//     return {
//       success: true,
//       data: {
//         paymentUrl: paymentResponse.paymentUrl,
//         paymentId: payment.id.toString() // Ensure ID is a string
//       },
//       error: null
//     }
//   } catch (error: any) {
//     return {
//       success: false,
//       data: null,
//       error: error.message || "Failed to initiate payment"
//     }
//   }
// }

// /**
//  * Processes a payment callback from Shop2Shop
//  */
// export async function processPaymentCallback(
//   paymentId: string,
//   status: string,
//   transactionId?: string
// ): Promise<PaymentResult<{
//   status: string;
//   orderId: string;
// }>> {
//   const supabase = await createServerClient()

//   try {
//     // Get payment record - convert string ID to number if needed
//     const { data: payment, error: paymentError } = await supabase
//       .from("payments")
//       .select("*, order:orders(*)")
//       .eq("id", parseInt(paymentId, 10) || paymentId)
//       .single()

//     if (paymentError || !payment) {
//       return {
//         success: false,
//         data: null,
//         error: "Payment not found"
//       }
//     }

//     // Map status to payment status
//     const paymentStatus = status === "success" ? "Paid" :
//                           status === "cancelled" ? "Cancelled" : "Failed"

//     // Update payment status
//     const { error: updateError } = await supabase
//       .from("payments")
//       .update({
//         status: paymentStatus,
//         transaction_id: transactionId || payment.transaction_id,
//         updated_at: new Date().toISOString()
//       })
//       .eq("id", paymentId)

//     if (updateError) {
//       return {
//         success: false,
//         data: null,
//         error: `Failed to update payment status: ${updateError.message}`
//       }
//     }

//     // Update order payment status
//     if (payment.order) {
//       try {
//         if (paymentStatus === "Paid") {
//           // First update the payment status
//           await updatePaymentStatus(
//             payment.order.id,
//             "Paid"
//           )

//           // Then update the order status to "Processing" when payment is successful
//           await updateOrderStatus(
//             payment.order.id,
//             "Processing",  // Keep as Processing but update delivery status
//             false  // Don't send notification yet as we'll do it below
//           )

//           // Update delivery status to "Preparing"
//           const { data: orderData } = await supabase
//             .from("orders")
//             .update({ delivery_status: "Preparing" })
//             .eq("id", payment.order.id)
//             .select()
//             .single()

//           // Send a comprehensive notification
//           try {
//             await sendOrderStatusNotification(
//               payment.order.id,
//               "Your payment has been received and your order is being prepared for delivery."
//             )
//           } catch (notificationError) {
//             // Don't fail the whole process if notification fails
//             console.error("Failed to send notification:", notificationError)
//           }
//         } else {
//           // For other statuses, just update the order status
//           await updatePaymentStatus(
//             payment.order.id,
//             paymentStatus === "Paid" ? "Paid" : "Failed"
//           )
//         }
//       } catch (orderUpdateError: any) {
//         return {
//           success: false,
//           data: null,
//           error: `Failed to update order status: ${orderUpdateError.message}`
//         }
//       }
//     } else {
//       return {
//         success: false,
//         data: null,
//         error: "Order not found for this payment"
//       }
//     }

//     return {
//       success: true,
//       data: {
//         status: paymentStatus,
//         orderId: payment.order.id
//       },
//       error: null
//     }
//   } catch (error: any) {
//     return {
//       success: false,
//       data: null,
//       error: error.message || "Failed to process payment callback"
//     }
//   }
// }

// /**
//  * Verifies a payment status with Shop2Shop
//  */
// export async function verifyPaymentStatus(
//   paymentId: string
// ): Promise<PaymentResult<{
//   status: string;
//   paymentId: string;
//   orderId: string;
//   paymentUrl?: string;
// }>> {
//   const supabase = await createServerClient()

//   try {
//     // Get payment record with order details - convert string ID to number if needed
//     const { data: payment, error: paymentError } = await supabase
//       .from("payments")
//       .select("*, order:orders(*)")
//       .eq("id", parseInt(paymentId, 10) || paymentId)
//       .single()

//     if (paymentError || !payment) {
//       return {
//         success: false,
//         data: null,
//         error: "Payment not found"
//       }
//     }

//     // In a real implementation, make an API call to Shop2Shop to verify payment status
//     // For now, we'll simulate a verification process

//     // If the payment is pending and was created more than 15 minutes ago,
//     // randomly decide if it should be marked as paid or failed
//     if (payment.status === "Pending") {
//       const paymentCreatedAt = new Date(payment.created_at).getTime()
//       const fifteenMinutesAgo = Date.now() - 15 * 60 * 1000

//       if (paymentCreatedAt < fifteenMinutesAgo) {
//         // Simulate a 70% chance of payment success
//         const isSuccessful = Math.random() < 0.7

//         // Update payment status
//         const newStatus = isSuccessful ? "Paid" : "Failed"

//         const { error: updateError } = await supabase
//           .from("payments")
//           .update({
//             status: newStatus,
//             updated_at: new Date().toISOString()
//           })
//           .eq("id", paymentId)

//         if (!updateError && payment.order) {
//           // Update order payment status
//           await updatePaymentStatus(
//             payment.order.id,
//             newStatus === "Paid" ? "Paid" : "Failed"
//           )

//           // Send notification for successful payments
//           if (newStatus === "Paid") {
//             try {
//               await sendOrderStatusNotification(
//                 payment.order.id,
//                 "Your payment has been received and your order is being processed."
//               )
//             } catch (notificationError) {
//               // Silently handle notification errors
//             }
//           }
//         }

//         // Update the payment object with the new status
//         payment.status = newStatus
//       }
//     }

//     return {
//       success: true,
//       data: {
//         status: payment.status,
//         paymentId: payment.id.toString(), // Ensure ID is a string
//         orderId: payment.order_id,
//         paymentUrl: payment.payment_url
//       },
//       error: null
//     }
//   } catch (error: any) {
//     return {
//       success: false,
//       data: null,
//       error: error.message || "Failed to verify payment status"
//     }
//   }
// }

// /**
//  * Mark a COD payment as collected
//  */
// export async function markCodPaymentCollected(
//   orderId: string,
//   collectedBy: string,
//   notes?: string
// ): Promise<PaymentResult<any>> {
//   const supabase = await createServerClient()

//   try {
//     // Get the payment record
//     const { data: payment, error: paymentError } = await supabase
//       .from("payments")
//       .select("*")
//       .eq("order_id", orderId)
//       .eq("payment_method", "COD")
//       .single()

//     if (paymentError || !payment) {
//       return {
//         success: false,
//         data: null,
//         error: paymentError?.message || "Payment record not found"
//       }
//     }

//     // Update the payment status
//     const { data: updatedPayment, error: updateError } = await supabase
//       .from("payments")
//       .update({
//         status: "Paid",
//         updated_at: new Date().toISOString(),
//         payment_details: {
//           collected_by: collectedBy,
//           collected_at: new Date().toISOString(),
//           notes: notes
//         }
//       })
//       .eq("id", payment.id)
//       .select()
//       .single()

//     if (updateError) {
//       return {
//         success: false,
//         data: null,
//         error: updateError.message
//       }
//     }

//     // Also update the order payment status
//     const { error: orderError } = await supabase
//       .from("orders")
//       .update({
//         payment_status: "Paid"
//       })
//       .eq("id", orderId)

//     if (orderError) {
//       console.error("Error updating order payment status:", orderError)
//     }

//     return {
//       success: true,
//       data: updatedPayment,
//       error: null
//     }
//   } catch (error: any) {
//     return {
//       success: false,
//       data: null,
//       error: error.message
//     }
//   }
// }

// /**
//  * Update payment status
//  */
// export async function updatePaymentStatus(
//   paymentId: string,
//   status: "Pending" | "Processing" | "Paid" | "Failed",
//   details?: any
// ): Promise<PaymentResult<any>> {
//   const supabase = await createServerClient()

//   try {
//     const { data: payment, error } = await supabase
//       .from("payments")
//       .update({
//         status,
//         payment_details: details,
//         updated_at: new Date().toISOString()
//       })
//       .eq("id", paymentId)
//       .select()
//       .single()

//     if (error) {
//       return {
//         success: false,
//         data: null,
//         error: error.message
//       }
//     }

//     // If payment is marked as paid, generate a receipt
//     if (status === "Paid") {
//       try {
//         await generateReceipt(paymentId)
//       } catch (receiptError) {
//         console.error("Error generating receipt:", receiptError)
//       }
//     }

//     return {
//       success: true,
//       data: payment,
//       error: null
//     }
//   } catch (error: any) {
//     return {
//       success: false,
//       data: null,
//       error: error.message
//     }
//   }
// }

// /**
//  * Get payment statistics
//  */
// export async function getPaymentStats() {
//   const supabase = await createServerClient()

//   try {
//     // Get counts by payment method and status
//     const { data, error } = await supabase
//       .from("payments")
//       .select(`
//         payment_method,
//         status,
//         count
//       `)
//       .select(`
//         payment_method,
//         status,
//         count(*) as count
//       `)
//       .group("payment_method, status")

//     if (error) {
//       console.error("Error fetching payment stats:", error)
//       return null
//     }

//     // Calculate totals by payment method
//     const { data: totals, error: totalsError } = await supabase
//       .from("payments")
//       .select(`
//         payment_method,
//         sum(amount) as total_amount
//       `)
//       .eq("status", "Paid")
//       .group("payment_method")

//     if (totalsError) {
//       console.error("Error fetching payment totals:", totalsError)
//     }

//     return {
//       counts: data,
//       totals: totals || []
//     }
//   } catch (error) {
//     console.error("Exception fetching payment stats:", error)
//     return null
//   }
// }

// /**
//  * Create a payment record for an order
//  */
// export async function createPaymentForOrder(
//   orderId: string,
//   amount: number,
//   paymentMethod: string
// ) {
//   const supabase = await createServerClient()

//   const { data, error } = await supabase
//     .from('payments')
//     .insert({
//       order_id: orderId,
//       amount: amount,
//       payment_method: paymentMethod,
//       status: 'Pending',
//       reference: `ORD-${orderId.substring(0, 8)}`
//     })
//     .select()

//   if (error) {
//     console.error('Error creating payment:', error)
//     return null
//   }

//   return data[0]
// }

