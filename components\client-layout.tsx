"use client"

import { <PERSON><PERSON>N<PERSON>, useEffect, useState } from "react"
import { AuthProvider } from "@/contexts/multi-shop-auth-context"
import { ShopProvider } from "@/contexts/shop-context"
import { CartProvider } from "@/contexts/cart-context"
import { ToastProvider } from "@/components/toast-notification"
import { OfflineProvider } from "@/contexts/offline-context"
import { RealTimeProvider } from "@/contexts/real-time-context"
import { PWAInstallPrompt } from "@/components/pwa-install-prompt"
import { QuickAccessFAB } from "@/components/quick-access-fab"
import { OnboardingOverlay } from "@/components/onboarding-overlay"
import { AccessibilitySettings } from "@/components/accessibility-settings"
import { ThemeScript } from "@/components/theme-script"
import { OfflineIndicator } from "@/components/offline-indicator"
import { AuthStateCleaner } from "@/components/auth-state-cleaner"
import DatabaseInitializer from "@/components/db-initializer"
import { DatabaseStatusIndicator } from "@/components/db-status-indicator"

export function ClientLayout({ children }: { children: ReactNode }) {
  const [mounted, setMounted] = useState(false)

  // Only render client components after mount
  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    // Return a minimal layout while client components are loading
    return (
      <div className="max-w-md mx-auto bg-white dark:bg-gray-900 min-h-screen flex flex-col shadow-md">
        <div className="flex-grow flex items-center justify-center">
          <div className="animate-pulse text-blue-600">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <OfflineProvider>
      <AuthProvider>
        <ShopProvider>
          <RealTimeProvider>
            <CartProvider>
              <ToastProvider>
                <div className="max-w-md mx-auto bg-white dark:bg-gray-900 min-h-screen flex flex-col shadow-md">
                  <ThemeScript />
                  <AuthStateCleaner />
                  <DatabaseInitializer />
                  {children}
                  <PWAInstallPrompt />
                  <QuickAccessFAB />
                  <OnboardingOverlay />
                  <AccessibilitySettings />
                  <OfflineIndicator />
                  <DatabaseStatusIndicator />
                </div>
              </ToastProvider>
            </CartProvider>
          </RealTimeProvider>
        </ShopProvider>
      </AuthProvider>
    </OfflineProvider>
  )
}
