import { NextRequest, NextResponse } from "next/server"
import { createClient } from '@/utils/supabase/client'
import { isProduct } from "@/lib/types"

export async function GET(request: NextRequest) {
  try {
    // Get search parameters
    const { searchParams } = new URL(request.url)
    const query = searchParams.get("query") || ""
    const categoryId = searchParams.get("categoryId") || undefined
    const minPrice = searchParams.get("minPrice") ? parseFloat(searchParams.get("minPrice")!) : undefined
    const maxPrice = searchParams.get("maxPrice") ? parseFloat(searchParams.get("maxPrice")!) : undefined
    const inStock = searchParams.get("inStock") ? searchParams.get("inStock") === "true" : undefined
    const sortBy = searchParams.get("sortBy") || "name"
    const sortOrder = searchParams.get("sortOrder") || "asc"
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0

    // Create Supabase client
    const supabase = await createClient()

    // Start building the query
    let query_builder = supabase
      .from("products")
      .select("*", { count: "exact" })

    // Apply filters
    if (query) {
      query_builder = query_builder.ilike("name", `%${query}%`)
    }

    if (categoryId) {
      query_builder = query_builder.eq("category_id", categoryId)
    }

    if (minPrice !== undefined) {
      query_builder = query_builder.gte("price", minPrice)
    }

    if (maxPrice !== undefined) {
      query_builder = query_builder.lte("price", maxPrice)
    }

    if (inStock !== undefined) {
      query_builder = query_builder.eq("in_stock", inStock)
    }

    // Apply sorting
    if (sortBy === "price") {
      query_builder = query_builder.order("price", { ascending: sortOrder === "asc" })
    } else if (sortBy === "popularity") {
      // Assuming there's a popularity or sales_count field
      // If not, fall back to name sorting
      query_builder = query_builder.order("name", { ascending: sortOrder === "asc" })
    } else {
      // Default to name sorting
      query_builder = query_builder.order("name", { ascending: sortOrder === "asc" })
    }

    // Apply pagination
    query_builder = query_builder.range(offset, offset + limit - 1)

    // Execute the query
    const { data, error, count } = await query_builder

    if (error) {
      return NextResponse.json(
        { error: "Failed to search products" },
        { status: 500 }
      )
    }

    // Validate products
    const validProducts = data?.filter(isProduct) || []

    // Return search results
    return NextResponse.json({
      items: validProducts,
      total: count || validProducts.length,
      hasMore: count ? offset + limit < count : false
    })
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
