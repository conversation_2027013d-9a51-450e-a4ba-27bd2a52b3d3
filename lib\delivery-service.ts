"use server"

import { createClient } from '@/utils/supabase/client'
import type { Order } from "./types"
import { updatePaymentStatus } from './payment-service'

export interface DeliveryPartner {
  id: string
  name: string
  mobile: string
  vehicle_type?: string
  is_active: boolean
  created_at?: string
}

export interface DeliveryAssignment {
  id: string
  order_id: string
  partner_id: string
  status: "Assigned" | "Picked Up" | "Out for Delivery" | "Delivered" | "Failed"
  pickup_time?: string
  delivery_time?: string
  notes?: string
  created_at?: string
  partner?: DeliveryPartner
  order?: Order
}

// Admin functions for delivery partners
export async function getDeliveryPartners() {
  const supabase = await createClient()
  const { data, error } = await supabase.from("delivery_partners").select("*").order("name")

  if (error) {
    console.error("Error fetching delivery partners:", error)
    return []
  }

  return data as DeliveryPartner[]
}

export async function createDeliveryPartner(partner: Omit<DeliveryPartner, "id" | "created_at">) {
  const supabase = await createClient()
  const { data, error } = await supabase.from("delivery_partners").insert(partner).select().single()

  if (error) {
    console.error("Error creating delivery partner:", error)
    return null
  }

  return data as DeliveryPartner
}

export async function updateDeliveryPartner(id: string, partner: Partial<Omit<DeliveryPartner, "id" | "created_at">>) {
  const supabase = await createClient()
  const { data, error } = await supabase.from("delivery_partners").update(partner).eq("id", id).select().single()

  if (error) {
    console.error("Error updating delivery partner:", error)
    return null
  }

  return data as DeliveryPartner
}

export async function toggleDeliveryPartnerStatus(id: string, isActive: boolean) {
  return updateDeliveryPartner(id, { is_active: isActive })
}

// Delivery assignment functions
export async function getDeliveryAssignments(status?: string) {
  const supabase = await createClient()
  let query = supabase
    .from("delivery_assignments")
    .select(`
      *,
      partner:delivery_partners(*),
      order:orders(*)
    `)
    .order("created_at", { ascending: false })

  if (status) {
    query = query.eq("status", status)
  }

  const { data, error } = await query

  if (error) {
    console.error("Error fetching delivery assignments:", error)
    return []
  }

  return data as DeliveryAssignment[]
}

export async function assignDelivery(orderId: string, partnerId: string) {
  const supabase = await createClient()
  const { data, error } = await supabase
    .from("delivery_assignments")
    .insert({
      order_id: orderId,
      partner_id: partnerId,
      status: "Assigned",
    })
    .select()
    .single()

  if (error) {
    console.error("Error assigning delivery:", error)
    return null
  }

  // Update order status
  await supabase.from("orders").update({ delivery_status: "Assigned" }).eq("id", orderId)

  return data as DeliveryAssignment
}

export async function updateDeliveryStatus(
  assignmentId: string,
  status: "Assigned" | "Picked Up" | "Out for Delivery" | "Delivered" | "Failed",
  notes?: string,
  paymentCollected: boolean = false
) {
  const supabase = await createClient()

  const updates: any = { status, notes }

  if (status === "Picked Up") {
    updates.pickup_time = new Date().toISOString()
  } else if (status === "Delivered" || status === "Failed") {
    updates.delivery_time = new Date().toISOString()
  }

  const { data, error } = await supabase
    .from("delivery_assignments")
    .update(updates)
    .eq("id", assignmentId)
    .select(`*, order:orders(*)`)
    .single()

  if (error) {
    console.error("Error updating delivery status:", error)
    return null
  }

  // If delivery is complete and it's a COD order, update payment status
  if (data?.order?.payment_method === "COD" && status === "Delivered" && paymentCollected) {
    try {
      // Update order payment status
      await updatePaymentStatus(data.order.id, "Paid")

      // Also update the payment record in the payments table
      const { error: paymentError } = await supabase
        .from("payments")
        .update({
          status: "Paid",
          updated_at: new Date().toISOString(),
          payment_details: {
            collected_by: "delivery_partner",
            collected_at: new Date().toISOString(),
            delivery_assignment_id: assignmentId
          }
        })
        .eq("order_id", data.order.id)

      if (paymentError) {
        console.error("Error updating payment record:", paymentError)
      }

      // Send notification about payment collection
      await sendOrderStatusNotification(
        data.order.id,
        "Your payment has been collected and your order is now complete. Thank you!"
      )
    } catch (paymentError) {
      console.error("Error updating payment status for COD order:", paymentError)
    }
  }

  return data
}




function sendOrderStatusNotification(id: any, arg1: string) {
  throw new Error('Function not implemented.')
}

