import { NextResponse } from "next/server"
import { devLog, logError } from "@/utils/logger"
import { updateCartItemQuantity } from '@/lib/services/cart-service'
import { z } from 'zod'

// Define a schema for the request body
const updateCartSchema = z.object({
  itemId: z.string().uuid({ message: "Invalid cart item ID format" }),
  quantity: z.number().int().positive({ message: "Quantity must be a positive integer" })
})

export async function POST(request: Request) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validatedBody = updateCartSchema.safeParse(body)

    if (!validatedBody.success) {
      return NextResponse.json(
        { success: false, error: validatedBody.error.issues[0].message },
        { status: 400 }
      )
    }

    const { itemId, quantity } = validatedBody.data

    // Update cart item quantity
    const result = await updateCartItemQuantity(itemId, quantity)

    if (!result.success) {
      logError("Error updating cart item quantity:", result.error)
      return NextResponse.json(
        { success: false, error: result.error || "Error updating cart" },
        { status: 400 }
      )
    }

    devLog(`Successfully updated cart item: ${itemId} to quantity: ${quantity}`)
    return NextResponse.json({ success: true })
  } catch (error) {
    logError("Error in update cart API:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
