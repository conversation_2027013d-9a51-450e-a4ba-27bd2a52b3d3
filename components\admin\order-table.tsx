"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Eye } from "lucide-react"
import { updateOrderStatus } from "@/lib/admin-service"
import { useRouter } from "next/navigation"
import { getSupabaseClient } from "@/lib/supabase"
import type { Order } from "@/lib/types"

interface OrderTableProps {
  orders: (Order & { user: { id: string; mobile: string; shop_name?: string } })[]
}

export function OrderTable({ orders }: OrderTableProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("")
  const [isUpdating, setIsUpdating] = useState<Record<string, boolean>>({})
  const router = useRouter()

  // Subscribe to real-time order inserts/updates and refresh table
  useEffect(() => {
    const supabase = getSupabaseClient()
    const channel = supabase
      .channel('orders_changes')
      .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'orders' }, () => router.refresh())
      .on('postgres_changes', { event: 'UPDATE', schema: 'public', table: 'orders' }, () => router.refresh())
      .subscribe()
    return () => { supabase.removeChannel(channel) }
  }, [router])

  const filteredOrders = orders.filter(
    (order) =>
      (statusFilter === "" || order.status === statusFilter) &&
      (order.user.mobile.includes(searchTerm) ||
        (order.user.shop_name && order.user.shop_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        order.id.includes(searchTerm)),
  )

  const handleStatusChange = async (orderId: string, status: "Processing" | "Delivered" | "Cancelled") => {
    setIsUpdating((prev) => ({ ...prev, [orderId]: true }))

    try {
      await updateOrderStatus(orderId, status)
      router.refresh()
    } catch (error) {
      console.error("Error updating order status:", error)
      alert("Failed to update order status")
    } finally {
      setIsUpdating((prev) => ({ ...prev, [orderId]: false }))
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString() + " " + date.toLocaleTimeString()
  }

  // Bulk selection and CSV export
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const allSelected = filteredOrders.length > 0 && filteredOrders.every(o => selectedOrders.includes(o.id))
  const toggleAll = () => setSelectedOrders(allSelected ? [] : filteredOrders.map(o => o.id))
  const toggleOne = (id: string) => setSelectedOrders(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id])
  const exportCSV = () => {
    const rows = filteredOrders.filter(o => selectedOrders.includes(o.id)).map(o => [
      o.id, o.user.shop_name || '', o.user.mobile, o.status, o.created_at
    ])
    const header = ['Order ID','Shop Name','Mobile','Status','Date']
    const csv = [header, ...rows].map(r => r.join(',')).join('\n')
    const blob = new Blob([csv], { type: 'text/csv' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a'); a.href = url; a.download = 'orders.csv'; a.click(); URL.revokeObjectURL(url)
  }

  return (
    <div>
      <div className="mb-4 flex items-center gap-4">
        <input type="checkbox" checked={allSelected} onChange={toggleAll} />
        <button onClick={exportCSV} disabled={selectedOrders.length===0} className="bg-blue-600 text-white px-3 py-1 rounded disabled:opacity-50">Export CSV</button>
      </div>
      <div className="mb-4 flex flex-col md:flex-row gap-4">
        <input
          type="text"
          placeholder="Search by mobile or shop name..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full md:w-2/3 p-2 border border-gray-300 rounded-md"
        />

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="w-full md:w-1/3 p-2 border border-gray-300 rounded-md"
        >
          <option value="">All Statuses</option>
          <option value="Processing">Processing</option>
          <option value="Delivered">Delivered</option>
          <option value="Cancelled">Cancelled</option>
        </select>
      </div>

      <div className="bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input type="checkbox" checked={allSelected} onChange={toggleAll} />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Order ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrders.length > 0 ? (
                filteredOrders.map((order) => (
                  <tr key={order.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input type="checkbox" checked={selectedOrders.includes(order.id)} onChange={() => toggleOne(order.id)} />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{order.id.slice(0, 8)}...</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{order.user.shop_name || "No Shop Name"}</div>
                      <div className="text-sm text-gray-500">{order.user.mobile}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(order.created_at || "")}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">R {Number(order.total_amount).toFixed(2)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={order.status}
                        onChange={(e) => handleStatusChange(order.id, e.target.value as any)}
                        disabled={isUpdating[order.id]}
                        className={`text-sm rounded-full px-3 py-1 font-semibold ${
                          order.status === "Processing"
                            ? "bg-blue-100 text-blue-800"
                            : order.status === "Delivered"
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                        }`}
                      >
                        <option value="Processing">Processing</option>
                        <option value="Delivered">Delivered</option>
                        <option value="Cancelled">Cancelled</option>
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        order.payment_status === "Paid" 
                          ? "bg-green-100 text-green-800" 
                          : order.payment_status === "Failed"
                            ? "bg-red-100 text-red-800"
                            : order.payment_method === "COD" && order.status === "Delivered"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-gray-100 text-gray-800"
                      }`}>
                        {order.payment_status === "Paid" 
                          ? "Paid" 
                          : order.payment_status === "Failed"
                            ? "Failed"
                            : order.payment_method === "COD" && order.status === "Delivered"
                              ? "COD Pending"
                              : "Pending"}
                      </span>
                      <div className="text-xs text-gray-500 mt-1">{order.payment_method}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link href={`/admin/orders/${order.id}`} className="text-blue-600 hover:text-blue-900">
                        <Eye size={18} />
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                    No orders found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}

