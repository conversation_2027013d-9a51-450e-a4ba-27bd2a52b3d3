# Recommended Project Structure

This document outlines the recommended project structure for the Spaza Smart Order application.

## Directory Structure

```
spaza-smart-order/
├── app/                      # Next.js App Router pages
│   ├── api/                  # API routes
│   ├── admin/                # Admin pages
│   ├── auth/                 # Authentication pages
│   ├── cart/                 # Cart pages
│   ├── orders/               # Order pages
│   ├── products/             # Product pages
│   ├── profile/              # User profile pages
│   ├── layout.tsx            # Root layout
│   └── page.tsx              # Home page
├── components/               # React components
│   ├── admin/                # Admin-specific components
│   ├── auth/                 # Authentication components
│   ├── cart/                 # Cart components
│   ├── common/               # Common/shared components
│   ├── layout/               # Layout components (header, footer, etc.)
│   ├── order/                # Order components
│   ├── product/              # Product components
│   └── ui/                   # UI components (buttons, inputs, etc.)
├── contexts/                 # React contexts
├── hooks/                    # Custom React hooks
├── lib/                      # Utility functions and services
│   ├── api/                  # API client functions
│   ├── services/             # Business logic services
│   ├── supabase/             # Supabase client utilities
│   ├── utils/                # General utilities
│   └── types.ts              # TypeScript type definitions
├── public/                   # Static assets
└── utils/                    # Utility functions
```

## Component Organization

Components should be organized by feature rather than by type. Each component should be in its own directory with the following structure:

```
components/feature/component-name/
├── index.ts                  # Re-exports the component
├── component-name.tsx        # Main component
├── component-name.module.css # Component-specific styles (if needed)
└── component-name.test.tsx   # Component tests (if applicable)
```

## Service Organization

Services should be organized by domain and follow a consistent pattern:

```
lib/services/
├── auth-service.ts           # Authentication service
├── cart-service.ts           # Cart service
├── order-service.ts          # Order service
├── product-service.ts        # Product service
└── user-service.ts           # User service
```

## API Organization

API routes should be organized by domain and follow RESTful principles:

```
app/api/
├── auth/                     # Authentication endpoints
│   ├── login/                # Login endpoint
│   ├── register/             # Registration endpoint
│   └── logout/               # Logout endpoint
├── cart/                     # Cart endpoints
│   ├── add/                  # Add to cart endpoint
│   ├── update/               # Update cart endpoint
│   └── remove/               # Remove from cart endpoint
├── orders/                   # Order endpoints
│   ├── [id]/                 # Order by ID endpoints
│   └── route.ts              # Orders list endpoint
└── products/                 # Product endpoints
    ├── [id]/                 # Product by ID endpoints
    └── route.ts              # Products list endpoint
```

## Migration Strategy

To migrate to this structure:

1. Create the new directory structure
2. Move files to their new locations
3. Update imports in all files
4. Test thoroughly to ensure everything works as expected

This can be done incrementally, starting with one feature at a time, to minimize disruption.
