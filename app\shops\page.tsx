"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useShop } from "@/contexts/shop-context"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Store, PlusCircle, ArrowRight } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { CreateShopForm } from "@/components/create-shop-form"
import { AuthCheck } from "@/components/auth-check"

export default function ShopsPage() {
  const router = useRouter()
  const { isAuthenticated, loading: authLoading } = useAuth()
  const { userShops, currentShop, setCurrentShop, loading: shopLoading } = useShop()
  
  // Redirect to products page if user already has a selected shop
  useEffect(() => {
    if (!authLoading && !shopLoading && isAuthenticated && currentShop) {
      router.push("/products")
    }
  }, [authLoading, shopLoading, isAuthenticated, currentShop, router])
  
  // Handle shop selection
  const handleSelectShop = (shopId: string) => {
    const shop = userShops.find(s => s.id === shopId)
    if (shop) {
      setCurrentShop(shop)
      router.push("/products")
    }
  }
  
  const loading = authLoading || shopLoading
  
  return (
    <AuthCheck>
      <div className="flex flex-col min-h-screen">
        <MainHeader />
        
        <main className="flex-1 p-4">
          <h1 className="text-2xl font-bold mb-6">Select a Shop</h1>
          
          {loading ? (
            <div className="space-y-4">
              {[1, 2].map(i => (
                <div key={i} className="bg-gray-100 animate-pulse h-32 rounded-lg"></div>
              ))}
            </div>
          ) : (
            <>
              {userShops.length === 0 ? (
                <div className="text-center py-8">
                  <Store className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h2 className="text-xl font-semibold mb-2">No Shops Found</h2>
                  <p className="text-gray-500 mb-6">You don't have any shops yet. Create your first shop to get started.</p>
                  
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button className="gap-2">
                        <PlusCircle className="h-4 w-4" />
                        Create Shop
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Create a New Shop</DialogTitle>
                      </DialogHeader>
                      <CreateShopForm />
                    </DialogContent>
                  </Dialog>
                </div>
              ) : (
                <div className="space-y-4">
                  {userShops.map(shop => (
                    <Card key={shop.id} className="border border-gray-200">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Store className="h-5 w-5 text-blue-600" />
                          {shop.name}
                        </CardTitle>
                        <CardDescription>
                          {shop.address || "No address provided"}
                        </CardDescription>
                      </CardHeader>
                      <CardFooter>
                        <Button 
                          onClick={() => handleSelectShop(shop.id)}
                          className="w-full gap-2"
                        >
                          Select Shop
                          <ArrowRight className="h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                  
                  <div className="pt-4">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" className="w-full gap-2">
                          <PlusCircle className="h-4 w-4" />
                          Create Another Shop
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Create a New Shop</DialogTitle>
                        </DialogHeader>
                        <CreateShopForm />
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              )}
            </>
          )}
        </main>
        
        <MainFooter />
      </div>
    </AuthCheck>
  )
}
