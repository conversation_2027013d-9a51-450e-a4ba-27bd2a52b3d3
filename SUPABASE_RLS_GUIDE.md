# Supabase Row Level Security (RLS) Guide

This guide explains how to implement proper Row Level Security (RLS) policies in Supabase to protect your data.

## What is Row Level Security?

Row Level Security (RLS) allows you to control which rows in a table a user can access. This is essential for multi-tenant applications where users should only see their own data.

## Setting Up RLS for Your Tables

### 1. Enable RLS on All Tables

First, enable RLS on all your tables. In the Supabase dashboard:

1. Go to **Authentication > Policies**
2. For each table, click **Enable RLS**

### 2. Create RLS Policies

Here are recommended RLS policies for your main tables:

#### Users Table

```sql
-- Allow users to read their own profile
CREATE POLICY "Users can view own profile"
ON "public"."users"
FOR SELECT
USING (auth.uid() = id);

-- Allow users to update their own profile
CREATE POLICY "Users can update own profile"
ON "public"."users"
FOR UPDATE
USING (auth.uid() = id);

-- Allow users to insert their own profile (during registration)
CREATE POLICY "Users can insert own profile"
ON "public"."users"
FOR INSERT
WITH CHECK (auth.uid() = id);
```

#### Cart Items Table

```sql
-- Allow users to view their own cart items
CREATE POLICY "Users can view own cart items"
ON "public"."cart_items"
FOR SELECT
USING (auth.uid() = user_id);

-- Allow users to insert their own cart items
CREATE POLICY "Users can insert own cart items"
ON "public"."cart_items"
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own cart items
CREATE POLICY "Users can update own cart items"
ON "public"."cart_items"
FOR UPDATE
USING (auth.uid() = user_id);

-- Allow users to delete their own cart items
CREATE POLICY "Users can delete own cart items"
ON "public"."cart_items"
FOR DELETE
USING (auth.uid() = user_id);
```

#### Orders Table

```sql
-- Allow users to view their own orders
CREATE POLICY "Users can view own orders"
ON "public"."orders"
FOR SELECT
USING (auth.uid() = user_id);

-- Allow users to insert their own orders
CREATE POLICY "Users can insert own orders"
ON "public"."orders"
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Allow users to update their own orders (e.g., cancel)
CREATE POLICY "Users can update own orders"
ON "public"."orders"
FOR UPDATE
USING (auth.uid() = user_id);
```

#### Order Items Table

```sql
-- Allow users to view their own order items
CREATE POLICY "Users can view own order items"
ON "public"."order_items"
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM orders
    WHERE orders.id = order_items.order_id
    AND orders.user_id = auth.uid()
  )
);

-- Allow users to insert their own order items
CREATE POLICY "Users can insert own order items"
ON "public"."order_items"
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM orders
    WHERE orders.id = order_items.order_id
    AND orders.user_id = auth.uid()
  )
);
```

#### Products Table

```sql
-- Allow anyone to view products
CREATE POLICY "Anyone can view products"
ON "public"."products"
FOR SELECT
USING (true);

-- Only admins can modify products
CREATE POLICY "Only admins can insert products"
ON "public"."products"
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.is_admin = true
  )
);

CREATE POLICY "Only admins can update products"
ON "public"."products"
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.is_admin = true
  )
);

CREATE POLICY "Only admins can delete products"
ON "public"."products"
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.is_admin = true
  )
);
```

#### Categories Table

```sql
-- Allow anyone to view categories
CREATE POLICY "Anyone can view categories"
ON "public"."categories"
FOR SELECT
USING (true);

-- Only admins can modify categories
CREATE POLICY "Only admins can insert categories"
ON "public"."categories"
FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.is_admin = true
  )
);

CREATE POLICY "Only admins can update categories"
ON "public"."categories"
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.is_admin = true
  )
);

CREATE POLICY "Only admins can delete categories"
ON "public"."categories"
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.is_admin = true
  )
);
```

## Testing Your RLS Policies

After implementing RLS policies, test them to ensure they work as expected:

1. **Regular User Test**:
   - Log in as a regular user
   - Try to access your own data (should succeed)
   - Try to access another user's data (should fail)
   - Try to modify products or categories (should fail)

2. **Admin User Test**:
   - Log in as an admin user
   - Try to access and modify products and categories (should succeed)
   - Try to access another user's private data (should fail unless you've created specific admin policies)

3. **Unauthenticated Test**:
   - Log out
   - Try to access public data like products and categories (should succeed)
   - Try to access private data like orders (should fail)

## Using the Admin Client

When you need to bypass RLS (e.g., for admin operations), use the `createAdminSupabaseClient` function we created earlier. This should only be used in server-side code and only for operations that require admin privileges.

Example:

```typescript
import { createAdminSupabaseClient } from "@/lib/supabase"

// Server-side function that needs admin access
export async function adminFunction() {
  const supabase = await createAdminSupabaseClient()
  
  // This bypasses RLS policies
  const { data, error } = await supabase
    .from("users")
    .select("*")
  
  // Process data...
}
```

Remember, never use the admin client in client-side code!
