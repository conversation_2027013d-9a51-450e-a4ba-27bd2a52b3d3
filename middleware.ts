import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { createServerClient } from "@supabase/ssr"

// Type definitions for role checking
type Role = { name: string }
type UserRole = { role_id: string; roles: Role | Role[] }

export async function middleware(request: NextRequest) {
  let response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  try {
    // Create a Supabase client configured to use cookies
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            // If the cookie is updated, update the cookies for the request and response
            request.cookies.set({
              name,
              value,
              ...options,
            })
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            })
            response.cookies.set({
              name,
              value,
              ...options,
            })
          },
          remove(name: string, options: any) {
            // If the cookie is removed, update the cookies for the request and response
            request.cookies.set({
              name,
              value: "",
              ...options,
            })
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            })
            response.cookies.set({
              name,
              value: "",
              ...options,
            })
          },
        },
      },
    )

    // This will refresh the session if it exists
    const {
      data: { user },
    } = await supabase.auth.getUser()

    // Define protected paths
    const authRequiredPaths = ["/products", "/cart", "/orders", "/profile", "/checkout"]
    const isAuthRequired = authRequiredPaths.some((path) => request.nextUrl.pathname.startsWith(path))

    // Check if path requires a shop selection
    const requiresShop = ["/products", "/cart", "/orders"]
    const needsShop = requiresShop.some((path) => request.nextUrl.pathname.startsWith(path))

    if (user) {
      // Get current shop from Supabase if user is authenticated
      const { data: shopData } = await supabase
        .from('user_shops')
        .select('shop_id')
        .eq('user_id', user.id)
        .single()

      if (needsShop && !shopData?.shop_id) {
        const redirectUrl = new URL("/shops", request.url)
        return NextResponse.redirect(redirectUrl)
      }
    }

    // Handle authentication redirects
    if (!user && isAuthRequired) {
      const redirectUrl = new URL("/login", request.url)
      return NextResponse.redirect(redirectUrl)
    }

    // Check if path is an admin path
    const isAdminPath = request.nextUrl.pathname.startsWith("/admin") && request.nextUrl.pathname !== "/admin/login"

    // If trying to access admin paths without being logged in, redirect to admin login
    if (!user && isAdminPath) {
      const redirectUrl = new URL("/admin/login", request.url)
      return NextResponse.redirect(redirectUrl)
    }

    // If the user is signed in and trying to access auth or login page
    if (user && (request.nextUrl.pathname === "/auth" || request.nextUrl.pathname === "/login")) {
      // Check if there's a redirect loop happening
      const redirectCount = parseInt(request.cookies.get('redirect_count')?.value || '0')

      if (redirectCount > 2) {
        // Break the loop by clearing the cookie and staying on the current page
        response.cookies.set('redirect_count', '0', { path: '/' })
        return response
      }

      // Increment redirect count
      const redirectUrl = new URL("/products", request.url)
      response = NextResponse.redirect(redirectUrl)
      response.cookies.set('redirect_count', (redirectCount + 1).toString(), { path: '/' })
      return response
    }

    // If the user is signed in but trying to access admin login, redirect to admin dashboard if admin, otherwise to products
    if (user && request.nextUrl.pathname === "/admin/login") {
      // Check if user has admin or owner role in any shop
      const { data: userRoles } = await supabase
        .from("user_roles")
        .select(`
          role_id,
          roles:roles(name)
        `)
        .eq("user_id", user.id)

      const isAdmin = userRoles?.some((role: { role_id: string; roles: { name: string }[] }) => {
        const roleName = Array.isArray(role.roles) ? role.roles[0]?.name : role.roles?.name;
        return roleName === 'Owner' || roleName === 'Admin';
      }) || false;

      if (isAdmin) {
        const redirectUrl = new URL("/admin", request.url)
        return NextResponse.redirect(redirectUrl)
      } else {
        const redirectUrl = new URL("/products", request.url)
        return NextResponse.redirect(redirectUrl)
      }
    }

    // If trying to access admin paths without being admin, redirect to products
    if (user && isAdminPath) {
      // Check if user has admin or owner role in any shop
      const { data: userRoles } = await supabase
        .from("user_roles")
        .select('*, roles:roles(name)')
        .eq("user_id", user.id)

      const isAdmin = userRoles?.some(role => {
        const roleName = role?.roles?.name || '';
        return roleName === 'Owner' || roleName === 'Admin';
      }) || false;

      if (!isAdmin) {
        const redirectUrl = new URL("/products", request.url)
        return NextResponse.redirect(redirectUrl)
      }
    }

    return response
  } catch (error) {
    console.error("Middleware error:", error)
    // If there's an error in the middleware, allow the request to continue
    // The application's error handling will take care of it
    return response
  }
}

export const config = {
  matcher: [
    "/products/:path*",
    "/cart/:path*",
    "/orders/:path*",
    "/auth",
    "/login",
    "/profile/:path*",
    "/checkout/:path*",
    "/admin/:path*",
    "/logout",
  ],
}

