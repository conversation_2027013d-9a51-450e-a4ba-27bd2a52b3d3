//  import  createSupabaseClient  from '@/utils/supabase/client';
// import  createSuperbaseServerClient  from '@/utils/supabase/server';    
// import { devLog, logError } from '@/utils/logger';
// import { Database } from '@/lib/database.types';

// export type Product = Database['public']['Tables']['products']['Row'];
// export type Category = Database['public']['Tables']['categories']['Row'];

// /**
//  * Get all products for a shop
//  * @param shopId Shop ID
//  * @returns Array of products
//  */
// export async function getProducts(shopId: string) {
//   const supabase = createSupabaseClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('products')
//       .select('*')
//       .eq('shop_id', shopId)
//       .order('name');
    
//     if (error) {
//       logError("Error fetching products:", error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError("Unexpected error fetching products:", error);
//     return [];
//   }
// }

// /**
//  * Get a product by ID
//  * @param productId Product ID
//  * @returns Product or null
//  */
// export async function getProductById(productId: string) {
//   const supabase = createSuperbaseServerClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('products')
//       .select('*')
//       .eq('id', productId)
//       .single();
    
//     if (error) {
//       logError(`Error fetching product ${productId}:`, error);
//       return null;
//     }
    
//     return data;
//   } catch (error) {
//     logError(`Unexpected error fetching product ${productId}:`, error);
//     return null;
//   }
// }

// /**
//  * Create a new product
//  * @param product Product data
//  * @returns Result object with success status and product ID or error
//  */
// export async function createProduct(product: Omit<Product, 'id' | 'created_at' | 'updated_at'>) {
//   const supabase = createSuperbaseServerClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('products')
//       .insert({
//         ...product,
//         created_at: new Date().toISOString(),
//         updated_at: new Date().toISOString()
//       })
//       .select()
//       .single();
    
//     if (error) {
//       logError("Error creating product:", error);
//       return { success: false, error: "Failed to create product" };
//     }
    
//     return { success: true, productId: data.id };
//   } catch (error) {
//     logError("Unexpected error creating product:", error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Update a product
//  * @param productId Product ID
//  * @param updates Product updates
//  * @returns Result object with success status or error
//  */
// export async function updateProduct(
//   productId: string, 
//   updates: Partial<Omit<Product, 'id' | 'created_at' | 'updated_at'>>
// ) {
//   const supabase = createSuperbaseServerClient();
  
//   try {
//     const { error } = await supabase
//       .from('products')
//       .update({
//         ...updates,
//         updated_at: new Date().toISOString()
//       })
//       .eq('id', productId);
    
//     if (error) {
//       logError(`Error updating product ${productId}:`, error);
//       return { success: false, error: "Failed to update product" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error updating product ${productId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Delete a product
//  * @param productId Product ID
//  * @returns Result object with success status or error
//  */
// export async function deleteProduct(productId: string) {
//   const supabase = createSuperbaseServerClient();
  
//   try {
//     const { error } = await supabase
//       .from('products')
//       .delete()
//       .eq('id', productId);
    
//     if (error) {
//       logError(`Error deleting product ${productId}:`, error);
//       return { success: false, error: "Failed to delete product" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error deleting product ${productId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Get all categories
//  * @returns Array of categories
//  */
// export async function getCategories() {
//   const supabase = createSuperbaseServerClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('categories')
//       .select('*')
//       .order('name');
    
//     if (error) {
//       logError("Error fetching categories:", error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError("Unexpected error fetching categories:", error);
//     return [];
//   }
// }

// /**
//  * Get products by category
//  * @param categoryId Category ID
//  * @param shopId Shop ID
//  * @returns Array of products
//  */
// export async function getProductsByCategory(categoryId: string, shopId: string) {
//   const supabase = createSuperbaseServerClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('products')
//       .select('*')
//       .eq('category_id', categoryId)
//       .eq('shop_id', shopId)
//       .order('name');
    
//     if (error) {
//       logError(`Error fetching products for category ${categoryId}:`, error);
//       return [];
//     }
    
//     return data || [];
//   } catch (error) {
//     logError(`Unexpected error fetching products for category ${categoryId}:`, error);
//     return [];
//   }
// }
