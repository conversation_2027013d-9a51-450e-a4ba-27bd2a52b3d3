// import { createClient } from '@/utils/supabase/client';
// import { devLog, logError } from '@/utils/logger';
// import { Database } from '@/lib/database.types';

// export type User = Database['public']['Tables']['users']['Row'];
// export type Shop = Database['public']['Tables']['shops']['Row'];
// export type UserRole = Database['public']['Tables']['user_roles']['Row'];
// export type Role = Database['public']['Tables']['roles']['Row'];

// /**
//  * Get a user by ID
//  * @param userId User ID
//  * @returns User or null
//  */
// export async function getUserById(userId: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('users')
//       .select('*')
//       .eq('id', userId)
//       .single();
    
//     if (error) {
//       logError(`Error fetching user ${userId}:`, error);
//       return null;
//     }
    
//     return data;
//   } catch (error) {
//     logError(`Unexpected error fetching user ${userId}:`, error);
//     return null;
//   }
// }

// /**
//  * Get a user by mobile number
//  * @param mobile Mobile number
//  * @returns User or null
//  */
// export async function getUserByMobile(mobile: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('users')
//       .select('*')
//       .eq('mobile', mobile)
//       .single();
    
//     if (error) {
//       logError(`Error fetching user by mobile ${mobile}:`, error);
//       return null;
//     }
    
//     return data;
//   } catch (error) {
//     logError(`Unexpected error fetching user by mobile ${mobile}:`, error);
//     return null;
//   }
// }

// /**
//  * Update a user profile
//  * @param userId User ID
//  * @param updates User profile updates
//  * @returns Result object with success status or error
//  */
// export async function updateUserProfile(
//   userId: string, 
//   updates: Partial<Omit<User, 'id' | 'created_at'>>
// ) {
//   const supabase = createClient();
  
//   try {
//     const { error } = await supabase
//       .from('users')
//       .update({
//         ...updates,
//         updated_at: new Date().toISOString()
//       })
//       .eq('id', userId);
    
//     if (error) {
//       logError(`Error updating user ${userId}:`, error);
//       return { success: false, error: "Failed to update user profile" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error updating user ${userId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Get shops for a user
//  * @param userId User ID
//  * @returns Array of shops with user roles
//  */
// export async function getUserShops(userId: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('user_roles')
//       .select(`
//         shop_id,
//         role_id,
//         roles (name),
//         shops (*)
//       `)
//       .eq('user_id', userId);
    
//     if (error) {
//       logError(`Error fetching shops for user ${userId}:`, error);
//       return [];
//     }
    
//     // Transform the data to a more usable format
//     return (data || []).map((item: { shops: any; role_id: any; roles: { name: any; }[]; shop_id: any; }) => ({
//       ...item.shops[0],
//       role: {
//         id: item.role_id,
//         name: item.roles?.[0]?.name
//       }
//     }));
//   } catch (error) {
//     logError(`Unexpected error fetching shops for user ${userId}:`, error);
//     return [];
//   }
// }

// /**
//  * Get a shop by ID
//  * @param shopId Shop ID
//  * @returns Shop or null
//  */
// export async function getShopById(shopId: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('shops')
//       .select('*')
//       .eq('id', shopId)
//       .single();
    
//     if (error) {
//       logError(`Error fetching shop ${shopId}:`, error);
//       return null;
//     }
    
//     return data;
//   } catch (error) {
//     logError(`Unexpected error fetching shop ${shopId}:`, error);
//     return null;
//   }
// }

// /**
//  * Create a new shop
//  * @param ownerId Owner user ID
//  * @param name Shop name
//  * @param address Shop address
//  * @returns Result object with success status and shop ID or error
//  */
// export async function createShop(ownerId: string, name: string, address?: string) {
//   const supabase = createClient();
  
//   try {
//     // Create the shop
//     const { data: shop, error: shopError } = await supabase
//       .from('shops')
//       .insert({
//         owner_user_id: ownerId,
//         name,
//         address,
//         created_at: new Date().toISOString(),
//         updated_at: new Date().toISOString()
//       })
//       .select()
//       .single();
    
//     if (shopError) {
//       logError("Error creating shop:", shopError);
//       return { success: false, error: "Failed to create shop" };
//     }
    
//     // Get owner role ID
//     const { data: ownerRole } = await supabase
//       .from('roles')
//       .select('id')
//       .eq('name', 'Owner')
//       .single();
    
//     if (!ownerRole) {
//       logError("Owner role not found");
//       return { success: true, shopId: shop.id }; // Still return success since shop was created
//     }
    
//     // Assign owner role to user
//     const { error: roleError } = await supabase
//       .from('user_roles')
//       .insert({
//         user_id: ownerId,
//         shop_id: shop.id,
//         role_id: ownerRole.id,
//         created_at: new Date().toISOString()
//       });
    
//     if (roleError) {
//       logError("Error assigning owner role:", roleError);
//       // Don't return error since shop was created successfully
//     }
    
//     return { success: true, shopId: shop.id };
//   } catch (error) {
//     logError("Unexpected error creating shop:", error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Update a shop
//  * @param shopId Shop ID
//  * @param updates Shop updates
//  * @returns Result object with success status or error
//  */
// export async function updateShop(
//   shopId: string, 
//   updates: Partial<Omit<Shop, 'id' | 'owner_user_id' | 'created_at'>>
// ) {
//   const supabase = createClient();
  
//   try {
//     const { error } = await supabase
//       .from('shops')
//       .update({
//         ...updates,
//         updated_at: new Date().toISOString()
//       })
//       .eq('id', shopId);
    
//     if (error) {
//       logError(`Error updating shop ${shopId}:`, error);
//       return { success: false, error: "Failed to update shop" };
//     }
    
//     return { success: true };
//   } catch (error) {
//     logError(`Unexpected error updating shop ${shopId}:`, error);
//     return { success: false, error: "An unexpected error occurred" };
//   }
// }

// /**
//  * Check if a user has a specific role in a shop
//  * @param userId User ID
//  * @param shopId Shop ID
//  * @param roleName Role name
//  * @returns Boolean indicating if user has the role
//  */
// export async function userHasRole(userId: string, shopId: string, roleName: string) {
//   const supabase = createClient();
  
//   try {
//     const { data, error } = await supabase
//       .from('user_roles')
//       .select(`
//         roles (name)
//       `)
//       .eq('user_id', userId)
//       .eq('shop_id', shopId);
    
//     if (error) {
//       logError(`Error checking user role for user ${userId} in shop ${shopId}:`, error);
//       return false;
//     }
    
//     return (data || []).some((item: { roles: { name: any; } }) => item.roles?.name === roleName);
//   } catch (error) {
//     logError(`Unexpected error checking user role for user ${userId} in shop ${shopId}:`, error);
//     return false;
//   }
// }



