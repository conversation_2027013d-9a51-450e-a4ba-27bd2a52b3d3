// Test script to verify the Supabase connection
// Read environment variables directly from .env.local file
const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Read and parse .env.local file
const envPath = path.resolve('.env.local');
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  // Skip comments and empty lines
  if (!line || line.startsWith('#')) return;

  const [key, ...valueParts] = line.split('=');
  if (key && valueParts.length) {
    const value = valueParts.join('=').trim();
    envVars[key.trim()] = value;
  }
});

// Set environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = envVars.NEXT_PUBLIC_SUPABASE_URL;
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

async function testSupabaseConnection() {
  console.log('Testing Supabase connection...');

  // Check if environment variables are set
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
    console.error('Error: NEXT_PUBLIC_SUPABASE_URL is not set');
    return;
  }

  if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.error('Error: NEXT_PUBLIC_SUPABASE_ANON_KEY is not set');
    return;
  }

  console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
  console.log('Anon Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 10) + '...');

  try {
    // Create a Supabase client with increased timeout
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        global: {
          fetch: (url, options) => {
            const timeout = 60000; // 60 seconds timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);

            return fetch(url, {
              ...options,
              signal: controller.signal,
            }).finally(() => clearTimeout(timeoutId));
          }
        }
      }
    );

    console.log('Supabase client created successfully');

    // Test a simple query
    console.log('Testing a simple query...');
    const startTime = Date.now();
    const { data, error } = await supabase.from('users').select('count(*)', { count: 'exact', head: true });
    const endTime = Date.now();

    if (error) {
      console.error('Error executing query:', error);
    } else {
      console.log('Query executed successfully in', endTime - startTime, 'ms');
      console.log('Result:', data);
    }

    // Test auth status
    console.log('Testing auth status...');
    const { data: authData, error: authError } = await supabase.auth.getSession();

    if (authError) {
      console.error('Error getting auth status:', authError);
    } else {
      console.log('Auth status retrieved successfully');
      console.log('Session:', authData.session ? 'Active' : 'None');
    }

    console.log('All tests completed');
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testSupabaseConnection();
