"use client"

import { useState } from "react"
import { ShoppingCart, Check } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useCart } from "@/contexts/cart-context"
import { useToast } from "@/components/toast-notification"
import { useRouter } from "next/navigation"

interface AddToCartButtonProps {
  productId: string
  productName: string
  quantity: number
  inStock: boolean
  compact?: boolean
}

export function AddToCartButton({
  productId,
  productName,
  quantity,
  inStock,
  compact = false
}: AddToCartButtonProps) {
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const { user } = useAuth()
  const { addToCart } = useCart()
  const { showToast } = useToast()
  const router = useRouter()
  
  const handleAddToCart = async () => {
    // Check if user is authenticated
    if (!user) {
      // Redirect to auth page if not logged in
      router.push("/auth")
      return
    }
    
    if (!inStock) {
      showToast("This product is out of stock", "error")
      return
    }
    
    setLoading(true)
    
    try {
      // Use the cart context to add to cart
      const result = await addToCart(productId, quantity)
      
      if (!result) {
        throw new Error('Failed to add to cart')
      }
      
      // Show success state and toast notification
      setSuccess(true)
      setTimeout(() => setSuccess(false), 2000) // Reset after 2 seconds
      showToast(`Added ${quantity} x ${productName} to cart`, "success")
    } catch (error) {
      console.error("Error adding to cart:", error)
      // Show error toast
      showToast("Failed to add to cart", "error")
    } finally {
      setLoading(false)
    }
  }
  
  const buttonClasses = compact 
    ? "px-2 py-1 text-xs rounded-md"
    : "px-3 py-1.5 text-sm rounded-full"
  
  return (
    <button
      onClick={handleAddToCart}
      disabled={loading || success || !inStock}
      className={`transition-colors flex items-center justify-center gap-1 ${buttonClasses} ${
        !inStock
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
          : success
            ? 'bg-green-500 text-white'
            : 'bg-green-600 text-white hover:bg-green-700 disabled:bg-green-400'
      }`}
      aria-label={inStock ? "Add to cart" : "Out of stock"}
    >
      {success ? (
        <>
          <Check size={compact ? 12 : 16} />
          <span className="whitespace-nowrap">Added!</span>
        </>
      ) : (
        <>
          <ShoppingCart size={compact ? 12 : 16} />
          <span className="whitespace-nowrap">{loading ? "Adding..." : "Add"}</span>
        </>
      )}
    </button>
  )
}
