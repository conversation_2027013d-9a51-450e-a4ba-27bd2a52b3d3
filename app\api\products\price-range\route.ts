import { NextResponse } from "next/server"
import { createClient } from '@/utils/supabase/client'

export async function GET() {
  try {
    // Create Supabase client
    const supabase = await createClient()

    // Get min price
    const { data: minData, error: minError } = await supabase
      .from("products")
      .select("price")
      .order("price", { ascending: true })
      .limit(1)
      .single()

    if (minError) {
      return NextResponse.json(
        { min: 0, max: 1000 },
        { status: 200 }
      )
    }

    // Get max price
    const { data: maxData, error: maxError } = await supabase
      .from("products")
      .select("price")
      .order("price", { ascending: false })
      .limit(1)
      .single()

    if (maxError) {
      return NextResponse.json(
        { min: 0, max: 1000 },
        { status: 200 }
      )
    }

    // Parse prices
    const minPrice = typeof minData.price === 'string'
      ? parseFloat(minData.price)
      : minData.price

    const maxPrice = typeof maxData.price === 'string'
      ? parseFloat(maxData.price)
      : maxData.price

    // Return price range
    return NextResponse.json({
      min: Math.floor(minPrice),
      max: Math.ceil(maxPrice)
    })
  } catch (error) {
    return NextResponse.json(
      { min: 0, max: 1000 },
      { status: 200 }
    )
  }
}
