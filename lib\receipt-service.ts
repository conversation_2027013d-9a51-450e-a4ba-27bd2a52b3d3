// import { createServerClient } from '@/utils/supabase/client'
// import { v4 as uuidv4 } from "uuid"

// import { Receipt as ReceiptType } from './types'

// export type ReceiptWithDetails = ReceiptType & {
//   payment: {
//     payment_method: string
//     status: string
//     order_id: string
//   }
//   order: {
//     id: string
//     user_id: string
//     total_amount: number
//   }
//   user: {
//     mobile: string
//     shop_name: string
//   }
// }

// export async function generateReceipt(paymentId: string): Promise<ReceiptType | null> {
//   try {
//     console.log(`Generating receipt for payment ID ${paymentId}`)
//     const supabase = await createServerClient()

//     // Check if receipt already exists
//     const { data: existingReceipt, error: receiptError } = await supabase
//       .from("receipts")
//       .select("*")
//       .eq("payment_id", paymentId)
//       .single()

//     if (receiptError && receiptError.code !== 'PGRST116') {
//       console.error(`Error checking for existing receipt for payment ${paymentId}:`, receiptError)
//     }

//     if (existingReceipt) {
//       console.log(`Receipt already exists for payment ${paymentId}, returning existing receipt`)
//       return existingReceipt
//     }

//     // Get payment details
//     const { data: payment, error: paymentError } = await supabase
//       .from("payments")
//       .select("*, orders(*)")
//       .eq("id", paymentId)
//       .single()

//     if (paymentError) {
//       console.error(`Error fetching payment ${paymentId}:`, paymentError)
//       return null
//     }

//     if (!payment) {
//       console.error(`No payment found with ID ${paymentId}`)
//       return null
//     }

//     // Generate receipt number (simple implementation)
//     const receiptNumber = `R-${Date.now().toString().slice(-6)}`
//     const receiptId = uuidv4()
//     const now = new Date().toISOString()

//     console.log(`Creating new receipt ${receiptId} with number ${receiptNumber} for payment ${paymentId}`)

//     // Get payment amount
//     const amount = payment.amount || 0

//     // Create receipt record
//     const receiptData = {
//       id: receiptId,
//       payment_id: paymentId,
//       receipt_number: receiptNumber,
//       amount: amount,
//       issued_date: now,
//       created_at: now,
//       updated_at: now
//     }

//     const { data: receipt, error } = await supabase
//       .from("receipts")
//       .insert(receiptData)
//       .select()

//     if (error) {
//       console.error(`Error creating receipt for payment ${paymentId}:`, error)
//       return null
//     }

//     if (!receipt || receipt.length === 0) {
//       console.error(`Receipt created but no data returned for payment ${paymentId}`)
//       return null
//     }

//     console.log(`Successfully created receipt ${receiptId} for payment ${paymentId}`)
//     return receipt[0] as ReceiptType
//   } catch (error) {
//     console.error(`Error generating receipt for payment ${paymentId}:`, error)
//     return null
//   }
// }

// /**
//  * Get all receipts with pagination
//  */
// export async function getReceipts(page = 1, limit = 20): Promise<{
//   receipts: ReceiptWithDetails[]
//   total: number
// }> {
//   const supabase = await createServerClient()
//   const offset = (page - 1) * limit

//   try {
//     // Get receipts with related data
//     const { data, error, count } = await supabase
//       .from("receipts")
//       .select(`
//         *,
//         payment:payments(payment_method, status, order_id),
//         order:payments!inner(orders!inner(id, user_id, total_amount)),
//         user:payments!inner(orders!inner(users!inner(mobile, shop_name)))
//       `, { count: 'exact' })
//       .order('issued_date', { ascending: false })
//       .range(offset, offset + limit - 1)

//     if (error) {
//       console.error("Error fetching receipts:", error)
//       return { receipts: [], total: 0 }
//     }

//     return {
//       receipts: data as ReceiptWithDetails[],
//       total: count || 0
//     }
//   } catch (error) {
//     console.error("Exception fetching receipts:", error)
//     return { receipts: [], total: 0 }
//   }
// }

// /**
//  * Get a single receipt by ID
//  */
// export async function getReceiptById(id: string): Promise<ReceiptWithDetails | null> {
//   const supabase = await createServerClient()

//   try {
//     const { data, error } = await supabase
//       .from("receipts")
//       .select(`
//         *,
//         payment:payments(payment_method, status, order_id),
//         order:payments!inner(orders!inner(id, user_id, total_amount)),
//         user:payments!inner(orders!inner(users!inner(mobile, shop_name)))
//       `)
//       .eq("id", id)
//       .single()

//     if (error) {
//       console.error("Error fetching receipt:", error)
//       return null
//     }

//     return data as ReceiptWithDetails
//   } catch (error) {
//     console.error("Exception fetching receipt:", error)
//     return null
//   }
// }

// /**
//  * Debug function to manually generate receipts for all paid payments
//  * that don't have receipts yet
//  */
// export async function generateMissingReceipts(): Promise<{ success: boolean, count: number }> {
//   const supabase = await createServerClient()

//   try {
//     // Get all paid payments that don't have receipts
//     const { data: payments, error: paymentsError } = await supabase
//       .from("payments")
//       .select("id")
//       .eq("status", "Paid")
//       .not("id", "in", supabase.from("receipts").select("payment_id"))

//     if (paymentsError) {
//       console.error("Error finding payments without receipts:", paymentsError)
//       return { success: false, count: 0 }
//     }

//     console.log(`Found ${payments.length} payments without receipts`)

//     // Generate receipts for each payment
//     let successCount = 0
//     for (const payment of payments) {
//       const receipt = await generateReceipt(payment.id)
//       if (receipt) successCount++
//     }

//     return { success: true, count: successCount }
//   } catch (error) {
//     console.error("Exception generating missing receipts:", error)
//     return { success: false, count: 0 }
//   }
// }

// /**
//  * Generate receipts for all orders with "Paid" payment status
//  */
// export async function generateReceiptsForPaidOrders() {
//   const supabase = await createServerClient()

//   try {
//     // Get all orders with "Paid" payment status
//     const { data: orders, error: ordersError } = await supabase
//       .from("orders")
//       .select("id, payment_status")
//       .eq("payment_status", "Paid")

//     if (ordersError) {
//       console.error("Error fetching paid orders:", ordersError)
//       return { success: false, message: ordersError.message }
//     }

//     console.log(`Found ${orders.length} orders with Paid payment status`)

//     // For each order, find the payment and generate a receipt
//     let receiptCount = 0
//     for (const order of orders) {
//       // Get the payment for this order
//       const { data: payment, error: paymentError } = await supabase
//         .from("payments")
//         .select("id")
//         .eq("order_id", order.id)
//         .eq("status", "Paid")
//         .single()

//       if (paymentError || !payment) {
//         console.error(`No paid payment found for order ${order.id}`)
//         continue
//       }

//       // Check if receipt already exists
//       const { data: existingReceipt } = await supabase
//         .from("receipts")
//         .select("id")
//         .eq("payment_id", payment.id)
//         .single()

//       if (existingReceipt) {
//         console.log(`Receipt already exists for payment ${payment.id}`)
//         continue
//       }

//       // Generate receipt
//       const receipt = await generateReceipt(payment.id)
//       if (receipt) {
//         receiptCount++
//         console.log(`Generated receipt for payment ${payment.id}`)
//       }
//     }

//     return {
//       success: true,
//       message: `Generated ${receiptCount} receipts for ${orders.length} paid orders`
//     }
//   } catch (error) {
//     console.error("Exception generating receipts:", error)
//     return { success: false, message: "Internal error" }
//   }
// }




