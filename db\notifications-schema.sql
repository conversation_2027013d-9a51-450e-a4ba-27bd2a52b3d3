-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL, -- SMS, WhatsApp
  message TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'Pending', -- Pending, Sent, Failed
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on notifications table
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- RLS policies for notifications
CREATE POLICY notifications_read_own ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY notifications_admin_read ON notifications
  FOR SELECT USING (is_admin());

CREATE POLICY notifications_admin_insert ON notifications
  FOR INSERT WITH CHECK (is_admin());
