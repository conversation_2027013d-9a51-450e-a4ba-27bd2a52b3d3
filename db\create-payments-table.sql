CREATE TABLE public.payments (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  order_id uuid NOT NULL,
  amount numeric(10, 2) NOT NULL,
  payment_method text NOT NULL,
  status text NOT NULL DEFAULT 'Pending',
  transaction_id text NULL,
  payment_details jsonb NULL,
  payment_url text NULL,
  payment_gateway_id text NULL,
  reference text NULL,
  created_at timestamp with time zone NULL DEFAULT now(),
  updated_at timestamp with time zone NULL DEFAULT now(),
  CONSTRAINT payments_pkey PRIMARY KEY (id),
  CONSTRAINT payments_order_id_fkey FOREIGN KEY (order_id) REFERENCES public.orders(id) ON DELETE CASCADE,
  CONSTRAINT payments_status_check CHECK (
    status = ANY (ARRAY['Pending'::text, 'Paid'::text, 'Failed'::text, 'Cancelled'::text])
  )
) TABLESPACE pg_default;

-- Create index for faster lookups
CREATE INDEX payments_order_id_idx ON public.payments(order_id);

-- Add RLS policies
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own payments
CREATE POLICY payments_select_policy ON public.payments
  FOR SELECT
  USING (
    order_id IN (
      SELECT id FROM public.orders WHERE user_id = auth.uid()
    )
  );

-- Policy for service role to manage all payments
CREATE POLICY payments_all_operations_service_policy ON public.payments
  USING (auth.role() = 'service_role' OR auth.role() = 'authenticated');

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_payments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_payments_updated_at
BEFORE UPDATE ON payments
FOR EACH ROW
EXECUTE FUNCTION update_payments_updated_at();



