"use client"

import { useState } from "react"
import { getSupabaseClient } from "@/lib/supabase"
import Link from "next/link"

export default function DirectLoginPage() {
  const [mobileNumber, setMobileNumber] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [debugInfo, setDebugInfo] = useState<any>(null)

  // Generate a valid email that will pass validation
  function generateValidEmail(mobile: string): string {
    // Clean the mobile number
    const cleanedMobile = mobile.replace(/\D/g, "")
    // Use a very simple format with a real domain
    return `mobile${cleanedMobile}@gmail.com`
  }

  const handleDirectLogin = async () => {
    if (!mobileNumber || !password) {
      setError("Please enter both mobile number and password")
      return
    }

    setLoading(true)
    setError("")
    setSuccess("")
    setDebugInfo(null)

    try {
      const supabase = getSupabaseClient()
      const email = generateValidEmail(mobileNumber)
      
      // Sign in directly with the Supabase client
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password,
      })

      if (error) {
        setError(error.message)
        setDebugInfo({ type: "error", data: error })
        return
      }

      // Get session to verify it was created
      const { data: sessionData } = await supabase.auth.getSession()
      
      setSuccess("Login successful! Session created.")
      setDebugInfo({ 
        type: "success", 
        user: data.user,
        session: sessionData.session
      })
      
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred")
      setDebugInfo({ type: "exception", error: err })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Direct Login (Bypass Middleware)</h1>
      <p className="mb-4 text-gray-600">This page tests direct login with Supabase, bypassing the server-side auth service.</p>
      
      {error && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">{error}</div>}
      {success && <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">{success}</div>}
      
      <div className="space-y-4 mb-6">
        <div>
          <label className="block text-gray-700 mb-1">Mobile Number</label>
          <input
            type="tel"
            value={mobileNumber}
            onChange={(e) => setMobileNumber(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Enter your mobile number"
          />
        </div>
        
        <div>
          <label className="block text-gray-700 mb-1">Password</label>
          <input
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Enter your password"
          />
        </div>
      </div>
      
      <button
        onClick={handleDirectLogin}
        disabled={loading}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md text-lg font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400"
      >
        {loading ? "Logging In..." : "Direct Login"}
      </button>
      
      {debugInfo && (
        <div className="mt-6">
          <h2 className="font-bold mb-2">Debug Information:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto text-xs">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mt-6 flex gap-4">
        <Link href="/test-auth" className="text-blue-600 hover:underline">
          Go to Auth Test Page
        </Link>
        <Link href="/products" className="text-blue-600 hover:underline">
          Try Products Page
        </Link>
      </div>
    </div>
  )
}
