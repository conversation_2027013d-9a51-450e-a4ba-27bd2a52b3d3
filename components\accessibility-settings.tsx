"use client"

import { useState, useEffect } from "react"
import { 
  Moon, Sun, ZoomIn, ZoomOut, Eye, 
  X, Check, RotateCcw, Keyboard
} from "lucide-react"

export function AccessibilitySettings() {
  const [isOpen, setIsOpen] = useState(false)
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system')
  const [fontSize, setFontSize] = useState(16)
  const [highContrast, setHighContrast] = useState(false)
  
  // Load saved settings
  useEffect(() => {
    const savedTheme = localStorage.getItem('a11y-theme') as 'light' | 'dark' | 'system' || 'system'
    const savedFontSize = parseInt(localStorage.getItem('a11y-fontSize') || '16')
    const savedHighContrast = localStorage.getItem('a11y-highContrast') === 'true'
    
    setTheme(savedTheme)
    setFontSize(savedFontSize)
    setHighContrast(savedHighContrast)
    
    // Apply settings
    applyTheme(savedTheme)
    applyFontSize(savedFontSize)
    applyHighContrast(savedHighContrast)
  }, [])
  
  const applyTheme = (newTheme: 'light' | 'dark' | 'system') => {
    const root = document.documentElement
    
    if (newTheme === 'dark' || (newTheme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      root.classList.add('dark')
      document.body.style.backgroundColor = '#1a1a1a'
      document.body.style.color = '#ffffff'
    } else {
      root.classList.remove('dark')
      document.body.style.backgroundColor = ''
      document.body.style.color = ''
    }
    
    localStorage.setItem('a11y-theme', newTheme)
  }
  
  const applyFontSize = (size: number) => {
    document.documentElement.style.fontSize = `${size}px`
    localStorage.setItem('a11y-fontSize', size.toString())
  }
  
  const applyHighContrast = (enabled: boolean) => {
    const root = document.documentElement
    
    if (enabled) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }
    
    localStorage.setItem('a11y-highContrast', enabled.toString())
  }
  
  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme)
    applyTheme(newTheme)
  }
  
  const handleFontSizeChange = (newSize: number) => {
    setFontSize(newSize)
    applyFontSize(newSize)
  }
  
  const handleHighContrastChange = (enabled: boolean) => {
    setHighContrast(enabled)
    applyHighContrast(enabled)
  }
  
  const resetSettings = () => {
    handleThemeChange('system')
    handleFontSizeChange(16)
    handleHighContrastChange(false)
  }
  
  return (
    <>
      {/* Accessibility button */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-20 left-4 z-40 bg-blue-600 text-white rounded-full p-3 shadow-lg"
        aria-label="Accessibility settings"
      >
        <Eye size={20} />
      </button>
      
      {/* Settings panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-sm w-full mx-4 animate-in slide-in-from-bottom-10 duration-200">
            <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
              <h2 className="text-lg font-bold flex items-center gap-2">
                <Eye size={20} />
                Accessibility Settings
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                aria-label="Close settings"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-4 space-y-6">
              {/* Theme settings */}
              <div>
                <h3 className="font-medium mb-2">Theme</h3>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleThemeChange('light')}
                    className={`flex-1 border rounded-lg p-3 flex flex-col items-center gap-2 ${
                      theme === 'light' ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/30' : 'border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <Sun size={24} className={theme === 'light' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'} />
                    <span>Light</span>
                  </button>
                  
                  <button
                    onClick={() => handleThemeChange('dark')}
                    className={`flex-1 border rounded-lg p-3 flex flex-col items-center gap-2 ${
                      theme === 'dark' ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/30' : 'border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <Moon size={24} className={theme === 'dark' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'} />
                    <span>Dark</span>
                  </button>
                  
                  <button
                    onClick={() => handleThemeChange('system')}
                    className={`flex-1 border rounded-lg p-3 flex flex-col items-center gap-2 ${
                      theme === 'system' ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/30' : 'border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <Keyboard size={24} className={theme === 'system' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-400 dark:text-gray-500'} />
                    <span>System</span>
                  </button>
                </div>
              </div>
              
              {/* Font size settings */}
              <div>
                <h3 className="font-medium mb-2">Font Size</h3>
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => handleFontSizeChange(Math.max(12, fontSize - 1))}
                    className="p-2 border rounded-full dark:border-gray-700"
                    aria-label="Decrease font size"
                    disabled={fontSize <= 12}
                  >
                    <ZoomOut size={20} />
                  </button>
                  
                  <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
                    <div 
                      className="h-2 bg-blue-600 rounded-full"
                      style={{ width: `${((fontSize - 12) / 12) * 100}%` }}
                    ></div>
                  </div>
                  
                  <button
                    onClick={() => handleFontSizeChange(Math.min(24, fontSize + 1))}
                    className="p-2 border rounded-full dark:border-gray-700"
                    aria-label="Increase font size"
                    disabled={fontSize >= 24}
                  >
                    <ZoomIn size={20} />
                  </button>
                </div>
                <div className="text-center mt-2 text-sm text-gray-500 dark:text-gray-400">
                  {fontSize}px
                </div>
              </div>
              
              {/* High contrast mode */}
              <div>
                <h3 className="font-medium mb-2">High Contrast</h3>
                <button
                  onClick={() => handleHighContrastChange(!highContrast)}
                  className={`w-full border rounded-lg p-3 flex items-center justify-between ${
                    highContrast ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/30' : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <span>Enable high contrast mode</span>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    highContrast ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                  }`}>
                    {highContrast && <Check size={16} className="text-white" />}
                  </div>
                </button>
              </div>
              
              {/* Reset button */}
              <button
                onClick={resetSettings}
                className="w-full mt-4 flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                <RotateCcw size={16} />
                Reset to defaults
              </button>
            </div>
            
            <div className="border-t p-4 flex justify-end dark:border-gray-700">
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
