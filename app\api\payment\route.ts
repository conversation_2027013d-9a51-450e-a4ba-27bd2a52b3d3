import { NextRequest, NextResponse } from "next/server"
import { createClient } from '@/utils/supabase/client'
import { initiatePayment } from "@/lib/payment-service"
import { getUserId } from "@/lib/auth-utils"

export async function POST(request: NextRequest) {
  try {
    // Get user ID from session
    const userId = await getUserId()

    if (!userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { orderId } = body

    if (!orderId) {
      return NextResponse.json(
        { error: "Order ID is required" },
        { status: 400 }
      )
    }

    // Create Supabase client
    const supabase = await createClient()

    // Verify order belongs to user
    const { data: order, error: orderError } = await supabase
      .from("orders")
      .select("*")
      .eq("id", orderId)
      .eq("user_id", userId)
      .single()

    if (orderError || !order) {
      return NextResponse.json(
        { error: "Order not found or access denied" },
        { status: 404 }
      )
    }

    // Initiate payment
    const result = await initiatePayment(orderId)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      paymentUrl: result.data?.paymentUrl,
      paymentId: result.data?.paymentId
    })
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}