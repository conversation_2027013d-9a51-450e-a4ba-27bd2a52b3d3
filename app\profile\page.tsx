"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { AdminAccess } from "@/components/admin-access"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { updateUserProfile } from "@/lib/user-service"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { User, MapPin, Phone, Store, Clock, ShoppingBag, LogOut } from "lucide-react"
import { createClient } from '@/utils/supabase/client'

export default function ProfilePage() {
  const { user, signOut } = useAuth()
  const router = useRouter()

  const [formData, setFormData] = useState({
    full_name: user?.full_name || "",
    mobile: user?.mobile || "",
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const [activeTab, setActiveTab] = useState("profile")
  const [fetchingProfile, setFetchingProfile] = useState(false)

  // Fetch the latest profile data directly from Supabase
  useEffect(() => {
    const fetchProfileFromSupabase = async () => {
      if (!user?.id || fetchingProfile) return

      setFetchingProfile(true)
      try {
        console.log("Profile: Fetching user data from Supabase for ID:", user.id)
        const supabase = createClient()

        const { data, error } = await supabase
          .from("users")
          .select("*")
          .eq("id", user.id)
          .single()

        console.log("Profile: Supabase response:", { data, error })

        if (error) {
          console.error("Profile: Error fetching user data:", error)
          setError("Failed to load your profile data")
        } else if (data) {
          console.log("Profile: Successfully fetched user data:", data)
          // Update form with the latest data from Supabase
          setFormData({
            full_name: data.full_name || "",
            mobile: data.mobile || "",
          })
        }
      } catch (err) {
        console.error("Profile: Exception fetching user data:", err)
        setError("An unexpected error occurred while loading your profile")
      } finally {
        setFetchingProfile(false)
      }
    }

    fetchProfileFromSupabase()
  }, [user?.id])

  // Update form data when user changes (e.g., after login)
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        full_name: prev.full_name || user.full_name || "",
        mobile: prev.mobile || user.mobile || "",
      }))
    }
  }, [user])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess("")

    try {
      if (!user) {
        throw new Error("User not authenticated")
      }

      console.log("Profile: Submitting update with data:", formData)
      const result = await updateUserProfile(user.id, formData)

      if (!result) {
        throw new Error("Failed to update profile")
      }

      setSuccess("Profile updated successfully")

      // Refresh the profile data after update
      const supabase = createClient()
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .eq("id", user.id)
        .single()

      if (!error && data) {
        console.log("Profile: Refreshed data after update:", data)
      }
    } catch (err) {
      console.error("Profile update error:", err)
      setError("An error occurred while updating your profile")
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    await signOut()
    router.push("/")
  }

  if (!user) {
    return (
      <>
        <MainHeader />
        <div className="p-4 flex-grow">
          <p className="text-center">Please log in to view your profile</p>
        </div>
        <MainFooter />
      </>
    )
  }

  return (
    <>
      <MainHeader />
      <div className="p-4 flex-grow overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Your Account</h1>
          {user.mobile && (
            <div className="flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
              <User size={14} />
              {user.mobile}
            </div>
          )}
        </div>

        {error && <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md flex items-center gap-2"><User size={18} />{error}</div>}
        {success && <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md flex items-center gap-2"><User size={18} />{success}</div>}
        {fetchingProfile && <div className="mb-4 p-3 bg-blue-100 text-blue-700 rounded-md flex items-center gap-2"><Clock size={18} />Loading your profile data...</div>}

        {/* Profile tabs */}
        <div className="flex border-b border-gray-200 mb-4">
          <button
            className={`px-4 py-2 font-medium text-sm ${activeTab === 'profile' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('profile')}
          >
            Profile
          </button>
          <button
            className={`px-4 py-2 font-medium text-sm ${activeTab === 'orders' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500 hover:text-gray-700'}`}
            onClick={() => setActiveTab('orders')}
          >
            Orders
          </button>
        </div>

        {activeTab === 'profile' ? (
          <form onSubmit={handleSubmit} className="space-y-4">

            <div className="bg-white rounded-md p-4 border border-gray-200">
              <div className="mb-4">
                <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                  <User size={16} />
                  Full Name
                </label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  value={formData.full_name}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                  <Phone size={16} />
                  Mobile Number
                </label>
                <input
                  type="tel"
                  id="mobile"
                  name="mobile"
                  value={formData.mobile}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  placeholder="Enter your mobile number"
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">Mobile number cannot be changed</p>
              </div>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center justify-center gap-2"
            >
              <User size={18} />
              {loading ? "Updating..." : "Update Profile"}
            </button>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="bg-white rounded-md p-4 border border-gray-200">
              <h2 className="font-medium text-lg mb-3 flex items-center gap-2">
                <ShoppingBag size={18} className="text-blue-600" />
                Recent Orders
              </h2>

              <div className="space-y-3">
                {/* This would be populated with real order data */}
                <div className="border-b border-gray-100 pb-3">
                  <div className="flex justify-between">
                    <div className="font-medium">Order #12345</div>
                    <div className="text-sm text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">Processing</div>
                  </div>
                  <div className="text-sm text-gray-500 flex items-center gap-1 mt-1">
                    <Clock size={14} />
                    Placed on {new Date().toLocaleDateString()}
                  </div>
                  <div className="mt-2 text-sm">3 items • R 250.00</div>
                </div>

                <Link href="/orders" className="block text-center text-blue-600 border border-blue-600 rounded-md py-2 hover:bg-blue-50 transition-colors mt-4">
                  View All Orders
                </Link>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6">
          <button
            onClick={handleLogout}
            className="w-full bg-red-600 text-white py-3 px-4 rounded-md font-medium hover:bg-red-700 transition-colors flex items-center justify-center gap-2"
          >
            <LogOut size={18} />
            Logout
          </button>

          {/* Hidden admin access - appears after 5 taps in the top-right corner */}
          <AdminAccess />
        </div>
      </div>
      <MainFooter />
    </>
  )
}


