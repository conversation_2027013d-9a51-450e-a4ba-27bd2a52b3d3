"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import { createClient } from "@/utils/supabase/client"
import type { User } from "@/lib/types"
import { devLog, devInfo, logError } from "@/utils/logger"

interface AuthContextType {
  user: User | null
  loading: boolean
  isAdmin: boolean
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  isAdmin: false,
  signOut: async () => {},
})

export const useAuth = () => useContext(AuthContext)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const fetchUser = async () => {
      try {
        devLog('Auth context: Checking for existing session...');
        // Check for existing session
        const { data: sessionData } = await supabase.auth.getSession()

        if (sessionData.session) {
          devLog('Auth context: Session found, getting user...');
          // Auth user found

          const {
            data: { user: authUser },
          } = await supabase.auth.getUser()

          if (authUser) {
            devLog('Auth context: Auth user found, getting profile...');
            // Get user profile

            const { data, error } = await supabase.from("users").select("*").eq("id", authUser.id).single()

            if (error) {
              devLog('Auth context: Error fetching user profile:', error.code);
              // Error fetching user profile

              // If the profile doesn't exist, try to create a minimal one
              if (error.code === 'PGRST116') {
                devLog('Auth context: User profile not found, creating minimal profile directly...');

                // Extract user data from metadata if available
                const mobile = authUser.user_metadata?.mobile || 'unknown'
                const email = authUser.email || undefined
                const isAdmin = authUser.user_metadata?.is_admin === true

                // Create a minimal user profile
                const newUserProfile = {
                  id: authUser.id,
                  mobile: mobile,
                  email: email,
                  shop_name: authUser.user_metadata?.shop_name || '',
                  address: authUser.user_metadata?.address || '',
                  is_admin: isAdmin,
                  created_at: new Date().toISOString()
                };

                try {
                  // Insert directly into the users table
                  const { data: newUser, error: insertError } = await supabase
                    .from("users")
                    .insert([newUserProfile])
                    .select()
                    .single();

                  if (insertError) {
                    logError('Auth context: Failed to create user profile directly:', insertError);
                    devLog('Auth context: Insert error details:', JSON.stringify(insertError));

                    // Even if there's an error, set a minimal user object to allow basic functionality
                    setUser(newUserProfile as User);
                    setLoading(false);
                  } else {
                    devLog('Auth context: Successfully created user profile directly');

                    // Update the user state with the new profile
                    if (newUser) {
                      setUser(newUser as User);
                      setLoading(false);

                      // Store login state
                      localStorage.setItem('userLoggedIn', 'true');
                    }
                  }
                } catch (err) {
                  console.error('Auth context: Exception creating user profile directly:', err);

                  // Set a minimal user object even if there's an error
                  setUser(newUserProfile as User);
                  setLoading(false);
                }
              } else {
                console.log('Auth context: Setting user to null due to profile error');
                setUser(null);
                setLoading(false);
              }
            } else {
              devLog('Auth context: User profile loaded successfully');
              // User profile loaded successfully
              // Ensure data has the required User properties
              if (data && typeof data === 'object' && 'id' in data && 'mobile' in data) {
                // Create a properly typed User object
                const userData: User = {
                  id: String(data.id),
                  mobile: String(data.mobile),
                  // shop_name: data.shop_name ? String(data.shop_name) : undefined, // shop_name is not part of the User type
                  // address: data.address ? String(data.address) : undefined,
                  is_admin: Boolean(data.is_admin),
                  created_at: data.created_at ? String(data.created_at) : undefined
                }
                setUser(userData);
                setLoading(false);
              } else {
                // User data missing required fields
                devLog('Auth context: User data missing required fields');
                setUser(null);
                setLoading(false);
              }
            }
          } else {
            // No auth user found despite session
            devLog('Auth context: No auth user found despite session');
            setUser(null);
            setLoading(false);
          }
        } else {
          // No session found
          devLog('Auth context: No session found');
          setUser(null);
          setLoading(false);
        }
      } catch (error) {
        // Error in fetchUser
        logError('Auth context: Error in fetchUser:', error);
        setUser(null);
        setLoading(false);
      }
    }

    // Initial session check
    (async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
          await fetchUser();
        } else {
          setUser(null);
          setLoading(false);
        }
      } catch (error) {
        logError('Auth context: Error getting session:', error);
        setUser(null);
        setLoading(false);
      }
    })();

    // Setting up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(async (event: string, session: any) => {
      devLog(`Auth context: Auth state changed: ${event}`);
      // Auth state changed

      if (event === "SIGNED_IN" && session?.user) {
        devLog('Auth context: User signed in, setting up minimal user immediately');

        // Immediately set a minimal user to speed up the process
        const mobile = session.user.user_metadata?.mobile || undefined;
        const email = session.user.email || undefined;
        const isAdmin = session.user.user_metadata?.is_admin === true;
        const minimalUser = {
          id: session.user.id,
          mobile: mobile,
          email: email,
          shop_name: session.user.user_metadata?.shop_name || '',
          address: session.user.user_metadata?.address || '',
          is_admin: isAdmin,
          created_at: new Date().toISOString()
        } as User;

        // Set user and loading state
        setUser(minimalUser);
        setLoading(false);

        // Store login state in localStorage for better persistence
        localStorage.setItem('userLoggedIn', 'true');

        // Then fetch the complete profile in the background
        devLog('Auth context: Fetching complete profile in background...');
        try {
          const { data, error } = await supabase.from("users").select("*").eq("id", session.user.id).single();

          if (error) {
            devLog('Auth context: Error fetching user profile after sign in:', error.code);

            // If the profile doesn't exist, try to create a minimal one
            if (error.code === 'PGRST116') {
              devLog('Auth context: User profile not found after sign in, creating minimal profile directly...');

              try {
                // Create a minimal user profile directly in the database
                const newUserProfile = {
                  id: session.user.id,
                  mobile: mobile || 'unknown',
                  email: session.user.email || undefined,
                  shop_name: session.user.user_metadata?.shop_name || '',
                  address: session.user.user_metadata?.address || '',
                  is_admin: isAdmin,
                  created_at: new Date().toISOString()
                };

                // Insert directly into the users table
                const { data: newUser, error: insertError } = await supabase
                  .from("users")
                  .insert([newUserProfile])
                  .select()
                  .single();

                if (insertError) {
                  console.error('Auth context: Failed to create user profile directly:', insertError);
                  console.log('Auth context: Insert error details:', JSON.stringify(insertError));

                  // Even if there's an error, set a minimal user object to allow basic functionality
                  setUser(newUserProfile as User);
                } else {
                  console.log('Auth context: Successfully created user profile directly');

                  // Update the user state with the new profile
                  if (newUser) {
                    setUser(newUser as User);

                    // We don't need to redirect here anymore
                    // The login page will handle the redirection
                    // Just log that the user profile was created successfully
                    devLog("Auth context: User profile created successfully");
                  }
                }
              } catch (error) {
                logError('Auth context: Exception creating user profile directly:', error);
              }
            }
          } else {
            devLog('Auth context: User profile loaded after sign in');
            // User profile loaded after sign in
            // Ensure data has the required User properties
            if (data && typeof data === 'object' && 'id' in data && 'mobile' in data) {
              // Create a properly typed User object
              const userData: User = {
                id: String(data.id),
                mobile: String(data.mobile),
                // shop_name: data.shop_name ? String(data.shop_name) : undefined, // shop_name is not part of the User type 
                // address: data.address ? String(data.address) : undefined, // address is not part of the User type
                is_admin: Boolean(data.is_admin),
                created_at: data.created_at ? String(data.created_at) : undefined
              }
              setUser(userData);
            }
          }
        } catch (error) {
          logError('Auth context: Exception fetching user profile after sign in:', error);
        }
      } else if (event === "SIGNED_OUT") {
        devLog('Auth context: User signed out');
        // User signed out
        setUser(null);
        setLoading(false);
        // Clear login state
        localStorage.removeItem('userLoggedIn');
        // Redirect to home page
        window.location.href = '/';
      } else if (event === "TOKEN_REFRESHED") {
        devLog('Auth context: Token refreshed, fetching user data again');
        // Token was refreshed, fetch user data again
        fetchUser();
      }
    })

    return () => {
      authListener.subscription.unsubscribe()
    }
  }, [supabase])

  const signOut = async () => {
    try {
      devLog('Auth context: Signing out user');
      const { error } = await supabase.auth.signOut()
      if (error) {
        logError('Auth context: Error signing out:', error);
        // Even if there's an error, still try to clean up the state
        setUser(null);
        // Force a page reload to clear any cached state
        window.location.href = '/';
      } else {
        devLog('Auth context: User signed out successfully');
        // User signed out successfully
        setUser(null);
        // Clear any cookies
        document.cookie.split(';').forEach(c => {
          document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
        });
        // Force a page reload to clear any cached state
        window.location.href = '/';
      }
    } catch (err) {
      logError('Auth context: Exception during sign out:', err);
      // Even if there's an exception, still try to clean up the state
      setUser(null);
      // Force a page reload to clear any cached state
      window.location.href = '/';
    }
  }

  return (
    <AuthContext.Provider value={{ user, loading, isAdmin: user?.is_admin || false, signOut }}>
      {children}
    </AuthContext.Provider>
  )
}