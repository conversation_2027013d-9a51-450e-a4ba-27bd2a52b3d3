import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/client'

export async function POST(request: NextRequest) {
  try {
    // Get Supabase client
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get request body
    const body = await request.json()
    const { mobile, full_name } = body

    // Check if user profile already exists
    const { data: existingProfile, error: profileError } = await supabase
      .from('users')
      .select('id')
      .eq('id', user.id)
      .single()

    if (existingProfile) {
      // Profile already exists, return success
      return NextResponse.json({ success: true, message: 'Profile already exists' })
    }

    // Create user profile
    const { data: newProfile, error: insertError } = await supabase
      .from('users')
      .insert([
        {
          id: user.id,
          mobile: mobile || 'unknown',
          full_name: full_name || user.user_metadata?.full_name || '',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ])
      .select()
      .single()

    // Create a default shop for the user
    if (!insertError && newProfile) {
      const { data: shopData, error: shopError } = await supabase
        .from('shops')
        .insert({
          owner_user_id: user.id,
          name: `${full_name || 'My'}'s Shop`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (!shopError && shopData) {
        // Assign owner role to the user for this shop
        const { data: roleData } = await supabase
          .from('roles')
          .select('id')
          .eq('name', 'Owner')
          .single()

        if (roleData) {
          await supabase
            .from('user_roles')
            .insert({
              user_id: user.id,
              shop_id: shopData.id,
              role_id: roleData.id,
              created_at: new Date().toISOString()
            })
        }
      }
    }

    if (insertError) {
      console.error('Error creating user profile:', insertError)
      return NextResponse.json(
        { error: insertError.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      profile: newProfile
    })
  } catch (error) {
    console.error('Unexpected error in create-user-profile API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
