import type { Product, Category, CartItem } from "./types"
import { createClient } from "@/utils/supabase/client"
import { devLog, devInfo, logError } from "@/utils/logger"

export async function addToCart(userId: string, productId: string, quantity: number) {
  const supabase = await createClient()

  // Check if the item is already in the cart
  const { data: existingItem, error: existingError } = await supabase
    .from("cart_items")
    .select("*")
    .eq("user_id", userId)
    .eq("product_id", productId)
    .single()

  if (existingError && existingError.code !== "PGRST116") {
    console.error("Error checking existing cart item:", existingError)
    return false
  }

  if (existingItem) {
    // Update the quantity if the item is already in the cart
    const { error: updateError } = await supabase
      .from("cart_items")
      .update({ quantity: existingItem.quantity + quantity })
      .eq("id", existingItem.id)

    if (updateError) {
      console.error("Error updating cart item:", updateError)
      return false
    }
  } else {
    // Add the item to the cart if it's not already there
    const { error: insertError } = await supabase.from("cart_items").insert({
      user_id: userId,
      product_id: productId,
      quantity: quantity,
    })

    if (insertError) {
      console.error("Error adding to cart:", insertError)
      return false
    }
  }

  return true
}

// Simple in-memory cache for cart items
const cartCache: Record<string, { data: CartItem[], timestamp: number }> = {}

export async function getCartItems(userId: string, cacheKey?: string): Promise<CartItem[]> {
  // If running in client, delegate to API to avoid server-only cookies
  if (typeof window !== 'undefined') {
    try {
      // Check cache first (valid for 60 seconds)
      const cacheId = `cart_${userId}`;
      const cachedData = cartCache[cacheId];
      const now = Date.now();

      // Use cached data if it's recent enough (increased to 2 minutes for better performance)
      if (cachedData && (now - cachedData.timestamp < 120000)) {
        // Silently use cached data without logging in production
        if (process.env.NODE_ENV === 'development') {
          console.info('Using recent cached cart data (client-side)');
        }
        return cachedData.data;
      }

      // Use a timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        if (process.env.NODE_ENV === 'development') {
          console.info('Client-side cart fetch taking longer than expected, aborting request');
        }
        controller.abort();
      }, 5000); // Reduced to 5 seconds for faster fallback to cache

      // Optimized fetch with priority hints
      let retries = 0;
      const maxRetries = 1; // Reduced retries for faster fallback to cache
      let res;

      while (retries <= maxRetries) {
        try {
          res = await fetch(`/api/cart?userId=${userId}&t=${now}`, {
            signal: controller.signal,
            headers: {
              'Cache-Control': 'no-cache',
              'X-Priority': 'high'
            },
            // Add priority hint for modern browsers
            priority: 'high' as any
          });

          // If successful, break out of the retry loop
          break;
        } catch (fetchError) {
          retries++;

          if (process.env.NODE_ENV === 'development') {
            console.warn(`Fetch attempt ${retries} failed:`, fetchError);
          }

          if (retries > maxRetries) {
            // If we have cached data, use it instead of retrying further
            if (cachedData) {
              clearTimeout(timeoutId);
              return cachedData.data;
            }
            throw fetchError; // Re-throw if we've exhausted retries and have no cache
          }

          // Shorter wait before retrying
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      // If we still don't have a response after retries, use cache or mock data
      if (!res) {
        clearTimeout(timeoutId);
        if (cachedData) {
          return cachedData.data;
        }
        // Return empty array
        return [];
      }

      clearTimeout(timeoutId);

      if (!res.ok) {
        // Use cached data if available
        if (cachedData) {
          return cachedData.data;
        }
        return [];
      }

      const data = await res.json();

      // Update client-side cache
      cartCache[cacheId] = {
        data: data as CartItem[],
        timestamp: now
      };

      return data as CartItem[];
    } catch (err) {
      // Check for client-side cache before returning empty array
      const cacheId = `cart_${userId}`;
      const cachedData = cartCache[cacheId];
      if (cachedData) {
        return cachedData.data;
      }

      // Return empty array if no cached data
      return [];
    }
  }

  // Check cache first (increased to 2 minutes for better performance)
  const cacheId = cacheKey || `cart_${userId}`
  const cachedData = cartCache[cacheId]
  const now = Date.now()

  if (cachedData && (now - cachedData.timestamp < 120000)) {
    // Silently use cached data without logging in production
    if (process.env.NODE_ENV === 'development') {
      console.info('Using cached cart data (server-side)')
    }
    return cachedData.data
  }

  const supabase = await createClient()

  try {
    // Optimize the query by selecting only necessary fields from products
    // This reduces the amount of data transferred and improves performance
    const { data, error } = await supabase
      .from("cart_items")
      .select(`
        id,
        user_id,
        product_id,
        quantity,
        created_at,
        product:products(id, name, price, image_url, is_available, stock_quantity)
      `)
      .eq("user_id", userId)
      // Add order by to ensure consistent results
      .order('created_at', { ascending: false })

    if (error) {
      // Silently use cached data without excessive logging
      if (process.env.NODE_ENV === 'development') {
        console.error("Error fetching cart items:", error)
      }
      if (cachedData?.data) {
        return cachedData.data;
      }
      return []
    }

    // Update cache
    const typedData = data.map(item => ({
      ...item,
      product: item.product?.[0] || null
    })) as CartItem[]
    cartCache[cacheId] = {
      data: typedData,
      timestamp: now
    }

    return typedData
  } catch (err) {
    // Log only in development
    if (process.env.NODE_ENV === 'development') {
      console.error("Exception fetching cart items:", err)
    }

    // Return cached data if available
    if (cachedData?.data) {
      return cachedData.data;
    }

    // Return empty array if no cached data
    return [];
  }
}

export async function updateCartItemQuantity(itemId: string, quantity: number) {
  const supabase = await createClient()

  const { data, error } = await supabase
    .from("cart_items")
    .update({ quantity })
    .eq("id", itemId)
    .select()
    .single()

  if (error) {
    console.error("Error updating cart item quantity:", error)
    return null
  }

  return data as CartItem
}

export async function removeCartItem(itemId: string) {
  const supabase = await createClient()

  const { error } = await supabase
    .from("cart_items")
    .delete()
    .eq("id", itemId)

  if (error) {
    console.error("Error removing cart item:", error)
    return false
  }

  return true
}

export async function getCategories() {
  try {
    // Use devLog instead of console.log
    devLog('Creating Supabase client for getCategories...');
    const supabase = await createClient();
    devLog('Supabase client created, fetching categories...');

    // Simple approach without Promise.race to avoid build issues
    const { data, error } = await supabase.from("categories").select("*").order("name");

    if (error) {
      console.error("Error fetching categories:", error);
      return [];
    }

    console.log(`Successfully fetched ${data?.length || 0} categories`);
    return data as Category[];
  } catch (err) {
    console.error('Unexpected error in getCategories:', err);
    return [];
  }
}

export async function searchProducts(query: string) {
  const supabase = await createClient()

  const { data, error } = await supabase.from("products").select("*").ilike("name", `%${query}%`).order("name")

  if (error) {
    console.error("Error searching products:", error)
    return []
  }

  return data as Product[]
}

export async function getProducts() {
  const supabase = await createClient()
  const { data, error } = await supabase.from("products").select("*").order("name")

  if (error) {
    console.error("Error fetching products:", error)
    return []
  }

  return data as Product[]
}

export async function getProductsByCategory(categoryName: string) {
  try {
    devLog(`Creating Supabase client for getProductsByCategory(${categoryName})...`);
    const supabase = await createClient();

    // Try to get the category ID
    devLog(`Fetching category ID for ${categoryName}...`);
    const { data: category, error: categoryError } = await supabase
      .from("categories")
      .select("id")
      .eq("name", categoryName)
      .single();

    if (categoryError || !category) {
      logError("Error fetching category:", categoryError);
      // If we can't find the category, try using mock data as fallback
      devLog(`Using mock data for category: ${categoryName}`);
      return getMockProductsForCategory(categoryName);
    }

    devLog(`Fetching products for category ID: ${category.id}...`);
    const { data, error } = await supabase.from("products").select("*").eq("category_id", category.id).order("name");

    if (error) {
      logError("Error fetching products by category:", error);
      // If we can't fetch products, try using mock data as fallback
      devLog(`Using mock data for category: ${categoryName}`);
      return getMockProductsForCategory(categoryName);
    }

    devLog(`Successfully fetched ${data.length} products for category: ${categoryName}`);
    return data as Product[];
  } catch (err) {
    logError("Exception fetching products by category:", err);
    // If there's an exception, try using mock data as fallback
    devLog(`Using mock data for category: ${categoryName}`);
    return getMockProductsForCategory(categoryName);
  }
}

// Helper function for when database is unavailable
function getMockProductsForCategory(categoryName: string): Product[] {
  console.error(`Database unavailable when fetching products for category: ${categoryName}`);
  return [];
}

interface FilterOptions {
  categoryId?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
}

export async function getFilteredProducts(filterOptions: FilterOptions) {
  const supabase = await createClient()
  let query = supabase.from("products").select("*").order("name")

  if (filterOptions.categoryId) {
    query = query.eq("category_id", filterOptions.categoryId)
  }

  if (filterOptions.minPrice) {
    query = query.gte("price", filterOptions.minPrice)
  }

  if (filterOptions.maxPrice) {
    query = query.lte("price", filterOptions.maxPrice)
  }

  if (filterOptions.inStock !== undefined) {
    query = query.eq("is_available", filterOptions.inStock)
  }

  const { data, error } = await query

  if (error) {
    console.error("Error fetching filtered products:", error)
    return []
  }

  return data as Product[]
}

export async function getMinMaxPrices() {
  const supabase = await createClient()
  try {
    // Fetch minimum price
    const { data: minRow, error: minError } = await supabase
      .from("products")
      .select("price")
      .order("price", { ascending: true })
      .limit(1)
      .single()
    if (minError || !minRow) {
      console.error("Error fetching minimum price:", minError)
      throw minError || new Error("No min price row")
    }

    // Fetch maximum price
    const { data: maxRow, error: maxError } = await supabase
      .from("products")
      .select("price")
      .order("price", { ascending: false })
      .limit(1)
      .single()
    if (maxError || !maxRow) {
      console.error("Error fetching maximum price:", maxError)
      throw maxError || new Error("No max price row")
    }

    return { min: minRow.price || 0, max: maxRow.price || 0 }
  } catch (error) {
    console.error("Error fetching min/max prices:", error)
    return { min: 0, max: 100 }
  }
}

export async function getOrderDetails(orderId: string) {


  // For real orders, fetch from the database
  try {
    const supabase = await createClient()

    const { data: order, error: orderError } = await supabase.from("orders").select("*").eq("id", orderId).single()

    if (orderError || !order) {
      console.error("Error fetching order:", orderError)
      return null
    }

    const { data: items, error: itemsError } = await supabase
      .from("order_items")
      .select(
        `
        *,
        product:products(*)
      `,
      )
      .eq("order_id", orderId)

    if (itemsError) {
      console.error("Error fetching order items:", itemsError)
      return null
    }

    return { ...order, items } as any
  } catch (error) {
    console.error("Exception fetching order details:", error)
    return null
  }
}


