import { NextRequest, NextResponse } from "next/server"
import { createClient } from '@/utils/supabase/client'

export async function POST(request: NextRequest) {
  try {
    // Get Supabase client
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: "Not authenticated" },
        { status: 401 }
      )
    }

    // Parse request body early to avoid waiting for DB operations
    let requestBody;
    try {
      requestBody = await request.json();
    } catch (e) {
      // Default values if parsing fails
      requestBody = {};
    }

    const { mobile, shop_name, address } = requestBody;

    // Use upsert instead of select-then-insert for better performance
    // This will create the profile if it doesn't exist, or do nothing if it does
    const { data: profile, error: upsertError } = await supabase
      .from("users")
      .upsert([
        {
          id: user.id,
          mobile: mobile || user.user_metadata?.mobile || "unknown",
          shop_name: shop_name || user.user_metadata?.shop_name || "",
          address: address || user.user_metadata?.address || "",
          is_admin: user.user_metadata?.is_admin === true,
          created_at: new Date().toISOString()
        }
      ],
      {
        onConflict: 'id',  // Only update if ID conflicts
        ignoreDuplicates: true  // Ignore if record already exists
      })
      .select()
      .single()

    if (upsertError) {
      console.error("Error upserting profile:", upsertError)
      return NextResponse.json(
        { success: false, error: "Failed to create/update profile" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      profile,
      created: true
    })
  } catch (error) {
    console.error("Error in check-profile API:", error)
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    )
  }
}
