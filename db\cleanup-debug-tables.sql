-- Script to clean up debug tables and data

-- Drop debug tables if they exist
DROP TABLE IF EXISTS debug_logs;
DROP TABLE IF EXISTS mock_users;
DROP TABLE IF EXISTS mock_orders;
DROP TABLE IF EXISTS mock_products;

-- Remove debug functions
DROP FUNCTION IF EXISTS debug_log(text);
DROP FUNCTION IF EXISTS debug_auth_check();

-- Clean up any test data in production tables
-- (Be careful with this in a real production environment)
-- DELETE FROM users WHERE email LIKE '%test%' OR email LIKE '%example%';
-- DELETE FROM orders WHERE created_at < '2023-01-01';

-- Vacuum the database to reclaim space
VACUUM FULL;
