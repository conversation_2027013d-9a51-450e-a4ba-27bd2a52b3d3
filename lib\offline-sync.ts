// "use client"

// import { storeOfflineData, getOfflineData, requestBackgroundSync } from "./service-worker"

// // Types for offline data
// export interface OfflineOrder {
//   id: string
//   items: OfflineCartItem[]
//   totalAmount: number
//   deliveryAddress: string
//   createdAt: string
//   synced: boolean
// }

// export interface OfflineCartItem {
//   productId: string
//   quantity: number
//   price: number
//   name: string
// }

// /**
//  * Save an order for offline processing
//  */
// export async function saveOfflineOrder(order: Omit<OfflineOrder, "id" | "synced" | "createdAt">): Promise<string> {
//   // Generate a unique ID for the order
//   const orderId = `offline_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

//   // Get existing offline orders
//   const existingOrders = getOfflineOrders()

//   // Create the new order with metadata
//   const newOrder: OfflineOrder = {
//     ...order,
//     id: orderId,
//     createdAt: new Date().toISOString(),
//     synced: false
//   }

//   // Add the new order to the list
//   const updatedOrders = [...existingOrders, newOrder]

//   // Save the updated list
//   await storeOfflineData('offlineOrders', updatedOrders)

//   // Request background sync if available
//   requestBackgroundSync('sync-orders').catch(() => {})

//   return orderId
// }

// /**
//  * Get all offline orders
//  */
// export function getOfflineOrders(): OfflineOrder[] {
//   return getOfflineData<OfflineOrder[]>('offlineOrders') || []
// }

// /**
//  * Get a specific offline order by ID
//  */
// export function getOfflineOrder(orderId: string): OfflineOrder | null {
//   const orders = getOfflineOrders()
//   return orders.find(order => order.id === orderId) || null
// }

// /**
//  * Mark an offline order as synced
//  */
// export async function markOrderAsSynced(orderId: string): Promise<boolean> {
//   const orders = getOfflineOrders()
//   const updatedOrders = orders.map(order =>
//     order.id === orderId ? { ...order, synced: true } : order
//   )

//   return await storeOfflineData('offlineOrders', updatedOrders)
// }

// /**
//  * Remove an offline order
//  */
// export async function removeOfflineOrder(orderId: string): Promise<boolean> {
//   const orders = getOfflineOrders()
//   const updatedOrders = orders.filter(order => order.id !== orderId)

//   return await storeOfflineData('offlineOrders', updatedOrders)
// }

// /**
//  * Save cart items for offline use
//  */
// export async function saveOfflineCart(items: OfflineCartItem[]): Promise<boolean> {
//   return await storeOfflineData('offlineCart', items)
// }

// /**
//  * Get offline cart items
//  */
// export function getOfflineCart(): OfflineCartItem[] {
//   return getOfflineData<OfflineCartItem[]>('offlineCart') || []
// }

// /**
//  * Clear offline cart
//  */
// export async function clearOfflineCart(): Promise<boolean> {
//   return await storeOfflineData('offlineCart', [])
// }

// /**
//  * Sync all offline orders
//  */
// export async function syncOfflineOrders(): Promise<{
//   success: boolean
//   synced: number
//   failed: number
// }> {
//   const orders = getOfflineOrders().filter(order => !order.synced)

//   if (orders.length === 0) {
//     return { success: true, synced: 0, failed: 0 }
//   }

//   let synced = 0
//   let failed = 0

//   for (const order of orders) {
//     try {
//       // Try to send the order to the server
//       const response = await fetch('/api/orders', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           items: order.items.map(item => ({
//             product_id: item.productId,
//             quantity: item.quantity
//           })),
//           delivery_address: order.deliveryAddress,
//           payment_method: 'COD', // Cash on delivery for offline orders
//           offline_id: order.id
//         }),
//       })

//       if (response.ok) {
//         // If successful, mark as synced
//         await markOrderAsSynced(order.id)
//         synced++
//       } else {
//         failed++
//       }
//     } catch (error) {
//       // Failed to sync offline order
//       failed++
//     }
//   }

//   return {
//     success: failed === 0,
//     synced,
//     failed
//   }
// }
