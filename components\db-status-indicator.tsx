"use client"

import { useEffect, useState } from "react"
import { useRealTimeData } from "@/contexts/real-time-context"
import { Database, AlertCircle, RefreshCw } from "lucide-react"

export function DatabaseStatusIndicator() {
  const { isMockData, error, fetchData, loading } = useRealTimeData()
  const [isVisible, setIsVisible] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)

  useEffect(() => {
    // Only show the indicator if we have an error
    if (error) {
      setIsVisible(true)
    } else {
      // Hide after a delay when the error is resolved
      const timer = setTimeout(() => {
        setIsVisible(false)
      }, 3000)

      return () => clearTimeout(timer)
    }
  }, [error])

  if (!isVisible) return null

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true)
      await fetchData()
      // If successful, show a success message briefly
      if (!error) {
        setTimeout(() => {
          setIsVisible(false)
        }, 2000)
      }
    } catch (error) {
      console.error("Error refreshing data:", error)
    } finally {
      setIsRefreshing(false)
    }
  }

  return (
    <div className={`fixed bottom-16 left-0 right-0 z-50 flex justify-center transition-opacity duration-300 ${isVisible ? 'opacity-100' : 'opacity-0'}`}>
      <div className="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full shadow-lg flex items-center gap-2 max-w-xs mx-auto">
        {error ? (
          <>
            <AlertCircle size={16} className="flex-shrink-0" />
            <span className="text-sm truncate">{error}</span>
          </>
        ) : null}

        <button
          onClick={handleRefresh}
          disabled={isRefreshing || loading}
          className={`ml-2 p-1 rounded-full ${isRefreshing || loading ? 'bg-yellow-200 cursor-not-allowed' : 'hover:bg-yellow-200 cursor-pointer'} transition-colors`}
          aria-label="Refresh data"
        >
          <RefreshCw size={14} className={isRefreshing ? 'animate-spin' : ''} />
        </button>
      </div>
    </div>
  )
}
