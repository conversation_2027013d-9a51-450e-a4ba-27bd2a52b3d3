"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { Shield, X, ArrowLeft } from "lucide-react"

// This is a hidden page that provides access to the admin portal
// It requires a PIN code to access
const ADMIN_PIN = "1234" // Change this to a secure PIN

export default function AdminAccessPage() {
  const { user, isAdmin } = useAuth()
  const router = useRouter()
  const [pin, setPin] = useState("")
  const [error, setError] = useState("")
  const [attempts, setAttempts] = useState(0)
  const [locked, setLocked] = useState(false)
  
  // Check if user is already an admin
  useEffect(() => {
    if (user && isAdmin) {
      router.push("/admin/dashboard")
    }
  }, [user, isAdmin, router])
  
  // Lock access after 3 failed attempts
  useEffect(() => {
    if (attempts >= 3) {
      setLocked(true)
      const timer = setTimeout(() => {
        setLocked(false)
        setAttempts(0)
      }, 30000) // Lock for 30 seconds
      
      return () => clearTimeout(timer)
    }
  }, [attempts])
  
  const handleDigitPress = (digit: string) => {
    if (locked) return
    if (pin.length < 4) {
      setPin(prev => prev + digit)
    }
  }
  
  const handleClear = () => {
    setPin("")
    setError("")
  }
  
  const handleDelete = () => {
    setPin(prev => prev.slice(0, -1))
    setError("")
  }
  
  const handleSubmit = () => {
    if (locked) return
    
    if (pin === ADMIN_PIN) {
      // Correct PIN, redirect to admin login
      router.push("/admin/login")
    } else {
      // Incorrect PIN
      setError("Incorrect PIN")
      setAttempts(prev => prev + 1)
      setPin("")
    }
  }
  
  return (
    <>
      <MainHeader />
      <div className="p-4 flex-grow flex flex-col items-center justify-center">
        <div className="w-full max-w-xs">
          <div className="flex items-center justify-between mb-6">
            <button 
              onClick={() => router.push("/")}
              className="text-gray-600 hover:text-gray-800 flex items-center"
            >
              <ArrowLeft size={18} className="mr-1" />
              Back
            </button>
            <h1 className="text-xl font-bold flex items-center">
              <Shield className="mr-2 text-blue-600" size={20} />
              Access Control
            </h1>
          </div>
          
          {locked ? (
            <div className="bg-red-100 text-red-700 p-4 rounded-md mb-4 text-center">
              <p className="font-bold">Access Locked</p>
              <p className="text-sm mt-1">Too many failed attempts. Please try again later.</p>
            </div>
          ) : (
            <>
              {error && (
                <div className="bg-red-100 text-red-700 p-2 rounded-md mb-4 text-center text-sm">
                  {error}
                </div>
              )}
              
              <div className="bg-gray-100 p-3 rounded-md mb-6 text-center">
                <div className="flex justify-center space-x-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div 
                      key={i} 
                      className="w-3 h-3 rounded-full bg-gray-300"
                      style={{ backgroundColor: i < pin.length ? '#2563eb' : '#d1d5db' }}
                    />
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">Enter PIN to access admin portal</p>
              </div>
              
              <div className="grid grid-cols-3 gap-3">
                {[1, 2, 3, 4, 5, 6, 7, 8, 9].map(digit => (
                  <button
                    key={digit}
                    onClick={() => handleDigitPress(digit.toString())}
                    className="bg-white border border-gray-300 rounded-md p-4 text-xl font-medium hover:bg-gray-50 transition-colors"
                  >
                    {digit}
                  </button>
                ))}
                <button
                  onClick={handleClear}
                  className="bg-gray-200 border border-gray-300 rounded-md p-4 text-sm font-medium hover:bg-gray-300 transition-colors"
                >
                  Clear
                </button>
                <button
                  onClick={() => handleDigitPress("0")}
                  className="bg-white border border-gray-300 rounded-md p-4 text-xl font-medium hover:bg-gray-50 transition-colors"
                >
                  0
                </button>
                <button
                  onClick={handleDelete}
                  className="bg-gray-200 border border-gray-300 rounded-md p-4 flex items-center justify-center hover:bg-gray-300 transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
              
              <button
                onClick={handleSubmit}
                disabled={pin.length !== 4}
                className="w-full mt-6 bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400"
              >
                Submit
              </button>
            </>
          )}
        </div>
      </div>
      <MainFooter />
    </>
  )
}
