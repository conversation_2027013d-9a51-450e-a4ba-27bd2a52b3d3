"use client"

import { useState, useEffect } from "react"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { getUserOrders } from "@/lib/services/order-service"
import { getOrderById } from "@/lib/services/order-service"
import { Package, ShoppingBag, Truck, AlertCircle, Clock, CheckCircle } from "lucide-react"

export default function OrdersPage() {
  const { user } = useAuth()
  const [orders, setOrders] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [orderItemCounts, setOrderItemCounts] = useState<Record<string, number>>({})

  const formatOrderNumber = (orderId: string) => {
    // Extract the first 6 characters from the UUID and convert to a number
    const numericPart = parseInt(orderId.replace(/-/g, '').substring(0, 6), 16);
    // Format as a 6-digit number with # prefix
    return `#${(numericPart % 1000000).toString().padStart(6, '0')}`;
  }

  useEffect(() => {
    async function loadOrders() {
      if (!user) {
        setLoading(false)
        return
      }

      try {
        const userOrders = await getUserOrders(user.id)
        setOrders(userOrders)

        // Get item counts for each order
        const counts: Record<string, number> = {}
        for (const order of userOrders) {
          // Get the count from order items if available, otherwise default to 0
          const count = order.items?.length || 0
          counts[order.id] = count
        }
        setOrderItemCounts(counts)
      } catch (error) {
        console.error("Error loading orders:", error)
      } finally {
        setLoading(false)
      }
    }

    loadOrders()
  }, [user])
  return (
    <>
      <MainHeader />
      <div className="p-4 flex-grow overflow-y-auto">
        <h1 className="text-2xl font-bold mb-4 flex items-center gap-2">
          <Package className="text-blue-600" />
          Your Orders
        </h1>

        {loading ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <p className="mt-4 text-gray-500">Loading your orders...</p>
          </div>
        ) : orders.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">You haven't placed any orders yet</p>
            <Link
              href="/products"
              className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Browse Products
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <div key={order.id} className="border border-gray-200 rounded-md overflow-hidden">
                <div className="bg-gray-50 p-3 border-b border-gray-200 flex justify-between items-center">
                  <div>
                    <span className="font-medium">Order {formatOrderNumber(order.id)}</span>
                    <p className="text-sm text-gray-500">
                      {new Date(order.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <span
                      className={`px-2 py-1 rounded-full text-xs flex items-center gap-1 ${
                        // Completed order (delivered and paid)
                        order.status === "Delivered" && order.payment_status === "Paid"
                          ? "bg-green-100 text-green-800"
                        // Delivered but payment pending (for COD)
                        : order.status === "Delivered" && order.payment_status === "Pending"
                          ? "bg-orange-100 text-orange-800"
                        // In preparation (processing and paid)
                        : order.status === "Processing" && order.payment_status === "Paid"
                          ? "bg-blue-100 text-blue-800"
                        // COD orders in preparation
                        : order.status === "Processing" && order.payment_status === "Pending" && order.payment_method === "COD"
                          ? "bg-blue-100 text-blue-800"
                        // Awaiting payment (processing but payment pending)
                        : order.status === "Processing" && order.payment_status === "Pending"
                          ? "bg-yellow-100 text-yellow-800"
                        // Any other status (cancelled, etc.)
                        : "bg-red-100 text-red-800"
                      }`}
                    >
                      {/* Icon selection based on status */}
                      {order.status === "Delivered" && order.payment_status === "Paid" ? (
                        <CheckCircle size={12} />
                      ) : order.status === "Delivered" && order.payment_status === "Pending" ? (
                        <Truck size={12} />
                      ) : order.status === "Processing" && (order.payment_status === "Paid" || order.payment_method === "COD") ? (
                        <Package size={12} />
                      ) : order.status === "Processing" && order.payment_status === "Pending" ? (
                        <Clock size={12} />
                      ) : (
                        <AlertCircle size={12} />
                      )}
                      
                      {/* Display text based on status */}
                      {order.status === "Delivered" && order.payment_status === "Paid"
                        ? "Completed"
                        : order.status === "Delivered" && order.payment_status === "Pending"
                          ? "Delivered - Payment Due"
                        : order.status === "Processing" && order.payment_status === "Paid"
                          ? "Preparing"
                        : order.status === "Processing" && order.payment_status === "Pending" && order.payment_method === "COD"
                          ? "Preparing"
                        : order.status === "Processing" && order.payment_status === "Pending"
                          ? "Awaiting Payment"
                          : order.status}
                    </span>
                  </div>
                </div>
                <div className="p-3">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Items:</span>
                    <span>{orderItemCounts[order.id] || 0}</span>
                  </div>
                  <div className="flex justify-between font-medium">
                    <span>Total:</span>
                    <span>R {order.total_amount ? order.total_amount.toFixed(2) : '0.00'}</span>
                  </div>
                  <Link
                    href={`/orders/${order.id}`}
                    className="block w-full text-center mt-3 text-blue-600 border border-blue-600 rounded-md py-2 hover:bg-blue-50 transition-colors flex items-center justify-center gap-2"
                  >
                    <Package size={16} />
                    View Details
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      <MainFooter />
    </>
  )
}








