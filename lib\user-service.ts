"use server"

import { createClient } from '@/utils/supabase/client'
import { User } from './types/schema'

export interface UserProfileData {
  full_name?: string
  mobile?: string
}

export async function updateUserProfile(userId: string, profileData: UserProfileData): Promise<boolean> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from("users")
      .update({
        ...profileData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .select()
      .single()

    if (error) {
      console.error("Error updating user profile:", error)
      return false
    }

    if (!data) {
      console.error("No user data found after update")
      return false
    }

    return true
  } catch (error) {
    console.error("Exception updating user profile:", error)
    return false
  }
}

export async function getUserProfile(userId: string): Promise<User | null> {
  try {
    const supabase = await createClient()

    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single()

    if (error) {
      console.error("Error fetching user profile:", error)
      return null
    }

    if (!data) {
      console.error("No user data found")
      return null
    }

    return data as User
  } catch (error) {
    console.error("Exception fetching user profile:", error)
    return null
  }
}
