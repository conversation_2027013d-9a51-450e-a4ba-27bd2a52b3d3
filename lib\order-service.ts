// "use server"

// import { createClient } from '@/utils/supabase/client'
// import { sendOrderStatusNotification } from "./notification-service"
// import type { CartItem, Order, OrderItem, DeliveryOption } from "./types"
// import { getDeliveryOptionById } from "./delivery-options-service"
// import { v4 as uuidv4 } from 'uuid'



// export async function createOrder(
//   userId: string,
//   shopId: string,
//   cartItems: CartItem[],
//   deliveryAddress: string,
//   paymentMethod: "COD" | "Shop2Shop" = "COD",
//   estimatedDelivery?: Date,
//   useDemoMode: boolean = false,
//   deliveryOptionId?: string,
//   deliveryFee: number = 50
// ) {
//   // We no longer support demo mode
//   if (useDemoMode) {
//     console.error("Demo mode is no longer supported");
//     throw new Error("Failed to create order. Please try again later.");
//   }

//   try {
//     console.log("Creating order for user:", userId, "with payment method:", paymentMethod);

//     // Create Supabase client with error handling
//     let supabase;
//     try {
//       supabase = await createClient();
//       console.log("Supabase client created successfully");
//     } catch (supabaseError) {
//       console.error("Failed to create Supabase client:", supabaseError);

//       // Throw an error when database connection fails
//       console.error("Database connection failed");
//       throw new Error("Failed to connect to the database. Please try again later.");
//     }

//     // Validate cart items
//     if (!cartItems || cartItems.length === 0) {
//       console.error("No cart items provided");
//       throw new Error("Your cart is empty. Please add items before checkout.");
//     }

//     // Calculate total amount
//     console.log("Calculating order subtotal for", cartItems.length, "items");
//     let subtotal = 0;
//     let validItemCount = 0;

//     for (const item of cartItems) {
//       if (!item.product_id) {
//         console.error("Cart item missing product_id", item);
//         continue;
//       }

//       try {
//         // If the item has a product object with price, use that
//         if (item.product && typeof item.product.price === 'number') {
//           const itemTotal = item.product.price * item.quantity;
//           console.log(`Adding item ${item.product.name || item.product_id}: ${item.quantity} x ${item.product.price} = ${itemTotal}`);
//           subtotal += itemTotal;
//           validItemCount++;
//         } else {
//           // Otherwise fetch the product price from the database
//           console.log(`Fetching price for product ${item.product_id} from database`);
//           const { data: product, error } = await supabase.from("products").select("price, name").eq("id", item.product_id).single();

//           if (error) {
//             console.error(`Error fetching product ${item.product_id}:`, error);
//             continue;
//           }

//           if (product) {
//             const itemTotal = product.price * item.quantity;
//             console.log(`Adding item ${product.name || item.product_id}: ${item.quantity} x ${product.price} = ${itemTotal}`);
//             subtotal += itemTotal;
//             validItemCount++;
//           } else {
//             console.error(`Product not found for id: ${item.product_id}`);
//           }
//         }
//       } catch (itemError) {
//         console.error(`Error processing cart item ${item.product_id}:`, itemError);
//       }
//     }

//     if (validItemCount === 0) {
//       console.error("No valid items found in cart");
//       throw new Error("No valid items found in your cart. Please try again.");
//     }

//     if (subtotal <= 0) {
//       console.error("Calculated subtotal is zero or negative", subtotal);
//       throw new Error("Order total calculation failed. Please try again.");
//     }

//     console.log(`Order subtotal calculated: ${subtotal} for ${validItemCount} valid items`);

//     // Get delivery option details if provided
//     let deliveryOptionDetails: DeliveryOption | null = null;
//     if (deliveryOptionId) {
//       try {
//         deliveryOptionDetails = await getDeliveryOptionById(deliveryOptionId);
//         console.log(`Using delivery option: ${deliveryOptionDetails?.name || 'Unknown'} with fee: ${deliveryFee}`);
//       } catch (error) {
//         console.error("Error fetching delivery option:", error);
//         // Continue with provided delivery fee
//       }
//     }

//     // Use the provided delivery fee (from checkout page)
//     const totalAmount = subtotal + deliveryFee;

//     console.log(`Order total amount: ${totalAmount} (subtotal: ${subtotal} + delivery fee: ${deliveryFee})`);

//     // Verify user exists or create if missing
//     console.log(`Verifying user exists: ${userId}`);
//     try {
//       const { data: userExists, error: userError } = await supabase
//         .from("users")
//         .select("id")
//         .eq("id", userId)
//         .single();

//       if (userError) {
//         console.error("Error checking user:", userError);
//         if (userError.code === 'PGRST116') {
//           // This is "No rows returned" error, which means user doesn't exist in the users table
//           // Try to get user from auth and create a profile
//           console.log("User not found in users table, attempting to create profile");

//           // Get user from auth
//           const { data: authUser, error: authError } = await supabase.auth.getUser();

//           if (authError || !authUser.user) {
//             console.error("Error getting auth user:", authError);
//             throw new Error("User authentication failed. Please log in again.");
//           }

//           // Create user profile
//           const { error: insertError } = await supabase.from("users").insert({
//             id: userId,
//             mobile: authUser.user.phone || authUser.user.user_metadata?.mobile || 'unknown',
//             full_name: authUser.user.user_metadata?.full_name || '',
//             created_at: new Date().toISOString()
//           });

//           if (insertError) {
//             console.error("Error creating user profile:", insertError);
//             // Instead of failing, we'll create a minimal profile with just the ID
//             // This allows the order to proceed even if full profile creation fails
//             console.log("Attempting to create minimal user profile");

//             const { error: minimalInsertError } = await supabase.from("users").insert({
//               id: userId,
//               mobile: 'unknown',
//               created_at: new Date().toISOString()
//             });

//             if (minimalInsertError) {
//               console.error("Error creating minimal user profile:", minimalInsertError);
//               throw new Error("Failed to create user profile. Please try again.");
//             }

//             console.log("Minimal user profile created successfully");
//           } else {
//             console.log("User profile created successfully");
//           }
//         } else {
//           throw new Error("Error verifying user account. Please try again.");
//         }
//       }

//       console.log("User verified successfully");
//     } catch (userError) {
//       console.error("Exception verifying user:", userError);
//       throw userError;
//     }

//     // Prepare order data
//     console.log("Preparing order data");
//     const orderData = {
//       shop_id: shopId,
//       created_by_user_id: userId,
//       // Always start with 'Processing' status to satisfy DB constraints
//       status: "Processing",
//       total_amount: totalAmount,
//       delivery_address: deliveryAddress,
//       payment_method: paymentMethod,
//       // For COD, mark as Paid; otherwise start Pending
//       payment_status: paymentMethod === "COD" ? "Paid" : "Pending",
//       delivery_status: paymentMethod === "COD" ? "Preparing" : "Processing",
//       estimated_delivery: estimatedDelivery?.toISOString() || null,
//       delivery_option_id: deliveryOptionId || null,
//       delivery_fee: deliveryFee
//     };

//     console.log("Order data prepared:", JSON.stringify(orderData));

//     // Create order
//     console.log("Creating order in database");
//     let order;
//     try {
//       const { data, error: orderError } = await supabase
//         .from("orders")
//         .insert(orderData)
//         .select()
//         .single();

//       if (orderError) {
//         console.error("Error creating order:", orderError);
//         throw new Error("Failed to create order in database. Please try again.");
//       }

//       if (!data) {
//         console.error("Order created but no data returned");
//         throw new Error("Order was created but details could not be retrieved. Please check your orders page.");
//       }

//       order = data;
//       console.log("Order created successfully:", order.id);
//     } catch (orderCreationError) {
//       console.error("Exception creating order:", orderCreationError);
//       throw new Error("Failed to create order. Please try again.");
//     }

//     // Order created successfully

//     // Ensure payment record is created for every order
//     console.log("Creating payment record for order:", order.id);
//     try {
//       const { error: paymentError } = await supabase
//         .from("payments")
//         .insert({
//           order_id: order.id,
//           shop_id: shopId,
//           recorded_by_user_id: userId,
//           amount: totalAmount,
//           payment_method: paymentMethod,
//           status: paymentMethod === "COD" ? "Pending" : "Pending", // Adjust based on your business logic
//           transaction_id: `ORD-${order.id.substring(0, 8)}`
//         });

//       if (paymentError) {
//         console.error("Error creating payment record:", paymentError);
//         // Continue with order creation even if payment record fails
//       } else {
//         console.log("Payment record created successfully");
//       }
//     } catch (paymentError) {
//       console.error("Exception creating payment record:", paymentError);
//       // Continue with order creation even if payment record fails
//     }

//     // Create order items
//     console.log("Creating order items for order:", order.id);
//     if (cartItems.length === 0) {
//       console.error("No cart items to add to order");
//       // This shouldn't happen as we've already validated cart items
//       return order; // Return the order even without items
//     }

//     try {
//       // Map cart items to order items
//       const orderItems = cartItems.map((item) => {
//         // Get the price from the product object or use 0 as fallback
//         const price = item.product?.price || 0;
//         const productName = item.product?.name || 'Unknown Product';

//         console.log(`Adding order item: ${productName}, quantity: ${item.quantity}, price: ${price}`);

//         return {
//           order_id: order.id,
//           product_id: item.product_id,
//           quantity: item.quantity,
//           price_per_unit: price,
//         };
//       }).filter(item => item.product_id && item.price_per_unit > 0);

//       if (orderItems.length === 0) {
//         console.error("No valid order items to add");
//         // We'll still return the order even if there are no valid items
//         return order;
//       }

//       console.log(`Adding ${orderItems.length} items to order ${order.id}`);

//       // Add items to the order
//       const { error: itemsError } = await supabase.from("order_items").insert(orderItems);

//       if (itemsError) {
//         console.error("Error creating order items:", itemsError);
//         // We'll still return the order even if items failed
//         return order;
//       }

//       console.log("Order items created successfully");
//     } catch (itemError) {
//       console.error("Exception creating order items:", itemError);
//       // We'll still return the order even if items failed
//       return order;
//     }

//     // Clear cart
//     console.log("Clearing cart for user:", userId);
//     try {
//       const { error: clearCartError } = await supabase.from("cart_items").delete().eq("user_id", userId);

//       if (clearCartError) {
//         console.error("Error clearing cart:", clearCartError);
//         // Continue anyway - this shouldn't prevent order creation
//       } else {
//         console.log("Cart cleared successfully");
//       }
//     } catch (clearError) {
//       console.error("Exception clearing cart:", clearError);
//       // Continue anyway - this shouldn't prevent order creation
//     }

//     // Send notification
//     console.log("Sending order notification for order:", order.id);
//     try {
//       const notification = await sendOrderStatusNotification(order.id, "Processing");
//       if (notification) {
//         console.log("Notification sent successfully");
//       } else {
//         console.log("Notification not sent, but continuing with order creation");
//       }
//     } catch (notifyError) {
//       console.error("Error sending notification:", notifyError);
//       // Continue anyway - this shouldn't prevent order creation
//     }

//     // Successfully completed order creation
//     console.log("Order creation completed successfully:", order.id);
//     return order;
//   } catch (error) {
//     console.error("Exception in createOrder function:", error);
//     // Rethrow the error to be handled by the caller
//     throw error;
//   }
// }

// export async function updateOrderStatus(
//   orderId: string,
//   status: "Processing" | "Delivered" | "Cancelled",
//   sendNotification = true,
// ) {
//   const supabase = await createClient();
//   const { data, error } = await supabase.from("orders").update({ status }).eq("id", orderId).select().single();

//   if (error) {
//     console.error("Error updating order status:", error);
//     return null;
//   }

//   if (sendNotification) {
//     await sendOrderStatusNotification(orderId, status);
//   }

//   return data as Order;
// }

// export async function updatePaymentStatus(orderId: string, paymentStatus: "Pending" | "Paid" | "Failed") {
//   const supabase = await createClient();
//   const { data, error } = await supabase
//     .from("orders")
//     .update({ payment_status: paymentStatus })
//     .eq("id", orderId)
//     .select()
//     .single();

//   if (error) {
//     console.error("Error updating payment status:", error);
//     return null;
//   }

//   // If payment is successful and was COD, send notification
//   if (paymentStatus === "Paid" && data.payment_method === "COD") {
//     await sendOrderStatusNotification(orderId, "Payment received");
//   }

//   return data as Order;
// }

// export async function getOrdersByStatus(shopId: string, status: string) {
//   const supabase = await createClient();
//   const { data, error } = await supabase
//     .from("orders")
//     .select(`
//       *,
//       created_by_user:users(*)
//     `)
//     .eq("shop_id", shopId)
//     .eq("status", status)
//     .order("created_at", { ascending: false });

//   if (error) {
//     console.error("Error fetching orders by status:", error);
//     return [];
//   }

//   return data;
// }

// export async function getOrdersForWholesalerProcurement(shopId: string) {
//   const supabase = await createClient();

//   // Get all processing orders for this shop
//   const { data: orders, error: ordersError } = await supabase
//     .from("orders")
//     .select("id")
//     .eq("shop_id", shopId)
//     .eq("status", "Processing");

//   if (ordersError || !orders || orders.length === 0) {
//     return [];
//   }

//   // Get all order items for these orders
//   const orderIds = orders.map((order) => order.id);
//   const { data: orderItems, error: itemsError } = await supabase
//     .from("order_items")
//     .select(`
//       product_id,
//       quantity,
//       product:products(name, category_id)
//     `)
//     .in("order_id", orderIds);

//   if (itemsError || !orderItems) {
//     return [];
//   }

//   // Aggregate quantities by product
//   const productMap: Record<
//     string,
//     {
//       product_id: string
//       name: string
//       category_id: string
//       total_quantity: number
//     }
//   > = {};

//   orderItems.forEach((item) => {
//     if (!productMap[item.product_id]) {
//       // Check if product data exists
//       if (item.product && typeof item.product === 'object' && 'name' in item.product) {
//         productMap[item.product_id] = {
//           product_id: item.product_id,
//           name: (item.product as any).name || 'Unknown Product',
//           category_id: (item.product as any).category_id || 'unknown',
//           total_quantity: 0,
//         };
//       } else {
//         // Fallback if product data is missing
//         productMap[item.product_id] = {
//           product_id: item.product_id,
//           name: 'Unknown Product',
//           category_id: 'unknown',
//           total_quantity: 0,
//         };
//       }
//     }
//     productMap[item.product_id].total_quantity += item.quantity;
//   });

//   // Convert to array and sort by category and name
//   const procurementList = Object.values(productMap);

//   // Get categories for sorting
//   const { data: categories } = await supabase.from("categories").select("id, name");

//   const categoryMap: Record<string, string> = {};
//   if (categories) {
//     categories.forEach((cat) => {
//       categoryMap[cat.id] = cat.name;
//     });
//   }

//   // Sort by category name and then product name
//   procurementList.sort((a, b) => {
//     const catA = categoryMap[a.category_id] || "";
//     const catB = categoryMap[b.category_id] || "";
//     if (catA !== catB) return catA.localeCompare(catB);
//     return a.name.localeCompare(b.name);
//   });

//   return procurementList;
// }



