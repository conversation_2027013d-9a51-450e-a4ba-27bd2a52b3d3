import { useEffect } from 'react'
import { useRouter } from 'next/router'

export default function LegacyPage() {
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to the app directory route
    router.replace('/')
  }, [router])
  
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      flexDirection: 'column',
      gap: '1rem'
    }}>
      <h1>Redirecting...</h1>
      <p>Please wait while we redirect you to the new version of the app.</p>
    </div>
  )
}
