"use client"

import Link from "next/link"
import { Home, Box, User, Calendar, BarChart2, BellRing } from "lucide-react"

export function AdminSidebar() {
  const navItems = [
    { href: "/admin/orders", icon: Box, label: "Orders" },
    { href: "/admin/products", icon: Box, label: "Products" },
    { href: "/admin/users", icon: User, label: "Users" },
    { href: "/admin/analytics", icon: BarChart2, label: "Analytics" },
    { href: "/admin/notifications", icon: BellRing, label: "Notifications" },
    { href: "/admin/settings", icon: Calendar, label: "Settings" },
  ]

  return (
    <nav className="flex flex-col p-4 space-y-2 bg-gray-800 text-white h-full">
      {navItems.map((item) => (
        <Link key={item.href} href={item.href} className="flex items-center gap-2 p-2 hover:bg-gray-700 rounded">
          <item.icon size={18} />
          <span>{item.label}</span>
        </Link>
      ))}
    </nav>
  )
}
