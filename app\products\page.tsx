"use client"

import { ProductList } from "@/components/product-list"
import { SearchBar } from "@/components/search-bar"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { WhatsAppSupport } from "@/components/whatsapp-support"
import { FloatingCart } from "@/components/floating-cart"
import { CategoryNavigation } from "@/components/category-navigation"
import { QuickAccessFAB } from "@/components/quick-access-fab"
import { AuthCheck } from "@/components/auth-check"
// import { FreshLoginHandler } from "@/components/fresh-login-handler"
import { categoryIcons } from "@/lib/product-icons"
import { ProductCardSkeleton, CategorySkeleton } from "@/components/skeleton-loaders"
import { useRealTimeData } from "@/contexts/real-time-context"
import { useShop } from "@/contexts/shop-context"
import { useEffect, useState, useCallback, memo } from "react"
import { Loader2, RefreshCw, Store } from "lucide-react"

// Memoized category component to prevent unnecessary re-renders
const CategorySection = memo(({ category, onViewAll }: {
  category: any,
  onViewAll: (categoryId: string) => void
}) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between gap-2 font-bold text-gray-700 mt-4 mb-3 pb-1 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="bg-gray-100 p-2 rounded-md">
            {categoryIcons[category.name] || categoryIcons["Beverages"]}
          </div>
          <h2 className="text-lg">{category.name}</h2>
        </div>
        <button
          onClick={() => onViewAll(category.id)}
          className="text-sm font-normal text-blue-600"
        >
          View all
        </button>
      </div>
      {category.error ? (
        <p className="text-gray-500 text-center py-4">Unable to load products for this category</p>
      ) : (
        <ProductList products={category.products} />
      )}
    </div>
  )
});

export default function ProductsPage() {
  const {
    categoriesWithProducts,
    loading,
    error,
    isMockData,
    refreshData
  } = useRealTimeData()
  const { currentShop, loading: shopLoading } = useShop()

  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastRefreshTime, setLastRefreshTime] = useState(0)

  // Handle navigation to category filter page
  const handleViewAll = useCallback((categoryId: string) => {
    window.location.href = `/products/filter?category=${categoryId}`;
  }, []);

  // Handle manual refresh with rate limiting
  const handleRefresh = useCallback(async () => {
    const now = Date.now();
    // Prevent refreshing more than once every 3 seconds
    if (now - lastRefreshTime < 3000) return;

    setIsRefreshing(true);
    setLastRefreshTime(now);

    await refreshData();

    // Minimum refresh time for better UX
    setTimeout(() => setIsRefreshing(false), 500);
  }, [refreshData, lastRefreshTime]);

  // Auto-refresh on mount and when there's an error
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        refreshData();
      }, 5000); // Try again after 5 seconds if there's an error

      return () => clearTimeout(timer);
    }
  }, [error, refreshData]);

  // Check for redirect flags
  useEffect(() => {
    // Clear any redirect flags that might be lingering
    const redirectAfterReload = localStorage.getItem('redirectAfterReload');
    if (redirectAfterReload === '/products') {
      console.log('Successfully redirected to products page, clearing flag');
      localStorage.removeItem('redirectAfterReload');
    }

    const redirectingTo = sessionStorage.getItem('redirectingTo');
    if (redirectingTo === '/products') {
      console.log('Successfully completed redirection to products page, clearing flag');
      sessionStorage.removeItem('redirectingTo');
    }

    // Log that we've reached the products page
    console.log('Products page loaded successfully');
  }, []);

  return (
    <>
      <AuthCheck />
      <MainHeader />
      <SearchBar />

      {/* Category Navigation */}
      <CategoryNavigation categories={categoriesWithProducts} />

      <div className="p-4 pt-2 flex-grow overflow-y-auto">
        {error && (
          <div className="bg-red-100 text-red-800 p-3 rounded-md mb-4 text-sm">
            <p className="font-medium">⚠️ Connection Error</p>
            <p>{error}</p>
          </div>
        )}

        {/* Shop indicator and refresh button */}
        <div className="flex justify-between items-center mb-4">
          {currentShop ? (
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Store className="h-4 w-4" />
              <span>{currentShop.name}</span>
            </div>
          ) : (
            <div className="flex items-center gap-1 text-sm text-yellow-600">
              <Store className="h-4 w-4" />
              <span>No shop selected</span>
            </div>
          )}

          <button
            onClick={handleRefresh}
            disabled={loading || isRefreshing}
            className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400"
            aria-label={isRefreshing ? 'Refreshing products' : 'Refresh products'}
          >
            {isRefreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
          </button>
        </div>

        {!currentShop && !shopLoading ? (
          <div className="text-center py-8">
            <Store className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <p className="text-gray-700 font-medium mb-2">No shop selected</p>
            <p className="text-gray-500 mb-4">Please select a shop to view products.</p>
            <button
              onClick={() => window.location.href = '/test-auth'}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors inline-block"
            >
              Select Shop
            </button>
          </div>
        ) : loading ? (
          <div className="space-y-8">
            <div>
              <CategorySkeleton />
              <div className="grid grid-cols-2 gap-4">
                <ProductCardSkeleton />
                <ProductCardSkeleton />
                <ProductCardSkeleton />
                <ProductCardSkeleton />
              </div>
            </div>
            <div>
              <CategorySkeleton />
              <div className="grid grid-cols-2 gap-4">
                <ProductCardSkeleton />
                <ProductCardSkeleton />
              </div>
            </div>
          </div>
        ) : categoriesWithProducts.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No products available at the moment.</p>
            <button
              onClick={handleRefresh}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors inline-block"
            >
              Refresh
            </button>
          </div>
        ) : (
          <>
            {/* Breadcrumbs */}
            <nav className="flex items-center text-sm text-gray-500 mb-4">
              <span className="font-medium text-blue-600">Home</span>
              <span className="mx-2">›</span>
              <span>All Products</span>
            </nav>

            {/* Use memoized category components to prevent unnecessary re-renders */}
            {categoriesWithProducts.map((category) => (
              <CategorySection
                key={category.id}
                category={category}
                onViewAll={handleViewAll}
              />
            ))}
          </>
        )}

        <WhatsAppSupport />
      </div>

      {/* Quick Access FAB */}
      <QuickAccessFAB />

      <FloatingCart />
      <MainFooter />
    </>
  )
}
