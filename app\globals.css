@tailwind base;
@tailwind components;
@tailwind utilities;

@import '../components/mobile-touch-fixes.css';
@import '../components/dark-mode.css';

:root {
  --foreground-rgb: 0, 0, 0;
  --background-rgb: 240, 242, 245;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-in-out;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes scaleIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }

  /* Animation utilities for components */
  .animate-in {
    animation-duration: 0.3s;
    animation-timing-function: ease-out;
    animation-fill-mode: both;
  }

  .fade-in {
    animation-name: fadeIn;
  }

  .slide-in-from-top-2 {
    animation-name: slideInFromTop2;
  }

  .slide-in-from-top-5 {
    animation-name: slideInFromTop5;
  }

  .slide-in-from-right-5 {
    animation-name: slideInFromRight5;
  }

  .slide-in-from-bottom-5 {
    animation-name: slideInFromBottom5;
  }

  .slide-in-from-bottom-10 {
    animation-name: slideInFromBottom10;
  }

  .zoom-in-95 {
    animation-name: zoomIn95;
  }

  .duration-200 {
    animation-duration: 0.2s;
  }

  .duration-300 {
    animation-duration: 0.3s;
  }

  @keyframes slideInFromTop2 {
    from { transform: translateY(-0.5rem); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideInFromTop5 {
    from { transform: translateY(-1.25rem); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideInFromRight5 {
    from { transform: translateX(1.25rem); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  @keyframes slideInFromBottom5 {
    from { transform: translateY(1.25rem); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes slideInFromBottom10 {
    from { transform: translateY(2.5rem); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes zoomIn95 {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* High contrast mode styles */
  .high-contrast {
    --foreground: 0 0% 0%;
    --background: 0 0% 100%;
    --primary: 240 100% 40%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 0%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 90%;
    --muted-foreground: 0 0% 0%;
    --accent: 240 100% 40%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 100% 40%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 0%;
    --input: 0 0% 0%;
    --ring: 240 100% 40%;
  }

  .high-contrast.dark {
    --foreground: 0 0% 100%;
    --background: 0 0% 0%;
    --primary: 240 100% 60%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 100%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 100%;
    --accent: 240 100% 60%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 60%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 100%;
    --input: 0 0% 100%;
    --ring: 240 100% 60%;
  }

  /* Improved focus styles for accessibility */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Improved touch targets for mobile */
  @media (max-width: 640px) {
    button,
    [role="button"],
    a.btn,
    input[type="submit"],
    input[type="button"],
    input[type="reset"] {
      min-height: 44px;
      min-width: 44px;
    }
  }
}
