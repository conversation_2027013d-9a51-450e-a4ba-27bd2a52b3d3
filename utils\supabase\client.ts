import { createClient as createSupabaseClient, SupabaseClient } from '@supabase/supabase-js'

// Return singleton Supabase client to avoid multiple instances
let supabaseClient: SupabaseClient | null = null

export function createClient(): SupabaseClient {
  if (supabaseClient) return supabase<PERSON>lient

  // Log the environment for debugging
  console.log(`Creating Supabase client in ${process.env.NODE_ENV} mode`)

  // Check if the environment variables are set
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.error("Supabase environment variables are not set")
  }

  supabaseClient = createSupabaseClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        persistSession: true,
        storageKey: 'supabase-auth',
        autoRefreshToken: true,
        debug: process.env.NODE_ENV === 'development',
      },
      global: {
        headers: {
          'x-client-info': 'spaza-smart-order',
        },
      },
    }
  )

  return supabaseClient
}