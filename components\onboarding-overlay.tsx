"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { 
  X, ChevronRight, ChevronLeft, ShoppingCart, 
  Search, User, Filter, CheckCircle
} from "lucide-react"

export function OnboardingOverlay() {
  const [isVisible, setIsVisible] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const { user } = useAuth()
  const router = useRouter()
  
  useEffect(() => {
    // Check if this is the first time the user is visiting
    const hasSeenOnboarding = localStorage.getItem('hasSeenOnboarding')
    
    // Only show onboarding for logged in users who haven't seen it
    if (user && !hasSeenOnboarding) {
      // Delay showing the onboarding to allow the page to load first
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [user])
  
  const completeOnboarding = () => {
    localStorage.setItem('hasSeenOnboarding', 'true')
    setIsVisible(false)
  }
  
  const steps = [
    {
      title: "Welcome to Spaza Smart Order!",
      description: "Let's take a quick tour to help you get started.",
      image: <ShoppingCart size={64} className="text-blue-600" />,
    },
    {
      title: "Browse Products",
      description: "Explore our wide range of products organized by categories.",
      image: <Filter size={64} className="text-blue-600" />,
      position: "bottom-32 left-1/2 transform -translate-x-1/2",
    },
    {
      title: "Search for Products",
      description: "Quickly find what you need using the search bar.",
      image: <Search size={64} className="text-blue-600" />,
      position: "top-20 left-1/2 transform -translate-x-1/2",
    },
    {
      title: "Manage Your Cart",
      description: "Add products to your cart and easily adjust quantities.",
      image: <ShoppingCart size={64} className="text-blue-600" />,
      position: "bottom-20 right-4",
    },
    {
      title: "Your Profile",
      description: "View your orders and manage your account settings.",
      image: <User size={64} className="text-blue-600" />,
      position: "bottom-20 right-20",
    },
    {
      title: "You're All Set!",
      description: "Start shopping now and enjoy a seamless experience.",
      image: <CheckCircle size={64} className="text-green-600" />,
    },
  ]
  
  if (!isVisible) return null
  
  const currentStepData = steps[currentStep]
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm">
      {/* Spotlight for specific UI elements */}
      {currentStepData.position && (
        <div 
          className={`absolute ${currentStepData.position} w-20 h-20 rounded-full bg-white/20 animate-pulse`}
          style={{ boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.7)' }}
        />
      )}
      
      {/* Content card */}
      <div className="bg-white rounded-xl shadow-xl max-w-sm w-full mx-4 overflow-hidden animate-in zoom-in-95 duration-200">
        {/* Close button */}
        <button 
          onClick={completeOnboarding}
          className="absolute top-2 right-2 p-2 text-gray-500 hover:text-gray-700 z-10"
          aria-label="Close onboarding"
        >
          <X size={20} />
        </button>
        
        {/* Image/icon */}
        <div className="bg-blue-50 p-8 flex items-center justify-center">
          {currentStepData.image}
        </div>
        
        {/* Content */}
        <div className="p-6">
          <h2 className="text-xl font-bold mb-2">{currentStepData.title}</h2>
          <p className="text-gray-600 mb-6">{currentStepData.description}</p>
          
          {/* Navigation buttons */}
          <div className="flex justify-between items-center">
            {/* Step indicators */}
            <div className="flex gap-1">
              {steps.map((_, index) => (
                <div 
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentStep ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            
            <div className="flex gap-2">
              {currentStep > 0 && (
                <button
                  onClick={() => setCurrentStep(prev => prev - 1)}
                  className="px-4 py-2 border border-gray-300 rounded-full text-gray-700 flex items-center gap-1"
                >
                  <ChevronLeft size={16} />
                  Back
                </button>
              )}
              
              {currentStep < steps.length - 1 ? (
                <button
                  onClick={() => setCurrentStep(prev => prev + 1)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-full flex items-center gap-1"
                >
                  Next
                  <ChevronRight size={16} />
                </button>
              ) : (
                <button
                  onClick={completeOnboarding}
                  className="px-4 py-2 bg-green-600 text-white rounded-full flex items-center gap-1"
                >
                  Get Started
                  <ChevronRight size={16} />
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
