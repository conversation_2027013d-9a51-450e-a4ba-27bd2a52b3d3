"use client"

import { useState, useEffect, useMemo } from "react"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ProductIcon } from "@/lib/product-icons"
import { useToast } from "@/components/toast-notification"
import { CartItemSkeleton, CartSummarySkeleton } from "@/components/skeleton-loaders"
import {
  Plus, Minus, Trash2, ShoppingCart, CheckSquare, Square,
  Filter, ArrowUpDown, ChevronDown, ChevronUp, Package, Truck
} from "lucide-react"
import { useCart } from "@/contexts/cart-context"
import { getDeliveryOptions } from "@/lib/services/delivery-options-service"
import type { DeliveryOption } from "@/lib/types"

export default function CartPage() {
  const router = useRouter()
  const { cartItems, cartTotal, loading, error, errorComponent, updateCartItemQuantity, removeCartItem, refreshCart } = useCart()
  const { showToast } = useToast()
  const [highlightedItems, setHighlightedItems] = useState<Record<string, boolean>>({})
  const [highlightTotal, setHighlightTotal] = useState(false)
  const [selectedItems, setSelectedItems] = useState<Record<string, boolean>>({})
  const [sortOrder, setSortOrder] = useState<'price-asc' | 'price-desc' | 'name-asc' | 'name-desc' | null>(null)
  const [filterCategory, setFilterCategory] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState(false)
  const [deliveryOptions, setDeliveryOptions] = useState<DeliveryOption[]>([])
  const [selectedDeliveryOption, setSelectedDeliveryOption] = useState<string>("")
  const [loadingDeliveryOptions, setLoadingDeliveryOptions] = useState(true)

  // Initialize selected items
  useEffect(() => {
    if (cartItems.length > 0) {
      const initialSelected = cartItems.reduce((acc, item) => {
        acc[item.id] = true; // Select all by default
        return acc;
      }, {} as Record<string, boolean>);
      setSelectedItems(initialSelected);
    } else {
      setSelectedItems({});
    }
  }, [cartItems.length]);

  // Highlight the total when cart items change
  useEffect(() => {
    if (cartItems.length > 0) {
      setHighlightTotal(true)
      const timer = setTimeout(() => setHighlightTotal(false), 500)
      return () => clearTimeout(timer)
    }
  }, [cartItems])

  // Load delivery options
  useEffect(() => {
    async function loadDeliveryOptions() {
      try {
        setLoadingDeliveryOptions(true)
        const options = await getDeliveryOptions()
        setDeliveryOptions(options)

        // Set the first option as default if we have options and none is selected
        if (options.length > 0 && !selectedDeliveryOption) {
          setSelectedDeliveryOption(options[0].id)
        }
      } catch (error) {
        console.error("Error loading delivery options:", error)
      } finally {
        setLoadingDeliveryOptions(false)
      }
    }

    loadDeliveryOptions()
  }, [])

  // Get unique categories from cart items
  const categories = useMemo(() => {
    const uniqueCategories = new Set<string>();
    cartItems.forEach(item => {
      if (item.product?.category) {
        uniqueCategories.add(item.product.category);
      }
    });
    return Array.from(uniqueCategories);
  }, [cartItems]);

  // Sort and filter cart items
  const processedCartItems = useMemo(() => {
    let items = [...cartItems];

    // Apply category filter
    if (filterCategory) {
      items = items.filter(item => item.product?.category === filterCategory);
    }

    // Apply sorting
    if (sortOrder) {
      items.sort((a, b) => {
        if (sortOrder === 'price-asc') {
          return (a.product?.price || 0) - (b.product?.price || 0);
        } else if (sortOrder === 'price-desc') {
          return (b.product?.price || 0) - (a.product?.price || 0);
        } else if (sortOrder === 'name-asc') {
          return (a.product?.name || '').localeCompare(b.product?.name || '');
        } else if (sortOrder === 'name-desc') {
          return (b.product?.name || '').localeCompare(a.product?.name || '');
        }
        return 0;
      });
    }

    return items;
  }, [cartItems, sortOrder, filterCategory]);

  // Get selected delivery option details
  const selectedDeliveryOptionDetails = useMemo(() => {
    return deliveryOptions.find(option => option.id === selectedDeliveryOption) || null;
  }, [deliveryOptions, selectedDeliveryOption]);

  // Calculate delivery fee based on selected option
  const DELIVERY_FEE = selectedDeliveryOptionDetails?.price || 0;

  // Calculate selected items total
  const selectedItemsTotal = useMemo(() => {
    return cartItems.reduce((total, item) => {
      if (selectedItems[item.id]) {
        return total + (item.product?.price || 0) * item.quantity;
      }
      return total;
    }, 0);
  }, [cartItems, selectedItems]);

  const calculateSubtotal = () => {
    return selectedItemsTotal;
  }

  const calculateTotal = () => {
    return calculateSubtotal() + DELIVERY_FEE;
  }

  const handleCheckout = () => {
    // Only checkout selected items
    const hasSelectedItems = Object.values(selectedItems).some(selected => selected);

    if (!hasSelectedItems) {
      showToast("Please select at least one item to checkout", "error");
      return;
    }

    // Ensure a delivery option is selected
    if (!selectedDeliveryOption) {
      showToast("Please select a delivery option", "error");
      return;
    }

    // Store selected delivery option in localStorage to use in checkout
    localStorage.setItem('selectedDeliveryOption', selectedDeliveryOption);

    router.push('/checkout');
  }

  const toggleSelectAll = () => {
    const allSelected = cartItems.every(item => selectedItems[item.id]);

    const newSelectedState = !allSelected;
    const newSelectedItems = cartItems.reduce((acc, item) => {
      acc[item.id] = newSelectedState;
      return acc;
    }, {} as Record<string, boolean>);

    setSelectedItems(newSelectedItems);
  }

  const toggleSelectItem = (itemId: string) => {
    setSelectedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  }

  const handleBulkRemove = () => {
    const selectedIds = Object.entries(selectedItems)
      .filter(([_, selected]) => selected)
      .map(([id]) => id);

    if (selectedIds.length === 0) {
      showToast("No items selected", "info");
      return;
    }

    // Confirm before removing
    if (window.confirm(`Remove ${selectedIds.length} selected item(s)?`)) {
      selectedIds.forEach(id => {
        removeCartItem(id);
      });

      showToast(`Removed ${selectedIds.length} item(s) from cart`, "success");
    }
  }

  return (
    <>
      <MainHeader />
      <div className="p-4 flex-grow overflow-y-auto">
        <div className="flex items-center justify-between mb-3">
          <h1 className="text-lg font-bold">Your Cart</h1>
          {cartItems.length > 0 && (
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-1 text-blue-600 text-xs font-medium"
            >
              <Filter size={12} />
              {showFilters ? "Hide Filters" : "Filter & Sort"}
            </button>
          )}
        </div>

        {/* Filters and sorting */}
        {showFilters && cartItems.length > 0 && (
          <div className="bg-blue-50 p-2 rounded-lg mb-3 animate-in slide-in-from-top-2 duration-200">
            <div className="flex flex-wrap gap-1 mb-1">
              <button
                onClick={() => setFilterCategory(null)}
                className={`px-2 py-0.5 rounded-full text-xs ${
                  filterCategory === null
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300'
                }`}
              >
                All
              </button>
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setFilterCategory(category)}
                  className={`px-2 py-0.5 rounded-full text-xs ${
                    filterCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-gray-700 border border-gray-300'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>

            <div className="flex flex-wrap gap-1">
              <button
                onClick={() => setSortOrder(sortOrder === 'price-asc' ? null : 'price-asc')}
                className={`px-2 py-0.5 rounded-full text-xs flex items-center gap-0.5 ${
                  sortOrder === 'price-asc'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300'
                }`}
              >
                Price <ChevronUp size={10} />
              </button>
              <button
                onClick={() => setSortOrder(sortOrder === 'price-desc' ? null : 'price-desc')}
                className={`px-2 py-0.5 rounded-full text-xs flex items-center gap-0.5 ${
                  sortOrder === 'price-desc'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300'
                }`}
              >
                Price <ChevronDown size={10} />
              </button>
              <button
                onClick={() => setSortOrder(sortOrder === 'name-asc' ? null : 'name-asc')}
                className={`px-2 py-0.5 rounded-full text-xs flex items-center gap-0.5 ${
                  sortOrder === 'name-asc'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300'
                }`}
              >
                A-Z
              </button>
              <button
                onClick={() => setSortOrder(sortOrder === 'name-desc' ? null : 'name-desc')}
                className={`px-2 py-0.5 rounded-full text-xs flex items-center gap-0.5 ${
                  sortOrder === 'name-desc'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300'
                }`}
              >
                Z-A
              </button>
            </div>
          </div>
        )}

        {/* Show error component if there's an error */}
        {error && errorComponent}

        {loading ? (
          <div className="space-y-4">
            <CartItemSkeleton />
            <CartItemSkeleton />
            <CartItemSkeleton />
            <CartSummarySkeleton />
          </div>
        ) : cartItems.length === 0 ? (
          <div className="text-center py-8">
            <div className="bg-gray-100 p-8 rounded-lg mb-6 inline-block">
              <ShoppingCart size={64} className="text-gray-400 mx-auto" />
            </div>
            <p className="text-gray-500 mb-4">Your cart is empty</p>
            <Link
              href="/products"
              className="bg-blue-600 text-white py-2 px-6 rounded-full hover:bg-blue-700 transition-colors inline-flex items-center gap-2"
            >
              Browse Products
              <ArrowUpDown size={16} />
            </Link>
          </div>
        ) : (
          <>
            {/* Bulk actions */}
            <div className="flex items-center justify-between mb-2 bg-gray-50 p-2 rounded-lg">
              <button
                onClick={toggleSelectAll}
                className="flex items-center gap-1 text-gray-700"
              >
                {cartItems.every(item => selectedItems[item.id]) ? (
                  <>
                    <CheckSquare size={14} className="text-blue-600" />
                    <span className="text-xs">Deselect All</span>
                  </>
                ) : (
                  <>
                    <Square size={14} className="text-gray-400" />
                    <span className="text-xs">Select All</span>
                  </>
                )}
              </button>

              <button
                onClick={handleBulkRemove}
                className="text-red-600 text-xs flex items-center gap-1"
                disabled={!Object.values(selectedItems).some(selected => selected)}
              >
                <Trash2 size={14} />
                Remove Selected
              </button>
            </div>

            <div className="space-y-3 mb-6">
              {processedCartItems.map((item) => (
                <div
                  key={item.id}
                  className={`flex items-center border rounded-lg p-2 transition-colors ${
                    selectedItems[item.id]
                      ? 'border-blue-300 bg-blue-50'
                      : 'border-gray-200 bg-white'
                  }`}
                >
                  {/* Selection checkbox */}
                  <button
                    onClick={() => toggleSelectItem(item.id)}
                    className="mr-1"
                    aria-label={selectedItems[item.id] ? "Deselect item" : "Select item"}
                  >
                    {selectedItems[item.id] ? (
                      <CheckSquare size={18} className="text-blue-600" />
                    ) : (
                      <Square size={18} className="text-gray-400" />
                    )}
                  </button>

                  <ProductIcon
                    product={item.product}
                    size={32}
                    className="w-10 h-10 rounded mr-2 flex-shrink-0"
                  />
                  <div className="flex-grow">
                    <h3 className="font-medium text-gray-900 text-xs">{item.product?.name || "Product"}</h3>
                    <p className="text-gray-700 text-xs">R {item.product?.price.toFixed(2) || "0.00"} each</p>
                    {item.product?.category && (
                      <span className="text-[10px] bg-gray-100 text-gray-600 px-1 py-0.5 rounded-full">
                        {item.product.category}
                      </span>
                    )}
                  </div>

                  <div className="flex flex-col items-end">
                    <div className="flex flex-col items-center gap-1 mb-1">
                      <div className="flex items-center">
                        <button
                          onClick={() => {
                            updateCartItemQuantity(item.id, item.quantity - 1)
                            // Highlight the item that was updated
                            setHighlightedItems(prev => ({ ...prev, [item.id]: true }))
                            // Remove highlight after animation
                            setTimeout(() => {
                              setHighlightedItems(prev => ({ ...prev, [item.id]: false }))
                            }, 500)
                          }}
                          className="bg-gray-200 p-1 rounded-l flex items-center justify-center w-7 h-7"
                          disabled={item.quantity <= 1}
                        >
                          <Minus size={12} />
                        </button>
                        <span className="px-2 py-0.5 bg-gray-100 min-w-[28px] text-center text-xs">{item.quantity}</span>
                        <button
                          onClick={() => {
                            updateCartItemQuantity(item.id, item.quantity + 1)
                            // Highlight the item that was updated
                            setHighlightedItems(prev => ({ ...prev, [item.id]: true }))
                            // Remove highlight after animation
                            setTimeout(() => {
                              setHighlightedItems(prev => ({ ...prev, [item.id]: false }))
                            }, 500)
                          }}
                          className="bg-gray-200 p-1 rounded-r flex items-center justify-center w-7 h-7"
                        >
                          <Plus size={12} />
                        </button>
                      </div>
                      <div className={`text-xs font-medium transition-colors duration-300 ${highlightedItems[item.id] ? 'text-green-600 scale-110' : 'text-blue-600'}`}>
                        R {((item.product?.price || 0) * item.quantity).toFixed(2)}
                      </div>
                    </div>
                    <button
                      onClick={() => removeCartItem(item.id)}
                      className="text-red-600 text-xs flex items-center gap-0.5"
                    >
                      <Trash2 size={12} />
                      <span>Remove</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Delivery options */}
            <div className="bg-gray-50 p-3 rounded-lg mb-4">
              <h3 className="font-medium mb-2 text-sm">Delivery Options</h3>

              {loadingDeliveryOptions ? (
                <div className="flex justify-center py-2">
                  <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                </div>
              ) : deliveryOptions.length === 0 ? (
                <p className="text-xs text-gray-500 text-center py-2">No delivery options available</p>
              ) : (
                <div className="flex gap-2">
                  {deliveryOptions.map(option => (
                    <button
                      key={option.id}
                      onClick={() => setSelectedDeliveryOption(option.id)}
                      className={`flex-1 border rounded-lg p-2 flex items-center ${
                        selectedDeliveryOption === option.id
                          ? 'border-blue-400 bg-blue-50'
                          : 'border-gray-200 bg-white'
                      }`}
                    >
                      <div className="flex items-center justify-center w-8 h-8 mr-2">
                        {option.name.toLowerCase().includes('express') ? (
                          <Package size={18} className={selectedDeliveryOption === option.id ? 'text-blue-600' : 'text-gray-400'} />
                        ) : (
                          <Truck size={18} className={selectedDeliveryOption === option.id ? 'text-blue-600' : 'text-gray-400'} />
                        )}
                      </div>
                      <div className="flex flex-col">
                        <span className="font-medium text-xs">{option.name}</span>
                        <span className="text-xs text-gray-500">R {option.price.toFixed(2)}</span>
                        <span className="text-[10px] text-gray-500">{option.estimated_days}</span>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Price breakdown */}
            <div className="border border-gray-200 rounded-lg p-3 bg-white mb-4">
              <h3 className="font-medium mb-2 text-sm">Order Summary</h3>
              <div className="space-y-1 mb-3">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Items ({cartItems.filter(item => selectedItems[item.id]).length}):</span>
                  <span>R {calculateSubtotal().toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-600">Delivery ({selectedDeliveryOptionDetails?.name || 'Standard'}):</span>
                  <span>R {DELIVERY_FEE.toFixed(2)}</span>
                </div>
                <div className="border-t border-gray-100 pt-1 mt-1"></div>
                <div className="flex justify-between font-bold">
                  <span className="text-sm">Total:</span>
                  <span className={`text-sm transition-all duration-300 ${highlightTotal ? 'text-green-600 scale-110' : 'text-blue-600'}`}>
                    R {calculateTotal().toFixed(2)}
                  </span>
                </div>
              </div>

              <button
                onClick={handleCheckout}
                disabled={!Object.values(selectedItems).some(selected => selected)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-blue-700 transition-colors disabled:bg-gray-300 disabled:text-gray-500 flex items-center justify-center gap-1"
              >
                <ShoppingCart size={16} />
                Proceed to Checkout
              </button>
            </div>
          </>
        )}
      </div>
      <MainFooter />
    </>
  )
}
