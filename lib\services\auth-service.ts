
// import { createClient, createAdminClient } from "@/utils/supabase/server"

// // Generate a valid email that will pass validation
// function generateValidEmail(mobile: string): string {
//   // Clean the mobile number
//   const cleanedMobile = mobile.replace(/\D/g, "")

//   // Use a very simple format with a real domain
//   // No special characters, just alphanumeric
//   return `mobile${cleanedMobile}@gmail.com`
// }

// export async function signUp(mobile: string, password: string, shopName?: string, address?: string) {
//   const supabase = await createClient()

//   // Validate mobile number format (basic validation)
//   if (!mobile.match(/^\d{10,12}$/)) {
//     return { success: false, error: "Please enter a valid mobile number (10-12 digits)" }
//   }

//   try {
//     // First check if a user with this mobile number already exists
//     const { data: existingUser, error: checkError } = await supabase
//       .from("users")
//       .select("id")
//       .eq("mobile", mobile)
//       .single()

//     if (existingUser) {
//       return {
//         success: false,
//         error: "A user with this mobile number already exists. Please login instead.",
//       }
//     }

//     if (checkError && checkError.code !== "PGRST116") {
//       // PGRST116 is the error code for "no rows returned" which is expected
//       // Any other error is a problem
//       console.error("Error checking for existing user:", checkError)
//       return { success: false, error: "Error checking user records" }
//     }

//     // Generate a deterministic email based on the mobile number
//     const email = generateValidEmail(mobile)

//     console.log("Attempting signup with email:", email) // Debug log

//     // First create auth user with the generated email
//     const { data: authData, error: authError } = await supabase.auth.signUp({
//       email: email,
//       password,
//       options: {
//         data: {
//           mobile: mobile, // Store mobile in user metadata
//         },
//       },
//     })

//     if (authError || !authData.user) {
//       console.error("Error signing up:", authError)
//       return { success: false, error: authError?.message || "Failed to sign up" }
//     }

//     // Create a special admin client for inserting the user profile
//     // This bypasses RLS policies
//     const adminSupabase = await createAdminClient()

//     // Then create user profile with the mobile number using admin privileges
//     const { error: profileError } = await adminSupabase.from("users").insert({
//       id: authData.user.id,
//       mobile,
//       shop_name: shopName,
//       address,
//     })

//     if (profileError) {
//       console.error("Error creating user profile:", profileError)

//       // If we get a duplicate key error, it means the user already exists
//       if (profileError.code === "23505") {
//         // PostgreSQL unique violation code
//         return {
//           success: false,
//           error: "A user with this mobile number already exists. Please login instead.",
//         }
//       }

//       return { success: false, error: "Failed to create user profile" }
//     }

//     return { success: true, userId: authData.user.id }
//   } catch (error) {
//     console.error("Unexpected error during signup:", error)
//     return { success: false, error: "An unexpected error occurred" }
//   }
// }

// export async function signIn(mobile: string, password: string) {
//   console.log("Auth service: signIn called with mobile:", mobile)
//   const supabase = await createClient()

//   try {
//     // Generate the same email that was used during signup
//     const email = generateValidEmail(mobile)

//     console.log("Auth service: Attempting login with email:", email)

//     // Sign in with the generated email and password
//     const { data, error } = await supabase.auth.signInWithPassword({
//       email: email,
//       password,
//     })

//     if (error || !data.user) {
//       console.error("Auth service: Error signing in:", error)

//       // Special handling for unconfirmed email
//       if (error?.message?.includes("Email not confirmed")) {
//         return {
//           success: false,
//           error: "Your account needs verification. Please try registering again with the same mobile number.",
//         }
//       }

//       return { success: false, error: "Invalid mobile number or password" }
//     }

//     console.log("Auth service: Login successful for user ID:", data.user.id)

//     // Verify the session was created
//     const { data: sessionData } = await supabase.auth.getSession()
//     console.log("Auth service: Session after login:", sessionData.session ? "Session exists" : "No session")

//     return { success: true, userId: data.user.id }
//   } catch (error) {
//     console.error("Auth service: Unexpected error during signin:", error)
//     return { success: false, error: "An unexpected error occurred" }
//   }
// }

// export async function signOut() {
//   const supabase = await createClient()
//   const { error } = await supabase.auth.signOut()

//   if (error) {
//     console.error("Error signing out:", error)
//     return { success: false, error: error.message }
//   }

//   return { success: true }
// }

// export async function getCurrentUser() {
//   const supabase = await createClient()
//   const {
//     data: { user },
//   } = await supabase.auth.getUser()

//   if (!user) {
//     return null
//   }

//   const { data } = await supabase.from("users").select("*").eq("id =", user.id).single()

//   return data
// }