"use client"

import { useState } from 'react'

interface CartErrorFallbackProps {
  onRetry: () => void
}

export function CartErrorFallback({ onRetry }: CartErrorFallbackProps) {
  const [isRetrying, setIsRetrying] = useState(false)
  
  const handleRetry = () => {
    setIsRetrying(true)
    
    // Call the retry function
    onRetry()
    
    // Reset retrying state after a delay
    setTimeout(() => {
      setIsRetrying(false)
    }, 2000)
  }
  
  return (
    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md mb-4">
      <div className="flex items-center">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-5 w-5 text-yellow-500 mr-2" 
          viewBox="0 0 20 20" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" 
            clipRule="evenodd" 
          />
        </svg>
        <p className="text-sm text-yellow-700">
          There was an issue loading your cart.
        </p>
      </div>
      
      <div className="mt-2 flex justify-end">
        <button
          onClick={handleRetry}
          disabled={isRetrying}
          className="text-xs bg-yellow-100 hover:bg-yellow-200 text-yellow-800 py-1 px-3 rounded disabled:opacity-50 flex items-center"
        >
          {isRetrying ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Retrying...
            </>
          ) : (
            'Retry'
          )}
        </button>
      </div>
    </div>
  )
}
