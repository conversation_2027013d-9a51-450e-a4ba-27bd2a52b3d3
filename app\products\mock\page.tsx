import { MainHeader } from "@/components/main-header"
import { MainFooter } from "@/components/main-footer"
import { SearchBar } from "@/components/search-bar"
import { WhatsAppSupport } from "@/components/whatsapp-support"
import { ProductList } from "@/components/product-list"
import type { Product } from "@/lib/types"

// Mock data for testing
const mockCategories = [
  {
    id: "cat1",
    name: "Groceries",
    products: [
      {
        id: "prod1",
        name: "White Star Maize Meal 5kg",
        description: "Premium quality maize meal",
        price: 65.0,
        image_url: "/placeholder.svg?height=60&width=60",
        in_stock: true,
      },
      {
        id: "prod2",
        name: "Tastic Rice 2kg",
        description: "Premium quality rice",
        price: 45.0,
        image_url: "/placeholder.svg?height=60&width=60",
        in_stock: true,
      },
    ],
  },
  {
    id: "cat2",
    name: "Beverages",
    products: [
      {
        id: "prod3",
        name: "Coca-Cola Can 300ml",
        description: "Refreshing soft drink",
        price: 8.5,
        image_url: "/placeholder.svg?height=60&width=60",
        in_stock: true,
      },
      {
        id: "prod4",
        name: "Fanta Orange 2L",
        description: "Orange flavored soft drink",
        price: 22.0,
        image_url: "/placeholder.svg?height=60&width=60",
        in_stock: true,
      },
    ],
  },
  {
    id: "cat3",
    name: "Household",
    products: [
      {
        id: "prod5",
        name: "Sunlight Dishwashing Liquid 750ml",
        description: "Effective dishwashing liquid",
        price: 35.0,
        image_url: "/placeholder.svg?height=60&width=60",
        in_stock: true,
      },
      {
        id: "prod6",
        name: "Handy Andy Surface Cleaner 750ml",
        description: "All-purpose surface cleaner",
        price: 28.0,
        image_url: "/placeholder.svg?height=60&width=60",
        in_stock: false,
      },
    ],
  },
]

export default function MockProductsPage() {
  return (
    <>
      <MainHeader />
      <SearchBar />
      <div className="p-4 flex-grow overflow-y-auto">
        {mockCategories.map((category) => (
          <div key={category.id}>
            <h2 className="font-bold text-gray-600 mt-4 mb-2 pb-1 border-b border-gray-200">{category.name}</h2>
            <ProductList products={category.products as Product[]} />
          </div>
        ))}

        <WhatsAppSupport />
      </div>
      <MainFooter />
    </>
  )
}
