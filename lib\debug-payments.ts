"use server"

import { createClient } from '@/utils/supabase/client'

export async function checkPaymentsForOrders() {
  const supabase = await createClient()

  try {
    // Get all orders
    const { data: orders, error: ordersError } = await supabase
      .from("orders")
      .select("id, payment_method, payment_status")
      .order("created_at", { ascending: false })
      .limit(20)

    if (ordersError) {
      console.error("Error fetching orders:", ordersError)
      return { error: ordersError.message }
    }

    // Check for payments for each order
    const results = []
    for (const order of orders) {
      const { data: payments, error: paymentsError } = await supabase
        .from("payments")
        .select("id, status, amount")
        .eq("order_id", order.id)

      if (paymentsError) {
        console.error(`Error fetching payments for order ${order.id}:`, paymentsError)
      }

      results.push({
        order_id: order.id,
        order_payment_status: order.payment_status,
        payment_method: order.payment_method,
        has_payment: payments && payments.length > 0,
        payment_count: payments?.length || 0,
        payment_status: payments?.[0]?.status || "None"
      })
    }

    return { results }
  } catch (error) {
    console.error("Exception checking payments:", error)
    return { error: "Internal error" }
  }
}
