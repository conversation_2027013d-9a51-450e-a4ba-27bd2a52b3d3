"use client"

import { useEffect, useState } from "react"
import { createClient } from '@/utils/supabase/client'

export default function LoginRedirectPage() {
  const [status, setStatus] = useState("Checking login status...")
  const [error, setError] = useState("")

  useEffect(() => {
    const checkAndRedirect = async () => {
      try {
        // Get Supabase client
        const supabase = createClient()

        // Check if user is logged in
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError || !user) {
          setStatus("Not logged in. Redirecting to login page...")
          setTimeout(() => {
            window.location.href = "/login"
          }, 1000)
          return
        }

        setStatus("Logged in. Checking profile...")

        // Check if user has a profile
        try {
          const response = await fetch("/api/check-profile", {
            method: "POST",
            headers: {
              "Content-Type": "application/json"
            },
            body: JSON.stringify({
              mobile: user.user_metadata?.mobile || "unknown"
            })
          })

          const result = await response.json()

          if (!response.ok) {
            setError(result.error || "Failed to check profile")
            return
          }

          if (result.created) {
            setStatus("Profile created. Redirecting to products page...")
          } else {
            setStatus("Profile found. Redirecting to products page...")
          }

          // Store login state
          localStorage.setItem("userLoggedIn", "true")

          // Redirect to products page
          setTimeout(() => {
            window.location.href = "/products"
          }, 1000)
        } catch (err) {
          console.error("Error checking profile:", err)
          setError("Failed to check profile. Please try again.")
        }
      } catch (err) {
        console.error("Error in login redirect:", err)
        setError("An unexpected error occurred. Please try again.")
      }
    }

    checkAndRedirect()
  }, [])

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6 text-center">
        <h1 className="text-2xl font-bold mb-6">Login Processing</h1>

        {error ? (
          <div className="mb-6">
            <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
              {error}
            </div>
            <button
              onClick={() => window.location.href = "/login"}
              className="bg-blue-600 text-white py-2 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors"
            >
              Back to Login
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <p className="text-gray-700">{status}</p>
          </div>
        )}
      </div>
    </div>
  )
}
