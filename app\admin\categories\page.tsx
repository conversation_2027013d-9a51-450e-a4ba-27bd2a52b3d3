import Link from "next/link"
import { getCategories } from "@/lib/data-service"
import { Plus } from "lucide-react"
import { CategoryTable } from "@/components/admin/category-table"

export default async function AdminCategories() {
  const categories = await getCategories()

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Categories</h1>
        <Link
          href="/admin/categories/new"
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700 transition-colors"
        >
          <Plus size={18} />
          Add Category
        </Link>
      </div>

      <CategoryTable categories={categories} />
    </div>
  )
}
