import Link from "next/link"

export default function LandingPage() {
  return (
    <div className="flex flex-col justify-center items-center text-center p-6 flex-grow bg-gradient-to-b from-blue-600 to-blue-800 text-white">
      <h1 className="text-3xl font-bold mb-2">Stock Up Your Spaza, Easily!</h1>
      <h2 className="text-xl font-normal mb-6 text-gray-100">
        Order essentials from wholesalers and get them delivered. Save time, save hassle.
      </h2>

      <Link
        href="/products"
        className="w-full bg-white text-blue-600 py-3 px-4 rounded-md text-lg font-medium text-center mb-3 hover:bg-gray-100 transition-colors"
      >
        Browse Products
      </Link>

      <Link
        href="/login"
        className="w-full bg-transparent border border-white text-white py-3 px-4 rounded-md text-lg font-medium text-center hover:bg-white/10 transition-colors mb-3"
      >
        Login
      </Link>

      <Link
        href="/auth"
        className="w-full bg-transparent border border-white/70 text-white/90 py-3 px-4 rounded-md text-lg font-medium text-center hover:bg-white/10 transition-colors mb-3"
      >
        Register
      </Link>

      <div className="mt-8 pt-8 border-t border-white/20 w-full">
        <div className="text-center text-white/50 text-xs">
          © {new Date().getFullYear()} Spaza Smart Order
        </div>
      </div>
    </div>
  )
}
