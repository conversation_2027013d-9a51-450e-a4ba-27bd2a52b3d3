"use client"

import Link from "next/link"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function LandingPage() {
  const { isAuthenticated, loading } = useAuth()
  const router = useRouter()

  // Redirect authenticated users to products page
  useEffect(() => {
    if (!loading && isAuthenticated) {
      router.push('/products')
    }
  }, [isAuthenticated, loading, router])

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center text-center p-6 flex-grow bg-gradient-to-b from-blue-600 to-blue-800 text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mb-4"></div>
        <p className="text-white">Loading...</p>
      </div>
    )
  }

  return (
    <div className="flex flex-col justify-center items-center text-center p-6 flex-grow bg-gradient-to-b from-blue-600 to-blue-800 text-white min-h-screen">
      <div className="max-w-sm w-full space-y-4">
        <h1 className="text-3xl font-bold mb-2">Spaza Smart Order</h1>
        <h2 className="text-lg font-normal mb-8 text-gray-100">
          Stock up your spaza shop easily with wholesale products
        </h2>

        <Link
          href="/login"
          className="block w-full bg-white text-blue-600 py-4 px-6 rounded-lg text-lg font-medium text-center hover:bg-gray-100 transition-colors shadow-lg"
        >
          Login to Your Account
        </Link>

        <Link
          href="/auth"
          className="block w-full bg-transparent border-2 border-white text-white py-4 px-6 rounded-lg text-lg font-medium text-center hover:bg-white/10 transition-colors"
        >
          Create New Account
        </Link>

        <div className="mt-8 pt-6">
          <p className="text-white/70 text-sm">
            Already have products to browse?
          </p>
          <Link
            href="/products"
            className="text-white underline text-sm hover:text-gray-200"
          >
            Browse Products
          </Link>
        </div>

        <div className="mt-8 pt-8 border-t border-white/20">
          <div className="text-center text-white/50 text-xs">
            © {new Date().getFullYear()} Spaza Smart Order
          </div>
        </div>
      </div>
    </div>
  )
}
