"use client"

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface LoginRedirectHandlerProps {
  destination: string
  isActive: boolean
}

/**
 * A component that handles redirection after login with multiple fallback mechanisms
 * to ensure reliable navigation even when Next.js router encounters issues
 */
export function LoginRedirectHandler({ destination, isActive }: LoginRedirectHandlerProps) {
  const router = useRouter()
  const [redirectAttempts, setRedirectAttempts] = useState(0)

  useEffect(() => {
    // Only proceed if component is active and we're not already at the destination
    if (!isActive || window.location.pathname === destination) return;

    // Create a unique key for this specific redirect to prevent conflicts
    const redirectKey = `redirect_${Date.now()}`;

    // Check if we're already in a redirect process to prevent loops
    const currentRedirectKey = sessionStorage.getItem('currentRedirectKey');
    const redirectAttemptCount = parseInt(sessionStorage.getItem('redirectAttemptCount') || '0');

    console.log(`LoginRedirectHandler: Activated, redirecting to ${destination}`,
                `currentRedirectKey: ${currentRedirectKey}`,
                `redirectAttemptCount: ${redirectAttemptCount}`,
                `currentPath: ${window.location.pathname}`);

    // If we've already tried redirecting multiple times, don't try again
    if (redirectAttemptCount >= 2) {
      console.log("LoginRedirectHandler: Too many redirect attempts, clearing redirect state");
      sessionStorage.removeItem('currentRedirectKey');
      sessionStorage.removeItem('redirectAttemptCount');
      sessionStorage.removeItem('redirectingTo');

      // Force reload the destination page directly to break any potential loops
      if (window.location.pathname !== destination) {
        console.log("LoginRedirectHandler: Forcing direct navigation to destination");
        window.location.replace(destination);
      }
      return;
    }

    // Set flags to track this specific redirect attempt
    sessionStorage.setItem('currentRedirectKey', redirectKey);
    sessionStorage.setItem('redirectingTo', destination);
    sessionStorage.setItem('redirectAttemptCount', (redirectAttemptCount + 1).toString());

    console.log("LoginRedirectHandler: Using direct navigation");

    // Use direct navigation instead of router to avoid Next.js router issues
    window.location.href = destination;

    return () => {
      // Cleanup function - if the component unmounts normally,
      // we can assume the redirect was successful
      if (sessionStorage.getItem('currentRedirectKey') === redirectKey) {
        console.log("LoginRedirectHandler: Redirect completed successfully, cleaning up");
        sessionStorage.removeItem('currentRedirectKey');
        sessionStorage.removeItem('redirectAttemptCount');
        sessionStorage.removeItem('redirectingTo');
      }
    };
  }, [isActive, destination, router]);

  // Check for redirect after reload
  useEffect(() => {
    const redirectAfterReload = localStorage.getItem('redirectAfterReload');
    if (redirectAfterReload) {
      console.log(`Found redirectAfterReload flag, redirecting to ${redirectAfterReload}`);
      localStorage.removeItem('redirectAfterReload');
      window.location.href = redirectAfterReload;
    }
  }, []);

  // This component doesn't render anything visible
  return null;
}
