"use client";

import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, ShoppingCart, Package, User, Shield } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"

export function MainFooter() {
  const pathname = usePathname()
  const { user } = useAuth()

  return (
    <footer className="bg-gray-100 border-t border-gray-200 py-3 px-4 relative flex justify-around items-center text-center z-10 sticky bottom-0">
      <Link
        href="/products"
        className={`flex flex-col items-center ${pathname === '/products' ? 'text-blue-600' : 'text-gray-600'}`}
      >
        <Home size={20} />
        <span className="text-xs mt-1">Home</span>
      </Link>

      <Link
        href="/cart"
        className={`flex flex-col items-center ${pathname === '/cart' ? 'text-blue-600' : 'text-gray-600'}`}
      >
        <ShoppingCart size={20} />
        <span className="text-xs mt-1">Cart</span>
      </Link>

      <Link
        href="/orders"
        className={`flex flex-col items-center ${pathname === '/orders' || pathname?.startsWith('/orders/') ? 'text-blue-600' : 'text-gray-600'}`}
      >
        <Package size={20} />
        <span className="text-xs mt-1">Orders</span>
      </Link>

      <Link
        href="/profile"
        className={`flex flex-col items-center ${pathname === '/profile' ? 'text-blue-600' : 'text-gray-600'}`}
      >
        <User size={20} />
        <span className="text-xs mt-1">Profile</span>
      </Link>
      {/* Admin portal quick access (visible only to admins) */}
      {user?.is_admin && (
        <Link
          href="/admin"
          className={`flex flex-col items-center ${pathname?.startsWith('/admin') ? 'text-blue-600' : 'text-gray-600'}`}
        >
          <Shield size={20} />
          <span className="text-xs mt-1">Admin</span>
        </Link>
      )}
    </footer>
  )
}
