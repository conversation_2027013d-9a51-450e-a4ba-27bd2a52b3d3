"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"
import { ProductSearch } from "@/components/product-search"
import { ProductGrid } from "@/components/product-grid"
import { FloatingCart } from "@/components/floating-cart"
import { WhatsAppSupport } from "@/components/whatsapp-support"
import { Loader2, ArrowLeft, Filter, ShoppingBag } from "lucide-react"
import Link from "next/link"
import { searchProducts, type SearchOptions } from "@/lib/search-utils"
import type { Product, Category } from "@/lib/types"
import { useOffline } from "@/contexts/offline-context"

export default function FilteredProductsPage() {
  const searchParams = useSearchParams()
  const { offline } = useOffline()

  // State
  const [products, setProducts] = useState<Product[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalProducts, setTotalProducts] = useState(0)
  const [hasMore, setHasMore] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({})
  const [priceRange, setPriceRange] = useState({ min: 0, max: 1000 })

  // Get initial category from URL
  const initialCategory = searchParams?.get("category") || ""
  const initialQuery = searchParams?.get("query") || ""

  // Load categories
  useEffect(() => {
    async function loadCategories() {
      try {
        const response = await fetch("/api/categories")
        if (!response.ok) {
          throw new Error("Failed to load categories")
        }

        const data = await response.json()
        setCategories(data)
      } catch (error) {
        setError("Failed to load categories")
      }
    }

    loadCategories()
  }, [])

  // Load price range
  useEffect(() => {
    async function loadPriceRange() {
      try {
        const response = await fetch("/api/products/price-range")
        if (!response.ok) {
          throw new Error("Failed to load price range")
        }

        const data = await response.json()
        setPriceRange({ min: data.min, max: data.max })
      } catch (error) {
        // Use default price range
      }
    }

    loadPriceRange()
  }, [])

  // Load products based on search options
  useEffect(() => {
    async function loadProducts() {
      setLoading(true)
      setError(null)

      try {
        // Set up search options
        const options: SearchOptions = {
          ...searchOptions
        }

        // Add initial category if provided and not already in options
        if (initialCategory && !options.categoryId) {
          options.categoryId = initialCategory
        }

        // Add initial query if provided and not already in options
        if (initialQuery && !options.query) {
          options.query = initialQuery
        }

        // Don't update searchOptions here - that's causing the infinite loop
        // Search products
        const result = await searchProducts(options)

        setProducts(result.items)
        setTotalProducts(result.total)
        setHasMore(result.hasMore)
        setCurrentPage(1)
      } catch (error) {
        setError("Failed to load products")
      } finally {
        setLoading(false)
      }
    }

    // Don't load products in offline mode
    if (!offline) {
      loadProducts()
    } else {
      setLoading(false)
      // In a real app, you would load cached products from IndexedDB here
    }
  }, [searchOptions, initialCategory, initialQuery, offline]) // Keep these dependencies

  // Handle search
  const handleSearch = (options: SearchOptions) => {
    setSearchOptions(options)
  }

  // Load more products
  const loadMoreProducts = async () => {
    if (loadingMore || !hasMore) return

    setLoadingMore(true)

    try {
      const nextPage = currentPage + 1
      const options: SearchOptions = {
        ...searchOptions,
        limit: 20,
        offset: (nextPage - 1) * 20
      }

      // Add initial category if provided
      if (initialCategory && !options.categoryId) {
        options.categoryId = initialCategory
      }

      // Add initial query if provided
      if (initialQuery && !options.query) {
        options.query = initialQuery
      }

      const result = await searchProducts(options)

      setProducts(prev => [...prev, ...result.items])
      setHasMore(result.hasMore)
      setCurrentPage(nextPage)
    } catch (error) {
      // Handle error silently to avoid disrupting the user experience
    } finally {
      setLoadingMore(false)
    }
  }

  // Get current category name
  const currentCategoryName = initialCategory
    ? categories.find(c => c.id === initialCategory)?.name || "Category"
    : "All Products"

  return (
    <>
      <MainHeader />

      <div className="p-4 flex-grow overflow-y-auto">
        {/* Breadcrumbs */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Link href="/products" className="text-gray-500 hover:text-gray-700">
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-xl font-bold">{currentCategoryName}</h1>
          </div>

          <div className="text-sm text-gray-500">
            {!loading && `${totalProducts} products`}
          </div>
        </div>

        {/* Search and filters */}
        <ProductSearch
          categories={categories as import("@/lib/types/schema").Category[]}
          initialMinPrice={priceRange.min}
          initialMaxPrice={priceRange.max}
          onSearch={handleSearch}
          className="mb-4"
        />

        {/* Error message */}
        {error && (
          <div className="bg-red-100 text-red-700 p-4 rounded-md mb-4">
            <p>{error}</p>
          </div>
        )}

        {/* Loading state */}
        {loading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 text-blue-600 animate-spin mb-4" />
            <p className="text-gray-600">Loading products...</p>
          </div>
        ) : (
          <>
            {/* No results */}
            {products.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <ShoppingBag className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
                <p className="text-gray-600 mb-4">
                  Try adjusting your search or filter criteria
                </p>
                <button
                  onClick={() => {
                    setSearchOptions({})
                  }}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            ) : (
              <>
                {/* Product grid */}
                <div className="grid grid-cols-2 gap-4">
                  {products.map((product) => (
                    <div key={product.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                      <Link href={`/products/${product.id}`}>
                        <div className="p-2">
                          <div className="aspect-square bg-gray-100 rounded-md flex items-center justify-center mb-2">
                            {product.image_url ? (
                              <img
                                src={product.image_url}
                                alt={product.name}
                                className="w-full h-full object-contain p-2"
                              />
                            ) : (
                              <ShoppingBag className="h-8 w-8 text-gray-400" />
                            )}
                          </div>
                          <h3 className="font-medium text-sm line-clamp-2 h-10">{product.name}</h3>
                          <div className="flex justify-between items-center mt-1">
                            <span className="font-bold text-blue-600">
                              R {typeof product.price === 'string'
                                ? parseFloat(product.price).toFixed(2)
                                : product.price.toFixed(2)}
                            </span>
                            {!product.is_available && (
                              <span className="text-xs text-red-600">Out of stock</span>
                            )}
                          </div>
                        </div>
                      </Link>
                    </div>
                  ))}
                </div>

                {/* Load more button */}
                {hasMore && (
                  <div className="mt-6 text-center">
                    <button
                      onClick={loadMoreProducts}
                      disabled={loadingMore}
                      className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50 flex items-center gap-2 mx-auto"
                    >
                      {loadingMore ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading...
                        </>
                      ) : (
                        <>Load More</>
                      )}
                    </button>
                  </div>
                )}
              </>
            )}
          </>
        )}

        <WhatsAppSupport />
      </div>

      <FloatingCart />
      <MainFooter />
    </>
  )
}







