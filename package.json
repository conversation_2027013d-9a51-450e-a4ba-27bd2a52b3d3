{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "clean": "node clean.js", "clean:build": "node clean.js && next build", "fresh": "npx rimraf .next && npx rimraf node_modules/.cache && next dev", "start": "next start", "lint": "next lint", "setup-db": "node scripts/run-db-setup.js"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "libphonenumber-js": "^1.12.8", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^2.1.9", "recharts": "2.15.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "vaul": "^0.9.9", "workbox-core": "latest", "workbox-expiration": "latest", "workbox-precaching": "latest", "workbox-routing": "latest", "workbox-strategies": "latest", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/uuid": "^10.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}