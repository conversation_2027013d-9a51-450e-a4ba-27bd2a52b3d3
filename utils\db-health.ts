"use server"

import { createClient } from '@/utils/supabase/client'

/**
 * Check if the database is accessible and tables exist
 * Returns a detailed health report
 */
export async function checkDatabaseHealth() {
  try {
    const supabase = await createClient()
    
    // Check if we can connect to Supabase
    const { data: connectionTest, error: connectionError } = await supabase.from('_health').select('*').limit(1)
    
    if (connectionError && !connectionError.message.includes('does not exist')) {
      return {
        connected: false,
        error: `Connection error: ${connectionError.message}`,
        tables: {},
        overallStatus: 'error'
      }
    }
    
    // Check essential tables
    const tables = ['users', 'categories', 'products', 'cart_items', 'orders', 'order_items']
    const tableStatus: Record<string, { exists: boolean, count: number, error?: string }> = {}
    
    for (const table of tables) {
      try {
        const { count, error } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true })
        
        if (error) {
          tableStatus[table] = { 
            exists: false, 
            count: 0, 
            error: error.message 
          }
        } else {
          tableStatus[table] = { 
            exists: true, 
            count: count || 0 
          }
        }
      } catch (err: any) {
        tableStatus[table] = { 
          exists: false, 
          count: 0, 
          error: err.message || 'Unknown error' 
        }
      }
    }
    
    // Check if all tables exist
    const allTablesExist = Object.values(tableStatus).every(status => status.exists)
    
    // Check if we have essential data
    const hasCategories = tableStatus['categories']?.count > 0
    const hasProducts = tableStatus['products']?.count > 0
    
    // Determine overall status
    let overallStatus = 'healthy'
    if (!allTablesExist) {
      overallStatus = 'missing_tables'
    } else if (!hasCategories || !hasProducts) {
      overallStatus = 'missing_data'
    }
    
    return {
      connected: true,
      tables: tableStatus,
      overallStatus
    }
  } catch (error: any) {
    return {
      connected: false,
      error: error.message || 'Unknown error checking database health',
      tables: {},
      overallStatus: 'error'
    }
  }
}

/**
 * Fix common database issues automatically
 */
export async function fixDatabaseIssues() {
  try {
    // Run the setup script
    const { exec } = require('child_process')
    
    return new Promise((resolve, reject) => {
      exec('node scripts/setup-database.js', (error: any, stdout: string, stderr: string) => {
        if (error) {
          console.error(`Error running setup script: ${error.message}`)
          reject({
            success: false,
            error: error.message,
            details: stderr
          })
          return
        }
        
        if (stderr) {
          console.error(`Setup script stderr: ${stderr}`)
        }
        
        console.log(`Setup script stdout: ${stdout}`)
        resolve({
          success: true,
          message: 'Database issues fixed successfully',
          details: stdout
        })
      })
    })
  } catch (error: any) {
    return {
      success: false,
      error: error.message || 'Unknown error fixing database issues'
    }
  }
}

/**
 * Simple check if the database is accessible
 * Returns true if connected, false otherwise
 */
export async function isDatabaseConnected() {
  try {
    const supabase = await createClient()
    const { data, error } = await supabase.from('categories').select('id').limit(1)
    
    // If we get a response (even empty) without error, we're connected
    return !error
  } catch (error) {
    console.error('Error checking database connection:', error)
    return false
  }
}
