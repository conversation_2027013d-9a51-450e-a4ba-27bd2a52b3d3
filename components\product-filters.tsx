"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import type { Category } from "@/lib/types/schema"

interface ProductFiltersProps {
  categories: Category[]
  minPrice: number
  maxPrice: number
}

export function ProductFilters({ categories, minPrice, maxPrice }: ProductFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [selectedCategory, setSelectedCategory] = useState<string>(searchParams?.get("category") || "")
  const [priceRange, setPriceRange] = useState<[number, number]>([
    Number(searchParams?.get("minPrice")) || minPrice,
    Number(searchParams?.get("maxPrice")) || maxPrice,
  ])
  const [inStock, setInStock] = useState<boolean>(searchParams?.get("inStock") !== "false")

  // Use a debounce effect to avoid too many URL changes
  useEffect(() => {
    const timer = setTimeout(() => {
      updateFilters()
    }, 500)

    return () => clearTimeout(timer)
  }, [selectedCategory, priceRange, inStock])

  const updateFilters = () => {
    const params = new URLSearchParams()

    if (selectedCategory) {
      params.set("category", selectedCategory)
    }

    if (priceRange[0] !== minPrice) {
      params.set("minPrice", priceRange[0].toString())
    }

    if (priceRange[1] !== maxPrice) {
      params.set("maxPrice", priceRange[1].toString())
    }

    if (!inStock) {
      params.set("inStock", "false")
    }

    const queryString = params.toString()
    router.push(`/products/filter${queryString ? `?${queryString}` : ""}`)
  }

  return (
    <div className="bg-white p-4 border border-gray-200 rounded-md mb-4">
      <h2 className="font-bold text-lg mb-3">Filters</h2>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md"
        >
          <option value="">All Categories</option>
          {categories.map((category) => (
            <option key={category.id} value={category.id}>
              {category.name}
            </option>
          ))}
        </select>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Price Range: R{priceRange[0].toFixed(2)} - R{priceRange[1].toFixed(2)}
        </label>
        <div className="flex gap-2">
          <input
            type="range"
            min={minPrice}
            max={maxPrice}
            value={priceRange[0]}
            onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
            className="w-full"
          />
          <input
            type="range"
            min={minPrice}
            max={maxPrice}
            value={priceRange[1]}
            onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value)])}
            className="w-full"
          />
        </div>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          id="inStock"
          checked={inStock}
          onChange={(e) => setInStock(e.target.checked)}
          className="h-4 w-4 text-blue-600 border-gray-300 rounded"
        />
        <label htmlFor="inStock" className="ml-2 text-sm text-gray-700">
          In Stock Only
        </label>
      </div>
    </div>
  )
}


