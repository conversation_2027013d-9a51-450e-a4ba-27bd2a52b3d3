import {
  Apple,
  Beef,
  Beer,
  Cake,
  CandyCane,
  Carrot,
  Cherry,
  ChefHat,
  Coffee,
  Cookie,
  Croissant,
  Egg,
  Fish,
  FlaskConical,
  Frown,
  Grape,
  IceCream,

  Milk,
  Package,
  Popcorn,
  Sandwich,
  ShoppingBag,
  ShoppingBasket,
  ShoppingCart,
  Soup,
  Utensils,
  Wine,
} from "lucide-react";

// Map product categories to icons
export const categoryIcons: Record<string, React.ReactNode> = {
  "Beverages": <Coffee size={24} />,
  "Dairy": <Milk size={24} />,
  "Bakery": <Croissant size={24} />,
  "Fruits": <Apple size={24} />,
  "Vegetables": <Carrot size={24} />,
  "Meat": <Beef size={24} />,
  "Seafood": <Fish size={24} />,
  "Snacks": <Cookie size={24} />,
  "Canned Goods": <Soup size={24} />,
  "Frozen Foods": <IceCream size={24} />,
  "Household": <ShoppingBag size={24} />,
  "Personal Care": <FlaskConical size={24} />,
  "Alcohol": <Beer size={24} />,
  "Sweets": <CandyCane size={24} />,
  "Grains": <Package size={24} />,
  "Condiments": <Soup size={24} />,
  "Breakfast": <Egg size={24} />,
  "Pasta": <Utensils size={24} />,
  "Baking": <ChefHat size={24} />,
  "Desserts": <Cake size={24} />,
};

// Function to get icon based on product name or category
export function getProductIcon(product: { name: string; category_id?: string }, size = 24) {
  const name = product.name.toLowerCase();

  // Check for specific product names
  if (name.includes("milk") || name.includes("yogurt") || name.includes("cheese")) {
    return <Milk size={size} />;
  } else if (name.includes("bread") || name.includes("loaf")) {
    return <Croissant size={size} />;
  } else if (name.includes("apple") || name.includes("fruit")) {
    return <Apple size={size} />;
  } else if (name.includes("carrot") || name.includes("vegetable")) {
    return <Carrot size={size} />;
  } else if (name.includes("meat") || name.includes("beef") || name.includes("chicken")) {
    return <Beef size={size} />;
  } else if (name.includes("fish") || name.includes("seafood")) {
    return <Fish size={size} />;
  } else if (name.includes("cookie") || name.includes("biscuit")) {
    return <Cookie size={size} />;
  } else if (name.includes("soup") || name.includes("canned")) {
    return <Soup size={size} />;
  } else if (name.includes("ice cream") || name.includes("frozen")) {
    return <IceCream size={size} />;
  } else if (name.includes("beer") || name.includes("wine") || name.includes("alcohol")) {
    return <Beer size={size} />;
  } else if (name.includes("candy") || name.includes("sweet")) {
    return <CandyCane size={size} />;
  } else if (name.includes("egg")) {
    return <Egg size={size} />;
  } else if (name.includes("pasta") || name.includes("noodle")) {
    return <Utensils size={size} />;
  } else if (name.includes("cake") || name.includes("dessert")) {
    return <Cake size={size} />;
  } else if (name.includes("coffee") || name.includes("tea")) {
    return <Coffee size={size} />;
  } else if (name.includes("cheese")) {
    return <Milk size={size} />;
  } else if (name.includes("cherry") || name.includes("berry")) {
    return <Cherry size={size} />;
  } else if (name.includes("grape")) {
    return <Grape size={size} />;
  } else if (name.includes("sandwich")) {
    return <Sandwich size={size} />;
  } else if (name.includes("popcorn")) {
    return <Popcorn size={size} />;
  } else if (name.includes("croissant")) {
    return <Croissant size={size} />;
  } else if (name.includes("wine")) {
    return <Wine size={size} />;
  }

  // Default icon if no specific match
  return <ShoppingCart size={size} />;
}

// Function to get a random product icon
export function getRandomProductIcon(size = 24) {
  const icons = [
    <Apple size={size} />,
    <Beef size={size} />,
    <Beer size={size} />,
    <Croissant size={size} />,
    <Cake size={size} />,
    <CandyCane size={size} />,
    <Carrot size={size} />,
    <Cherry size={size} />,
    <Coffee size={size} />,
    <Cookie size={size} />,
    <Egg size={size} />,
    <Fish size={size} />,
    <IceCream size={size} />,
    <Milk size={size} />,
    <Package size={size} />,
    <ShoppingCart size={size} />,
    <Soup size={size} />,
    <Utensils size={size} />,
  ];

  const randomIndex = Math.floor(Math.random() * icons.length);
  return icons[randomIndex];
}

// Component for displaying product icon
export function ProductIcon({ product, size = 24, className = "" }: {
  product: { name: string; category_id?: string } | null | undefined;
  size?: number;
  className?: string;
}) {
  if (!product) {
    return <Frown size={size} className={className} />;
  }

  return (
    <div className={`flex items-center justify-center bg-gray-100 rounded-md p-2 ${className}`}>
      {getProductIcon(product, size)}
    </div>
  );
}
