import { NextRequest, NextResponse } from "next/server"
import { createClient } from '@/utils/supabase/client'
import { isProduct } from "@/lib/types"

export async function GET(request: NextRequest) {
  try {
    // Get barcode from query parameters
    const { searchParams } = new URL(request.url)
    const barcode = searchParams.get("code")

    if (!barcode) {
      return NextResponse.json(
        { error: "Barcode parameter is required" },
        { status: 400 }
      )
    }

    // Create Supabase client
    const supabase = await createClient()

    // Query the database for the product with the given barcode
    const { data, error } = await supabase
      .from("products")
      .select("*")
      .eq("barcode", barcode)
      .single()

    if (error) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      )
    }

    // Validate the product data
    if (!data || !isProduct(data)) {
      return NextResponse.json(
        { error: "Invalid product data" },
        { status: 500 }
      )
    }

    // Return the product data
    return NextResponse.json(data)
  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
