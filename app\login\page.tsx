"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/multi-shop-auth-context"
import { createClient } from '@/utils/supabase/client'

export default function LoginPage() {
  const [mobileNumber, setMobileNumber] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  const { user, isAuthenticated, login } = useAuth()
  const router = useRouter()

  // Check if user is already logged in
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log("User already authenticated, redirecting to products...")
      router.push('/products')
    }
  }, [isAuthenticated, user, router])

  const formatMobile = (mobile: string) => {
    // Remove all non-digits
    const digits = mobile.replace(/\D/g, '')

    // If starts with 27, keep as is
    if (digits.startsWith('27')) {
      return digits
    }

    // If starts with 0, replace with 27
    if (digits.startsWith('0')) {
      return '27' + digits.substring(1)
    }

    // If 9 digits, assume it's missing the leading 0
    if (digits.length === 9) {
      return '27' + digits
    }

    return digits
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!mobileNumber || !password) {
      setError("Please enter both mobile number and password")
      return
    }

    setLoading(true)
    setError("")
    setSuccess("")

    try {
      const formattedMobile = formatMobile(mobileNumber)
      console.log("Attempting login with mobile:", formattedMobile)

      const result = await login(formattedMobile, password)

      if (result.success) {
        setSuccess("Login successful! Redirecting...")

        // Small delay to show success message, then redirect
        setTimeout(() => {
          router.push('/products')
        }, 1000)
      } else {
        setError(result.error || "Login failed. Please check your credentials.")
      }
    } catch (err) {
      console.error("Unexpected login error:", err)
      setError("An unexpected error occurred. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">
          Login to Your Account
        </h1>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {success}
          </div>
        )}

        <form onSubmit={handleLogin}>
          <div className="mb-4">
            <label htmlFor="mobile" className="block mb-1 font-medium text-gray-700">
              Mobile Number
            </label>
            <input
              type="tel"
              id="mobile"
              value={mobileNumber}
              onChange={(e) => setMobileNumber(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="0721234567 or 27721234567"
              disabled={loading}
              required
            />
            <p className="text-xs text-gray-500 mt-1">Enter your mobile number</p>
          </div>

          <div className="mb-6">
            <label htmlFor="password" className="block mb-1 font-medium text-gray-700">
              Password
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter your password"
              disabled={loading}
              required
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors disabled:bg-blue-400 flex items-center justify-center"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Logging In...
              </>
            ) : (
              "Login"
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <Link href="/auth" className="text-blue-600 hover:underline">
              Register here
            </Link>
          </p>

          <p className="mt-4">
            <Link href="/" className="text-blue-600 hover:underline text-sm">
              ← Back to Welcome Screen
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}