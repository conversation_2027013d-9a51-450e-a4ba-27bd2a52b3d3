import { NextRequest, NextResponse } from "next/server"
import { createClient } from '@/utils/supabase/client'
import { getUserId } from "@/lib/auth-utils"
import { generateReceipt } from "@/lib/receipt-service"
import { v4 as uuidv4 } from 'uuid'

export async function POST(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the user ID from the session
    const userId = await getUserId()
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get the order ID from the URL params
    const orderId = params.id
    if (!orderId) {
      return NextResponse.json({ error: "Order ID is required" }, { status: 400 })
    }

    // Create Supabase client
    const supabase = await createClient()

    // First verify the order belongs to this user
    const { data: order, error: orderError } = await supabase
      .from("orders")
      .select("id, user_id, payment_status, payment_method")
      .eq("id", orderId)
      .single()

    if (orderError) {
      console.error("Database error fetching order:", orderError.message, orderError)
      return NextResponse.json({ error: "Failed to fetch order" }, { status: 500 })
    }

    if (!order) {
      return NextResponse.json({ error: "Order not found" }, { status: 404 })
    }

    if (order.user_id !== userId) {
      return NextResponse.json({ error: "You don't have permission to update this order" }, { status: 403 })
    }

    // Only allow confirmation for COD orders that are marked as paid
    if (order.payment_method !== "COD" || order.payment_status !== "Paid") {
      return NextResponse.json(
        { error: "Receipt confirmation only available for paid COD orders" },
        { status: 400 }
      )
    }

    // Create a receipt record for this order
    const receiptCreated = await createReceiptRecord(supabase, orderId)

    // If we successfully created a receipt or found an existing one,
    // consider this a success even if we can't update the order
    if (receiptCreated) {
      try {
        // Try to update the order, but don't fail if the columns don't exist
        await supabase
          .from("orders")
          .update({
            receipt_confirmed: true,
            receipt_confirmed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq("id", orderId)
      } catch (updateError) {
        // Log the error but don't fail the request
        console.warn("Could not update order with receipt confirmation:", updateError)
      }

      // Return success response
      return NextResponse.json({
        success: true,
        message: "Receipt confirmation recorded successfully",
        timestamp: new Date().toISOString()
      })
    }

    // If we couldn't create a receipt, return an error
    return NextResponse.json(
      { error: "Unable to confirm receipt at this time. Please try again later or contact support." },
      { status: 500 }
    )
  } catch (error) {
    console.error("Unexpected error confirming receipt:", error)
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// Helper function to create a receipt record
async function createReceiptRecord(supabase: any, orderId: string): Promise<boolean> {
  try {
    console.log(`Creating receipt record for order ${orderId}...`)

    // Get payment for this order
    const { data: payment, error: paymentError } = await supabase
      .from("payments")
      .select("id, status, amount")
      .eq("order_id", orderId)
      .single()

    if (paymentError && paymentError.code !== 'PGRST116') {
      console.error(`Error fetching payment for order ${orderId}:`, paymentError)
    }

    let paymentId = payment?.id

    // If no payment found, create one
    if (!paymentId) {
      console.log(`No payment found for order ${orderId}, creating one...`)

      // First get the order details to get the amount
      const { data: orderData, error: orderError } = await supabase
        .from("orders")
        .select("total_amount, payment_method, payment_status")
        .eq("id", orderId)
        .single()

      if (orderError) {
        console.error(`Error fetching order ${orderId}:`, orderError)
        return false
      }

      if (!orderData) {
        console.error(`Could not find order ${orderId}`)
        return false
      }

      // Create a new payment record
      const { data: newPayment, error: createError } = await supabase
        .from("payments")
        .insert({
          order_id: orderId,
          amount: orderData.total_amount || 0,
          status: 'Paid',
          payment_method: orderData.payment_method || 'COD',
          reference: `ORD-${orderId.substring(0, 8)}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()

      if (createError) {
        console.error(`Failed to create payment for order ${orderId}:`, createError)
        return false
      }

      if (!newPayment || newPayment.length === 0) {
        console.error(`Payment created but no data returned for order ${orderId}`)
        return false
      }

      paymentId = newPayment[0].id
      console.log(`Created new payment with ID ${paymentId} for order ${orderId}`)
    } else if (payment.status !== 'Paid') {
      // Update payment status to Paid if it exists but isn't marked as paid
      console.log(`Updating payment ${paymentId} status to Paid`)
      await supabase
        .from("payments")
        .update({
          status: 'Paid',
          updated_at: new Date().toISOString()
        })
        .eq("id", payment.id)
    }

    if (!paymentId) {
      console.error(`Could not create or find payment for order ${orderId}`)
      return false
    }

    // Check if receipt already exists
    const { data: existingReceipt, error: receiptCheckError } = await supabase
      .from("receipts")
      .select("id")
      .eq("payment_id", paymentId)
      .single()

    if (receiptCheckError && receiptCheckError.code !== 'PGRST116') {
      console.error(`Error checking for existing receipt for payment ${paymentId}:`, receiptCheckError)
    }

    if (existingReceipt) {
      console.log(`Receipt already exists for payment ${paymentId}`)
      return true
    }

    // Generate a receipt for this payment
    console.log(`Generating receipt for payment ${paymentId}...`)
    const receipt = await generateReceipt(paymentId)

    // If receipt was created successfully, update it to mark as confirmed
    if (receipt?.id) {
      console.log(`Receipt generated with ID ${receipt.id}, marking as confirmed`)
      await supabase
        .from("receipts")
        .update({
          confirmed_by_customer: true,
          confirmed_at: new Date().toISOString()
        })
        .eq("id", receipt.id)

      return true
    }

    // If we couldn't generate a receipt, create a simple one
    if (!receipt) {
      console.log(`Failed to generate receipt via service, creating a simple one...`)
      const receiptId = uuidv4()
      const receiptNumber = `R-${Date.now().toString().slice(-6)}`

      // Get payment amount
      const amount = payment?.amount || 0
      const now = new Date().toISOString()

      const { data: newReceipt, error: receiptError } = await supabase
        .from("receipts")
        .insert({
          id: receiptId,
          payment_id: paymentId,
          receipt_number: receiptNumber,
          amount: amount,
          issued_date: now,
          created_at: now,
          updated_at: now
        })
        .select()

      if (receiptError) {
        console.error(`Failed to create receipt for payment ${paymentId}:`, receiptError)
        return false
      }

      if (!newReceipt || newReceipt.length === 0) {
        console.error(`Receipt created but no data returned for payment ${paymentId}`)
        return false
      }

      console.log(`Created simple receipt ${receiptId} for payment ${paymentId}`)
      return true
    }

    return false
  } catch (error) {
    console.error("Error creating receipt record:", error)
    return false
  }
}



