"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"
import { useCart } from "@/contexts/cart-context"
import {
  Plus, X, ShoppingCart, Search, User,
  Grid, Clock, Heart, Menu, Home, Barcode
} from "lucide-react"
import { QuickAccessMenu } from "./quick-access-menu"
import { BarcodeScanner } from "./barcode-scanner"

export function QuickAccessFAB() {
  const [isOpen, setIsOpen] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isScannerOpen, setIsScannerOpen] = useState(false)
  const router = useRouter()
  const { user } = useAuth()
  const { cartCount, addToCart } = useCart()

  // Handle scroll behavior - hide on scroll down, show on scroll up
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false)
        // Also close the menu when scrolling down
        setIsOpen(false)
      } else {
        setIsVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [lastScrollY])

  const toggleMenu = () => {
    setIsOpen(!isOpen)
  }

  const handleQuickAction = (action: string) => {
    setIsOpen(false)

    switch (action) {
      case "home":
        router.push("/products")
        break
      case "cart":
        router.push("/cart")
        break
      case "search":
        router.push("/search")
        break
      case "profile":
        router.push("/profile")
        break
      case "barcode":
        setIsScannerOpen(true)
        break
      case "categories":
        // This will be implemented with a categories modal
        break
      default:
        break
    }
  }

  const handleBarcodeScan = async (barcode: string) => {
    try {
      // Close the scanner
      setIsScannerOpen(false)

      // Fetch product by barcode
      const response = await fetch(`/api/products/barcode?code=${encodeURIComponent(barcode)}`)

      if (!response.ok) {
        throw new Error('Product not found')
      }

      const product = await response.json()

      // Add product to cart
      if (product && product.id) {
        await addToCart(product.id, 1)

        // Show success message or navigate to product
        router.push(`/products/${product.id}`)
      }
    } catch (error) {
      console.error('Error handling barcode scan:', error)
      // Could show an error toast here
    }
  }

  if (!user) return null // Don't show FAB if user is not logged in

  return (
    <>
      {/* Main FAB button */}
      <div
        className={`fixed right-4 z-50 transition-all duration-300 ${
          isVisible ? "bottom-20" : "bottom-[-80px]"
        }`}
      >
        <button
          onClick={toggleMenu}
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full w-16 h-16 flex items-center justify-center shadow-lg transition-all duration-300 touch-target"
          aria-label={isOpen ? "Close menu" : "Open quick access menu"}
        >
          {isOpen ? (
            <X size={28} />
          ) : (
            <Plus size={28} />
          )}
        </button>

        {/* Cart badge */}
        {cartCount > 0 && !isOpen && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-7 h-7 flex items-center justify-center font-bold">
            {cartCount > 99 ? '99+' : cartCount}
          </span>
        )}
      </div>

      {/* Quick access menu */}
      {isOpen && (
        <QuickAccessMenu onAction={handleQuickAction} onClose={() => setIsOpen(false)} />
      )}

      {/* Barcode scanner */}
      <BarcodeScanner
        isOpen={isScannerOpen}
        onScan={handleBarcodeScan}
        onClose={() => setIsScannerOpen(false)}
      />

      {/* Backdrop when menu is open */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 z-40 animate-in fade-in duration-200 touch-target"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  )
}
