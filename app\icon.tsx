import { ImageResponse } from 'next/og'

export function generateImageMetadata() {
  return [
    {
      contentType: 'image/png',
      size: { width: 192, height: 192 },
      id: 'icon',
    },
    {
      contentType: 'image/png',
      size: { width: 512, height: 512 },
      id: 'icon-512',
    },
  ]
}

// Image generation
export default function Icon({ id }: { id: string }) {
  // Determine size based on id
  const size = id === 'icon-512' 
    ? { width: 512, height: 512 } 
    : { width: 192, height: 192 }
  
  // Adjust font size and border radius based on icon size
  const fontSize = id === 'icon-512' ? 384 : 144
  const borderRadius = id === 'icon-512' ? '64px' : '24px'

  return new ImageResponse(
    (
      <div
        style={{
          fontSize,
          background: '#2563eb',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          borderRadius,
        }}
      >
        S
      </div>
    ),
    { ...size }
  )
}
