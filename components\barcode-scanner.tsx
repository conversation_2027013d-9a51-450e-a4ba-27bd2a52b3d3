"use client"

import { useState, useEffect, useRef } from "react"
import { Camera, X, RefreshCw } from "lucide-react"
import { createBarcodeScanner, type BarcodeResult } from "@/lib/barcode-scanner"

interface BarcodeScannerProps {
  onScan: (barcode: string) => void
  onClose?: () => void
  isOpen: boolean
}

export function BarcodeScanner({ onScan, onClose, isOpen }: BarcodeScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const scannerRef = useRef<ReturnType<typeof createBarcodeScanner> | null>(null)
  const [scanning, setScanning] = useState(false)
  const [error, setError] = useState("")
  const [cameraPermission, setCameraPermission] = useState<boolean | null>(null)

  // Start/stop scanner based on isOpen prop
  useEffect(() => {
    if (isOpen) {
      startCamera()
    } else {
      stopCamera()
    }

    return () => {
      stopCamera()
    }
  }, [isOpen])

  const startCamera = async () => {
    try {
      if (!videoRef.current) return

      // Create barcode scanner with options
      scannerRef.current = createBarcodeScanner({
        tryHarder: true,
        onResult: handleBarcodeResult,
        onError: (error) => {
          setCameraPermission(false)
          setError("Error with barcode scanner: " + error.message)
        }
      })

      // Start scanning
      await scannerRef.current.startScanning(videoRef.current)
      setCameraPermission(true)
      setScanning(true)
    } catch (err) {
      setCameraPermission(false)
      setError("Camera access denied. Please enable camera permissions.")
    }
  }

  const stopCamera = () => {
    if (scannerRef.current) {
      scannerRef.current.stopScanning()
    }
    setScanning(false)
  }

  const handleBarcodeResult = (result: BarcodeResult) => {
    // Stop scanning once we have a result
    if (scannerRef.current) {
      scannerRef.current.stopScanning()
    }

    setScanning(false)
    onScan(result.text)
  }

  const resetScanner = () => {
    setError("")
    startCamera()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden w-full max-w-md">
        <div className="p-3 bg-blue-50 border-b border-blue-100 flex justify-between items-center">
          <h2 className="font-medium flex items-center gap-2">
            <Camera size={18} />
            Scan Barcode
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        {/* Camera viewfinder */}
        <div className="relative aspect-video bg-black">
          {scanning && (
            <>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 border-2 border-dashed border-yellow-400 m-8 pointer-events-none"></div>
              <div className="absolute bottom-2 left-0 right-0 text-center text-white text-sm bg-black bg-opacity-50 py-1">
                Position barcode in the center
              </div>
            </>
          )}

          {!scanning && !error && (
            <div className="flex items-center justify-center h-full bg-gray-900 text-white p-4">
              <div className="text-center">
                <div className="text-xl mb-2">Processing...</div>
              </div>
            </div>
          )}
        </div>

        {/* Error message */}
        {error && (
          <div className="p-4">
            <div className="bg-red-50 text-red-700 p-3 rounded-md mb-4">
              <p>{error}</p>
              <button
                onClick={resetScanner}
                className="mt-2 flex items-center gap-1 text-blue-600 hover:text-blue-800"
              >
                <RefreshCw size={14} />
                Try Again
              </button>
            </div>
          </div>
        )}

        <div className="p-4 border-t border-gray-200">
          <p className="text-sm text-gray-500 mb-2">
            Scan a product barcode to quickly find and add it to your cart.
          </p>
          <div className="flex space-x-2">
            <button
              onClick={resetScanner}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md flex items-center justify-center gap-1 hover:bg-blue-700"
            >
              <RefreshCw size={16} />
              Reset Scanner
            </button>
            <button
              onClick={onClose}
              className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
