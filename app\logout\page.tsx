"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { performLogout } from "@/utils/auth"

export default function LogoutPage() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function handleLogout() {
      const result = await performLogout()
      if (!result.success) {
        setError('Failed to log out. Please try again.')
      }
    }

    handleLogout()
  }, [])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-2xl font-bold text-center mb-4">Logging Out</h1>

        {error ? (
          <div className="text-red-600 mb-4 text-center">
            {error}
            <button
              onClick={() => window.location.href = '/'}
              className="block mx-auto mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Return to Home
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-gray-600">Logging you out...</p>
          </div>
        )}
      </div>
    </div>
  )
}
