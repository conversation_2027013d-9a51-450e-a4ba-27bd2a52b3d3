import { NextRequest, NextResponse } from 'next/server'
import { checkDatabaseHealth, fixDatabaseIssues } from '@/utils/db-health'
import { createClient } from '@/utils/supabase/client'

export async function GET(request: NextRequest) {
  try {
    const health = await checkDatabaseHealth()
    
    return NextResponse.json(health)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Unknown error checking database health' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const supabase = await createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Check if user is admin
    const { data: userData } = await supabase
      .from('users')
      .select('is_admin')
      .eq('id', user.id)
      .single()
    
    if (!userData?.is_admin) {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 403 }
      )
    }
    
    // Fix database issues
    const result = await fixDatabaseIssues()
    
    return NextResponse.json(result)
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || 'Unknown error fixing database issues' },
      { status: 500 }
    )
  }
}
