"use server"

import { createClient } from '@/utils/supabase/client'
import { Shop, UserRoleType } from './types'

// Get all shops that a user is a member of
export async function getUserShops(userId: string): Promise<Shop[]> {
  const supabase = await createClient()

  try {
    // Get all shops where the user has a role
    const { data: userRoles, error: rolesError } = await supabase
      .from('user_roles')
      .select(`
        shop_id,
        role_id,
        roles(name)
      `)
      .eq('user_id', userId)

    if (rolesError) {
      console.error('Error fetching user roles:', rolesError)
      return []
    }

    if (!userRoles || userRoles.length === 0) {
      return []
    }

    // Get the shop details for each shop the user is a member of
    const shopIds = userRoles.map(role => role.shop_id)
    const { data: shops, error: shopsError } = await supabase
      .from('shops')
      .select('*')
      .in('id', shopIds)

    if (shopsError) {
      console.error('Error fetching shops:', shopsError)
      return []
    }

    return shops || []
  } catch (error) {
    console.error('Exception in getUserShops:', error)
    return []
  }
}

// Get a single shop by ID
export async function getShopById(shopId: string): Promise<Shop | null> {
  const supabase = await createClient()

  try {
    const { data, error } = await supabase
      .from('shops')
      .select('*')
      .eq('id', shopId)
      .single()

    if (error) {
      console.error('Error fetching shop:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Exception in getShopById:', error)
    return null
  }
}

// Create a new shop
export async function createShop(shopData: Partial<Shop>, userId: string): Promise<Shop | null> {
  const supabase = await createClient()

  try {
    // First create the shop
    const { data: shop, error: shopError } = await supabase
      .from('shops')
      .insert({
        name: shopData.name,
        address: shopData.address,
        owner_user_id: userId
      })
      .select()
      .single()

    if (shopError) {
      console.error('Error creating shop:', shopError)
      return null
    }

    // Then add the user as an Owner of the shop
    const { error: roleError } = await supabase
      .from('user_roles')
      .insert({
        user_id: userId,
        shop_id: shop.id,
        role_id: 1 // Assuming 1 is the ID for the Owner role
      })

    if (roleError) {
      console.error('Error assigning owner role:', roleError)
      // Consider deleting the shop if role assignment fails
      return null
    }

    return shop
  } catch (error) {
    console.error('Exception in createShop:', error)
    return null
  }
}

// Update a shop
export async function updateShop(shopId: string, shopData: Partial<Shop>): Promise<Shop | null> {
  const supabase = await createClient()

  try {
    const { data, error } = await supabase
      .from('shops')
      .update({
        name: shopData.name,
        address: shopData.address
      })
      .eq('id', shopId)
      .select()
      .single()

    if (error) {
      console.error('Error updating shop:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Exception in updateShop:', error)
    return null
  }
}

// Get user's role in a shop
export async function getUserRoleInShop(userId: string, shopId: string): Promise<UserRoleType | null> {
  const supabase = await createClient()

  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        role_id,
        roles(name)
      `)
      .eq('user_id', userId)
      .eq('shop_id', shopId)
      .single()

    if (error) {
      console.error('Error fetching user role:', error)
      return null
    }

    // Return the role name as UserRoleType
    return data.roles[0].name as UserRoleType
  } catch (error) {
    console.error('Exception in getUserRoleInShop:', error)
    return null
  }
}

// Get all members of a shop with their roles
export async function getShopMembers(shopId: string) {
  const supabase = await createClient()

  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        user_id,
        role_id,
        roles(name),
        users(id, mobile, full_name)
      `)
      .eq('shop_id', shopId)

    if (error) {
      console.error('Error fetching shop members:', error)
      return []
    }

    return data.map(item => ({
      user_id: item.user_id,
      role: item.roles[0].name,
      user: item.users[0]
    }))
  } catch (error) {
    console.error('Exception in getShopMembers:', error)
    return []
  }
}

// Add a user to a shop with a specific role
export async function addUserToShop(userId: string, shopId: string, roleId: number) {
  const supabase = await createClient()

  try {
    const { error } = await supabase
      .from('user_roles')
      .insert({
        user_id: userId,
        shop_id: shopId,
        role_id: roleId
      })

    if (error) {
      console.error('Error adding user to shop:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Exception in addUserToShop:', error)
    return false
  }
}

// Update a user's role in a shop
export async function updateUserRole(userId: string, shopId: string, roleId: number) {
  const supabase = await createClient()

  try {
    const { error } = await supabase
      .from('user_roles')
      .update({ role_id: roleId })
      .eq('user_id', userId)
      .eq('shop_id', shopId)

    if (error) {
      console.error('Error updating user role:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Exception in updateUserRole:', error)
    return false
  }
}

// Remove a user from a shop
export async function removeUserFromShop(userId: string, shopId: string) {
  const supabase = await createClient()

  try {
    const { error } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', userId)
      .eq('shop_id', shopId)

    if (error) {
      console.error('Error removing user from shop:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Exception in removeUserFromShop:', error)
    return false
  }
}

