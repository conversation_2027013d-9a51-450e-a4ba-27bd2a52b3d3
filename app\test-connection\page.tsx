"use client"

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/utils/supabase'
import { MainHeader } from '@/components/main-header'
import { MainFooter } from '@/components/main-footer'

export default function TestConnectionPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')
  const [responseTime, setResponseTime] = useState<number | null>(null)
  const [retryCount, setRetryCount] = useState(0)

  useEffect(() => {
    const testConnection = async () => {
      try {
        setStatus('loading')
        setMessage('Testing connection to Supabase...')

        const startTime = Date.now()

        // Create Supabase client with the updated timeout settings
        const supabase = createBrowserClient()

        // Test a simple query
        const { data, error } = await supabase
          .from('users')
          .select('count(*)', { count: 'exact', head: true })
          .limit(1)

        const endTime = Date.now()
        setResponseTime(endTime - startTime)

        if (error) {
          console.error('Error connecting to Supabase:', error)
          setStatus('error')
          setMessage(`Connection failed: ${error.message}`)
        } else {
          console.log('Connection successful:', data)
          setStatus('success')
          setMessage('Connection successful! Database is responding.')
        }
      } catch (err: any) {
        console.error('Exception testing connection:', err)
        setStatus('error')
        setMessage(`Exception: ${err.message || 'Unknown error'}`)
      }
    }

    testConnection()
  }, [retryCount])

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
  }

  return (
    <>
      <MainHeader />
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-4">Supabase Connection Test</h1>

          <div className="mb-6">
            <div className="flex items-center mb-2">
              <div className={`w-3 h-3 rounded-full mr-2 ${
                status === 'loading' ? 'bg-yellow-500' :
                status === 'success' ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="font-medium">
                {status === 'loading' ? 'Testing connection...' :
                 status === 'success' ? 'Connected' : 'Connection failed'}
              </span>
            </div>

            <p className="text-gray-700 text-sm">{message}</p>

            {responseTime !== null && (
              <p className="text-sm text-gray-500 mt-2">
                Response time: {responseTime}ms
              </p>
            )}
          </div>

          <div className="space-y-4">
            <div className="p-3 bg-gray-50 rounded border border-gray-200">
              <h2 className="font-medium mb-2">Environment Variables</h2>
              <p className="text-sm mb-1">
                <span className="font-medium">NEXT_PUBLIC_SUPABASE_URL:</span>{' '}
                {process.env.NEXT_PUBLIC_SUPABASE_URL ?
                  <span className="text-green-600">✓ Set</span> :
                  <span className="text-red-600">✗ Not set</span>}
              </p>
              <p className="text-sm">
                <span className="font-medium">NEXT_PUBLIC_SUPABASE_ANON_KEY:</span>{' '}
                {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ?
                  <span className="text-green-600">✓ Set</span> :
                  <span className="text-red-600">✗ Not set</span>}
              </p>
            </div>

            <button
              onClick={handleRetry}
              className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors"
            >
              Retry Connection Test
            </button>

            <a
              href="/"
              className="block w-full py-2 px-4 text-center border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors"
            >
              Back to Home
            </a>
          </div>
        </div>
      </div>
      <MainFooter />
    </>
  )
}
