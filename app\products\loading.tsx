import { MainHeader } from "@/components/main-header-new"
import { MainFooter } from "@/components/main-footer"

export default function Loading() {
  return (
    <>
      <MainHeader />
      <div className="p-4 flex-grow">
        <div className="animate-pulse">
          {/* Search bar placeholder */}
          <div className="h-10 bg-gray-200 rounded-md mb-6"></div>

          {/* Category placeholders */}
          {[1, 2, 3].map((i) => (
            <div key={i} className="mb-6">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-8 h-8 bg-gray-200 rounded-md"></div>
                <div className="h-5 bg-gray-200 rounded w-32"></div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                {[1, 2, 3, 4].map((j) => (
                  <div key={j} className="bg-white p-3 rounded-md border border-gray-200">
                    <div className="w-full h-24 bg-gray-200 rounded-md mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div className="flex justify-between items-center mt-2">
                      <div className="h-5 bg-gray-200 rounded w-16"></div>
                      <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
      <MainFooter />
    </>
  )
}
